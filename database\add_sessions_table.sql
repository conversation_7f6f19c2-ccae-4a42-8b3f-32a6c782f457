-- Step 1: Create event_sessions table
CREATE TABLE IF NOT EXISTS `event_sessions` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `event_id` int(11) NOT NULL,
  `session_name` varchar(100) NOT NULL,
  `session_code` varchar(20) NOT NULL,
  `session_date` date DEFAULT NULL,
  `session_time` time DEFAULT NULL,
  `venue` varchar(200) DEFAULT NULL,
  `max_capacity` int(11) DEFAULT NULL,
  `description` text,
  `is_active` tinyint(1) NOT NULL DEFAULT 1,
  `created_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP,
  `updated_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  PRIMARY KEY (`id`),
  UNIQUE KEY `unique_event_session_code` (`event_id`, `session_code`),
  KEY `idx_event_id` (`event_id`),
  KEY `idx_session_code` (`session_code`),
  <PERSON><PERSON>Y `idx_is_active` (`is_active`),
  CONSTRAINT `fk_event_sessions_event` FOREIGN KEY (`event_id`) REFERENCES `events` (`id`) ON DELETE CASCADE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci

-- Step 2: Add session_id column to students table
ALTER TABLE `students` ADD COLUMN `session_id` int(11) DEFAULT NULL AFTER `event_id`

-- Step 3: Add index for session_id
ALTER TABLE `students` ADD KEY `fk_student_session` (`session_id`)

-- Step 4: Add foreign key constraint
ALTER TABLE `students` ADD CONSTRAINT `fk_student_session` FOREIGN KEY (`session_id`) REFERENCES `event_sessions` (`id`) ON DELETE SET NULL

-- Step 5: Insert sample sessions for existing events
INSERT IGNORE INTO `event_sessions` (`event_id`, `session_name`, `session_code`, `session_date`, `session_time`, `venue`, `max_capacity`, `description`) VALUES
(1, 'Morning Session - Faculty of Engineering', 'CONV2024_ENG_AM', '2024-12-15', '09:00:00', 'Main Auditorium', 200, 'Engineering faculty convocation - morning session'),
(1, 'Afternoon Session - Faculty of Science', 'CONV2024_SCI_PM', '2024-12-15', '14:00:00', 'Main Auditorium', 200, 'Science faculty convocation - afternoon session'),
(1, 'Evening Session - Faculty of Arts', 'CONV2024_ART_EV', '2024-12-15', '18:00:00', 'Main Auditorium', 200, 'Arts faculty convocation - evening session'),
(2, 'Undergraduate Ceremony', 'GRAD2024_UG', '2024-11-30', '10:00:00', 'University Hall', 300, 'Undergraduate graduation ceremony'),
(2, 'Postgraduate Ceremony', 'GRAD2024_PG', '2024-11-30', '15:00:00', 'University Hall', 150, 'Postgraduate graduation ceremony'),
(3, 'New Students - Batch A', 'ORIENT2024_A', '2024-02-15', '09:00:00', 'Conference Hall', 100, 'Orientation for new students - Batch A'),
(3, 'New Students - Batch B', 'ORIENT2024_B', '2024-02-15', '14:00:00', 'Conference Hall', 100, 'Orientation for new students - Batch B')
