<?php
/**
 * Add Sample Image Column Migration
 * Run this once to add sample_image column to photo_types table
 */

// Security check
$allowed_ips = ['127.0.0.1', '::1', 'localhost'];
if (!in_array($_SERVER['REMOTE_ADDR'], $allowed_ips) && !in_array($_SERVER['HTTP_HOST'], ['localhost', '127.0.0.1'])) {
    die('Access denied. Migration can only be run from localhost.');
}

// Include configuration
require_once '../config/database.php';

$migration_log = [];
$errors = [];

function log_message($message, $type = 'info') {
    global $migration_log;
    $migration_log[] = [
        'time' => date('Y-m-d H:i:s'),
        'type' => $type,
        'message' => $message
    ];
    
    if ($type === 'error') {
        global $errors;
        $errors[] = $message;
    }
}

// Start migration process
if ($_SERVER['REQUEST_METHOD'] === 'POST' && isset($_POST['run_migration'])) {
    log_message("Starting sample image column migration...", 'info');
    
    try {
        $pdo = get_pdo_connection();
        log_message("Database connection established", 'success');
        
        // Check if column already exists
        log_message("Checking if sample_image column exists...", 'info');
        $stmt = $pdo->prepare("
            SELECT COUNT(*) FROM INFORMATION_SCHEMA.COLUMNS 
            WHERE table_name = 'photo_types' 
            AND table_schema = DATABASE() 
            AND column_name = 'sample_image'
        ");
        $stmt->execute();
        $column_exists = $stmt->fetchColumn() > 0;
        
        if ($column_exists) {
            log_message("⚠️ sample_image column already exists", 'warning');
        } else {
            // Add sample_image column
            log_message("Adding sample_image column...", 'info');
            $pdo->exec("ALTER TABLE `photo_types` ADD COLUMN `sample_image` VARCHAR(255) NULL AFTER `description`");
            log_message("✅ sample_image column added successfully", 'success');
        }
        
        // Check if updated_at column exists
        log_message("Checking if updated_at column exists...", 'info');
        $stmt = $pdo->prepare("
            SELECT COUNT(*) FROM INFORMATION_SCHEMA.COLUMNS 
            WHERE table_name = 'photo_types' 
            AND table_schema = DATABASE() 
            AND column_name = 'updated_at'
        ");
        $stmt->execute();
        $updated_at_exists = $stmt->fetchColumn() > 0;
        
        if (!$updated_at_exists) {
            log_message("Adding updated_at column...", 'info');
            $pdo->exec("ALTER TABLE `photo_types` ADD COLUMN `updated_at` TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP AFTER `created_at`");
            log_message("✅ updated_at column added successfully", 'success');
            
            // Update existing records
            $pdo->exec("UPDATE `photo_types` SET `updated_at` = `created_at` WHERE `updated_at` IS NULL");
            log_message("✅ Existing records updated with timestamps", 'success');
        } else {
            log_message("⚠️ updated_at column already exists", 'warning');
        }
        
        // Add index if it doesn't exist
        log_message("Adding index for sample_image...", 'info');
        try {
            $pdo->exec("ALTER TABLE `photo_types` ADD INDEX `idx_sample_image` (`sample_image`)");
            log_message("✅ Index added successfully", 'success');
        } catch (PDOException $e) {
            if (strpos($e->getMessage(), 'Duplicate key name') !== false) {
                log_message("⚠️ Index already exists", 'warning');
            } else {
                throw $e;
            }
        }
        
        // Verify migration
        log_message("Verifying migration...", 'info');
        $stmt = $pdo->query("DESCRIBE photo_types");
        $columns = $stmt->fetchAll(PDO::FETCH_ASSOC);
        
        $has_sample_image = false;
        $has_updated_at = false;
        
        foreach ($columns as $column) {
            if ($column['Field'] === 'sample_image') {
                $has_sample_image = true;
            }
            if ($column['Field'] === 'updated_at') {
                $has_updated_at = true;
            }
        }
        
        if ($has_sample_image && $has_updated_at) {
            log_message("🎉 Migration completed successfully!", 'success');
            log_message("📊 Photo types table now supports sample images", 'info');
        } else {
            log_message("❌ Migration verification failed", 'error');
        }
        
    } catch (Exception $e) {
        log_message("Migration failed: " . $e->getMessage(), 'error');
    }
}
?>
<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Sample Image Migration - Photo Management System</title>
    <link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/bulma@0.9.4/css/bulma.min.css">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css">
    <style>
        .migration-header {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            padding: 2rem;
            border-radius: 8px;
            margin-bottom: 2rem;
        }
        .log-entry {
            padding: 0.5rem;
            margin-bottom: 0.25rem;
            border-radius: 4px;
            font-family: monospace;
            font-size: 0.875rem;
        }
        .log-info { background-color: #f0f8ff; }
        .log-success { background-color: #f0fff0; color: #006400; }
        .log-warning { background-color: #fff8dc; color: #b8860b; }
        .log-error { background-color: #ffe4e1; color: #dc143c; }
    </style>
</head>
<body>
    <div class="container mt-4">
        <!-- Header -->
        <div class="migration-header">
            <h1 class="title is-2 has-text-white">
                <i class="fas fa-images mr-3"></i>
                Sample Image Migration
            </h1>
            <p class="subtitle is-4 has-text-white">
                Add sample image support to photo types
            </p>
        </div>

        <!-- Migration Status -->
        <?php if (!empty($migration_log)): ?>
            <div class="box">
                <h2 class="title is-5">
                    <i class="fas fa-list mr-2"></i>
                    Migration Log
                </h2>
                
                <?php if (empty($errors)): ?>
                    <div class="notification is-success">
                        <i class="fas fa-check-circle mr-2"></i>
                        Migration completed successfully!
                    </div>
                <?php else: ?>
                    <div class="notification is-danger">
                        <i class="fas fa-exclamation-circle mr-2"></i>
                        Migration completed with <?php echo count($errors); ?> error(s).
                    </div>
                <?php endif; ?>
                
                <div style="max-height: 400px; overflow-y: auto; border: 1px solid #ddd; padding: 1rem;">
                    <?php foreach ($migration_log as $entry): ?>
                        <div class="log-entry log-<?php echo $entry['type']; ?>">
                            <strong>[<?php echo $entry['time']; ?>]</strong> 
                            <?php echo htmlspecialchars($entry['message']); ?>
                        </div>
                    <?php endforeach; ?>
                </div>
            </div>
        <?php endif; ?>

        <!-- Migration Form -->
        <?php if (empty($migration_log) || !empty($errors)): ?>
            <div class="box">
                <h2 class="title is-5">
                    <i class="fas fa-database mr-2"></i>
                    Run Migration
                </h2>
                
                <div class="content">
                    <p>This migration will add sample image support to photo types:</p>
                    <ol>
                        <li>✅ Add <code>sample_image</code> column to store image paths</li>
                        <li>✅ Add <code>updated_at</code> timestamp column</li>
                        <li>✅ Add database index for performance</li>
                        <li>✅ Update existing records with proper timestamps</li>
                    </ol>
                    
                    <div class="notification is-info">
                        <strong>Safe to run:</strong> This migration checks for existing columns and won't break anything.
                    </div>
                </div>
                
                <form method="POST" action="">
                    <div class="field">
                        <div class="control">
                            <button class="button is-primary is-large" type="submit" name="run_migration">
                                <i class="fas fa-play mr-2"></i>
                                Run Migration
                            </button>
                        </div>
                    </div>
                </form>
            </div>
        <?php endif; ?>

        <!-- Success Actions -->
        <?php if (!empty($migration_log) && empty($errors)): ?>
            <div class="box">
                <h2 class="title is-5">
                    <i class="fas fa-check-circle mr-2"></i>
                    Migration Complete!
                </h2>
                
                <div class="content">
                    <p>Sample image support is now available! You can:</p>
                    <ul>
                        <li><strong>Upload Sample Images:</strong> Add sample photos for each photo type</li>
                        <li><strong>Manage Photo Types:</strong> Edit photo types through the admin panel</li>
                        <li><strong>View Enhanced Registration:</strong> Students will see sample photos</li>
                    </ul>
                </div>
                
                <div class="buttons">
                    <a href="../app/photo-pricing.php" class="button is-primary">
                        <i class="fas fa-tags mr-2"></i>
                        Manage Photo Types
                    </a>
                    <a href="../student/registration-step3.php" class="button is-info">
                        <i class="fas fa-eye mr-2"></i>
                        View Registration
                    </a>
                </div>
            </div>
        <?php endif; ?>

        <!-- Footer -->
        <div class="has-text-centered mt-6 mb-4">
            <p class="has-text-grey">
                <i class="fas fa-shield-alt mr-2"></i>
                Photo Management System - Sample Image Migration Tool
            </p>
        </div>
    </div>
</body>
</html>
