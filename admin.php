<?php
/**
 * Redirect file for old app URLs
 * This file redirects users from old app/ URLs to the new admin/ structure
 */

// Get the requested path
$requestUri = $_SERVER['REQUEST_URI'];
$scriptName = $_SERVER['SCRIPT_NAME'];

// Extract the path after the domain
$path = parse_url($requestUri, PHP_URL_PATH);

// Remove the script name from the path to get the relative path
$relativePath = str_replace(dirname($scriptName), '', $path);

// Clean up the path
$relativePath = ltrim($relativePath, '/');

// If someone tries to access app/something.php, redirect to admin/something.php
if (strpos($relativePath, 'app/') === 0) {
    $newPath = str_replace('app/', 'admin/', $relativePath);
    $newUrl = dirname($_SERVER['SCRIPT_NAME']) . '/' . $newPath;
    
    // Preserve query parameters
    $queryString = $_SERVER['QUERY_STRING'];
    if (!empty($queryString)) {
        $newUrl .= '?' . $queryString;
    }
    
    // Redirect with 301 (permanent redirect)
    header('HTTP/1.1 301 Moved Permanently');
    header('Location: ' . $newUrl);
    exit;
}

// If accessed directly, redirect to admin dashboard
header('Location: admin/dashboard.php');
exit;
?>
