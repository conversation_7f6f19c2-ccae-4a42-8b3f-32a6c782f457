<?php
require_once '../config/config.php';
require_admin();

$message = '';
$message_type = '';

// Handle report generation and export
if ($_SERVER['REQUEST_METHOD'] === 'POST') {
    if (!verify_csrf_token($_POST['csrf_token'] ?? '')) {
        $message = 'Invalid security token';
        $message_type = 'is-danger';
    } else {
        $report_type = sanitize_input($_POST['report_type'] ?? '');
        $date_from = sanitize_input($_POST['date_from'] ?? '');
        $date_to = sanitize_input($_POST['date_to'] ?? '');
        $format = sanitize_input($_POST['format'] ?? 'view');
        
        // Redirect to appropriate report handler
        $params = http_build_query([
            'type' => $report_type,
            'date_from' => $date_from,
            'date_to' => $date_to,
            'format' => $format
        ]);
        
        redirect("report-generator.php?{$params}");
    }
}

// Get services for statistics
$photoSessionService = new PhotoSessionService();
$operatorService = new OperatorService();
$studentService = new StudentService();

// Get summary statistics
$stats = [
    'total_registered' => $studentService->getTotalRegisteredCount(),
    'total_completed' => $photoSessionService->getCompletedSessionsCount(),
    'today_completed' => $photoSessionService->getTodaySessionsCount(),
    'total_operators' => $operatorService->getOperatorStatistics()['total_operators'] ?? 0,
    'active_operators' => $operatorService->getOperatorStatistics()['active_operators'] ?? 0
];

$stats['completion_rate'] = $stats['total_registered'] > 0 ? 
    round(($stats['total_completed'] / $stats['total_registered']) * 100, 1) : 0;

// Get recent activity
$recentSessions = $photoSessionService->getPhotoSessions([], 10, 0);
?>
<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Reports - <?php echo APP_NAME; ?></title>
    <link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/bulma@0.9.4/css/bulma.min.css">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css">
    <script src="https://cdn.jsdelivr.net/npm/chart.js"></script>
    <style>
        .dashboard-header {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
        }
        .navbar-item.has-text-white:hover {
            background-color: rgba(255,255,255,0.1);
            color: white !important;
        }
        .navbar-item.is-active {
            background-color: rgba(255,255,255,0.2);
            font-weight: bold;
        }
        .navbar-dropdown {
            background-color: white;
            border: 1px solid #dbdbdb;
            box-shadow: 0 8px 16px rgba(10,10,10,.1);
        }
        .navbar-dropdown .navbar-item {
            color: #363636 !important;
            padding: 0.75rem 1rem;
        }
        .navbar-dropdown .navbar-item:hover {
            background-color: #f5f5f5;
            color: #363636 !important;
        }
        .navbar-dropdown .navbar-divider {
            background-color: #dbdbdb;
        }
        .navbar-brand .navbar-item {
            color: white !important;
            font-weight: bold;
        }
        .navbar-item.has-dropdown .navbar-link {
            color: white !important;
        }
        .navbar-item.has-dropdown .navbar-link:hover {
            background-color: rgba(255,255,255,0.05) !important;
            color: white !important;
        }
        .navbar-item.has-dropdown.is-active .navbar-link,
        .navbar-item.has-dropdown:hover .navbar-link {
            background-color: rgba(255,255,255,0.05) !important;
            color: white !important;
        }
        .stat-card {
            border-left: 4px solid #667eea;
            transition: transform 0.2s;
        }
        .stat-card:hover {
            transform: translateY(-2px);
        }
        .stat-card.success {
            border-left-color: #48c78e;
        }
        .stat-card.warning {
            border-left-color: #ffdd57;
        }
        .stat-card.info {
            border-left-color: #3273dc;
        }
        .report-card {
            border-left: 4px solid #48c78e;
            transition: transform 0.2s;
        }
        .report-card:hover {
            transform: translateY(-2px);
        }
        .chart-container {
            position: relative;
            height: 300px;
        }
    </style>
</head>
<body>
    <!-- Navigation -->
    <nav class="navbar dashboard-header" role="navigation">
        <div class="navbar-brand">
            <div class="navbar-item">
                <i class="fas fa-shield-alt mr-2"></i>
                <strong>Admin Dashboard</strong>
            </div>
        </div>
        
        <div class="navbar-menu">
            <div class="navbar-start">
                <a class="navbar-item has-text-white" href="dashboard.php">
                    <i class="fas fa-tachometer-alt mr-2"></i>
                    Dashboard
                </a>
                <a class="navbar-item has-text-white" href="operators.php">
                    <i class="fas fa-users mr-2"></i>
                    Operators
                </a>
                <a class="navbar-item has-text-white" href="sessions.php">
                    <i class="fas fa-camera mr-2"></i>
                    Photo Sessions
                </a>
                <a class="navbar-item has-text-white is-active" href="reports.php">
                    <i class="fas fa-chart-bar mr-2"></i>
                    Reports
                </a>
            </div>
            
            <div class="navbar-end">
                <div class="navbar-item has-dropdown is-hoverable">
                    <a class="navbar-link has-text-white">
                        <i class="fas fa-user-shield mr-2"></i>
                        <?php echo htmlspecialchars($_SESSION['full_name']); ?>
                    </a>
                    <div class="navbar-dropdown">
                        <a class="navbar-item" href="profile.php">
                            <i class="fas fa-user-cog mr-2"></i>
                            Profile
                        </a>
                        <a class="navbar-item" href="settings.php">
                            <i class="fas fa-cog mr-2"></i>
                            Settings
                        </a>
                        <hr class="navbar-divider">
                        <a class="navbar-item" href="../logout.php">
                            <i class="fas fa-sign-out-alt mr-2"></i>
                            Logout
                        </a>
                    </div>
                </div>
            </div>
        </div>
    </nav>

    <div class="container mt-4">
        <!-- Header -->
        <div class="box">
            <h1 class="title is-4">
                <i class="fas fa-chart-bar mr-2"></i>
                System Reports
            </h1>
            <p class="subtitle is-6">Generate comprehensive reports and analytics</p>
        </div>

        <!-- Messages -->
        <?php if ($message): ?>
            <div class="notification <?php echo $message_type; ?>">
                <button class="delete" onclick="this.parentElement.style.display='none'"></button>
                <?php echo htmlspecialchars($message); ?>
            </div>
        <?php endif; ?>

        <!-- Summary Statistics -->
        <div class="columns">
            <div class="column">
                <div class="box stat-card">
                    <div class="level">
                        <div class="level-left">
                            <div>
                                <p class="heading">Total Registered</p>
                                <p class="title is-3"><?php echo number_format($stats['total_registered']); ?></p>
                            </div>
                        </div>
                        <div class="level-right">
                            <i class="fas fa-users fa-2x has-text-info"></i>
                        </div>
                    </div>
                </div>
            </div>
            <div class="column">
                <div class="box stat-card success">
                    <div class="level">
                        <div class="level-left">
                            <div>
                                <p class="heading">Completed</p>
                                <p class="title is-3"><?php echo number_format($stats['total_completed']); ?></p>
                            </div>
                        </div>
                        <div class="level-right">
                            <i class="fas fa-check-circle fa-2x has-text-success"></i>
                        </div>
                    </div>
                </div>
            </div>
            <div class="column">
                <div class="box stat-card warning">
                    <div class="level">
                        <div class="level-left">
                            <div>
                                <p class="heading">Completion Rate</p>
                                <p class="title is-3"><?php echo $stats['completion_rate']; ?>%</p>
                            </div>
                        </div>
                        <div class="level-right">
                            <i class="fas fa-percentage fa-2x has-text-warning"></i>
                        </div>
                    </div>
                </div>
            </div>
            <div class="column">
                <div class="box stat-card info">
                    <div class="level">
                        <div class="level-left">
                            <div>
                                <p class="heading">Today's Sessions</p>
                                <p class="title is-3"><?php echo number_format($stats['today_completed']); ?></p>
                            </div>
                        </div>
                        <div class="level-right">
                            <i class="fas fa-calendar-day fa-2x has-text-primary"></i>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- Report Generation -->
        <div class="columns">
            <div class="column is-8">
                <div class="box report-card">
                    <h2 class="title is-5">
                        <i class="fas fa-file-alt mr-2"></i>
                        Generate Reports
                    </h2>
                    
                    <form method="POST" action="">
                        <input type="hidden" name="csrf_token" value="<?php echo generate_csrf_token(); ?>">
                        
                        <div class="columns">
                            <div class="column is-6">
                                <div class="field">
                                    <label class="label">Report Type</label>
                                    <div class="control">
                                        <div class="select is-fullwidth">
                                            <select name="report_type" required>
                                                <option value="">Select Report Type</option>
                                                <option value="photo_sessions">Photo Sessions Report</option>
                                                <option value="operator_performance">Operator Performance</option>
                                                <option value="daily_summary">Daily Summary</option>
                                                <option value="completion_status">Completion Status</option>
                                                <option value="session_analytics">Session Analytics</option>
                                            </select>
                                        </div>
                                    </div>
                                </div>
                            </div>
                            <div class="column is-6">
                                <div class="field">
                                    <label class="label">Export Format</label>
                                    <div class="control">
                                        <div class="select is-fullwidth">
                                            <select name="format">
                                                <option value="view">View Online</option>
                                                <option value="csv">Download CSV</option>
                                                <option value="pdf">Download PDF</option>
                                            </select>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                        
                        <div class="columns">
                            <div class="column is-6">
                                <div class="field">
                                    <label class="label">From Date</label>
                                    <div class="control">
                                        <input class="input" type="date" name="date_from" 
                                               value="<?php echo date('Y-m-01'); ?>">
                                    </div>
                                </div>
                            </div>
                            <div class="column is-6">
                                <div class="field">
                                    <label class="label">To Date</label>
                                    <div class="control">
                                        <input class="input" type="date" name="date_to" 
                                               value="<?php echo date('Y-m-d'); ?>">
                                    </div>
                                </div>
                            </div>
                        </div>
                        
                        <div class="field">
                            <div class="control">
                                <button class="button is-primary is-large" type="submit">
                                    <i class="fas fa-chart-line mr-2"></i>
                                    Generate Report
                                </button>
                            </div>
                        </div>
                    </form>
                </div>

                <!-- Quick Reports -->
                <div class="box">
                    <h2 class="title is-5">
                        <i class="fas fa-bolt mr-2"></i>
                        Quick Reports
                    </h2>
                    <div class="buttons">
                        <a href="report-generator.php?type=today_summary&format=view" class="button is-info">
                            <i class="fas fa-calendar-day mr-2"></i>
                            Today's Summary
                        </a>
                        <a href="report-generator.php?type=weekly_summary&format=view" class="button is-success">
                            <i class="fas fa-calendar-week mr-2"></i>
                            This Week
                        </a>
                        <a href="report-generator.php?type=monthly_summary&format=view" class="button is-warning">
                            <i class="fas fa-calendar-alt mr-2"></i>
                            This Month
                        </a>
                        <a href="sessions.php?export=csv" class="button is-light">
                            <i class="fas fa-download mr-2"></i>
                            Export All Sessions
                        </a>
                    </div>
                </div>
            </div>
            
            <!-- Recent Activity -->
            <div class="column is-4">
                <div class="box">
                    <h2 class="title is-5">
                        <i class="fas fa-clock mr-2"></i>
                        Recent Activity
                    </h2>
                    <div style="max-height: 400px; overflow-y: auto;">
                        <?php if (empty($recentSessions)): ?>
                            <div class="has-text-centered py-4">
                                <i class="fas fa-inbox fa-2x has-text-grey-light mb-3"></i>
                                <p class="has-text-grey">No recent sessions</p>
                            </div>
                        <?php else: ?>
                            <?php foreach ($recentSessions as $session): ?>
                                <div class="box is-small mb-2">
                                    <div class="is-size-7">
                                        <strong><?php echo htmlspecialchars($session['student_name']); ?></strong><br>
                                        <span class="has-text-grey">
                                            <?php echo htmlspecialchars($session['invoice_number']); ?> | 
                                            Seq: <?php echo htmlspecialchars($session['photo_sequence_number']); ?>
                                        </span><br>
                                        <span class="has-text-grey is-size-7">
                                            <?php echo htmlspecialchars($session['operator_name']); ?> - 
                                            <?php echo date('M j, H:i', strtotime($session['completed_at'])); ?>
                                        </span>
                                    </div>
                                </div>
                            <?php endforeach; ?>
                        <?php endif; ?>
                    </div>
                </div>

                <!-- System Health -->
                <div class="box">
                    <h2 class="title is-6">
                        <i class="fas fa-heartbeat mr-2"></i>
                        System Health
                    </h2>
                    <div class="content is-small">
                        <div class="level is-mobile">
                            <div class="level-left">
                                <span>Active Operators</span>
                            </div>
                            <div class="level-right">
                                <span class="tag is-success"><?php echo $stats['active_operators']; ?>/<?php echo $stats['total_operators']; ?></span>
                            </div>
                        </div>
                        <div class="level is-mobile">
                            <div class="level-left">
                                <span>Database Status</span>
                            </div>
                            <div class="level-right">
                                <span class="tag is-success">Online</span>
                            </div>
                        </div>
                        <div class="level is-mobile">
                            <div class="level-left">
                                <span>Last Backup</span>
                            </div>
                            <div class="level-right">
                                <span class="tag is-light">Manual</span>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <script>
        // Auto-hide notifications after 5 seconds
        document.addEventListener('DOMContentLoaded', function() {
            const notifications = document.querySelectorAll('.notification');
            notifications.forEach(function(notification) {
                setTimeout(function() {
                    notification.style.display = 'none';
                }, 5000);
            });
        });
    </script>
</body>
</html>
