<?php
require_once '../config/config.php';
require_login();

$invoice_number = sanitize_input($_GET['invoice'] ?? '');
$student_data = null;
$error_message = '';
$success_message = '';

if (empty($invoice_number)) {
    $error_message = 'No invoice number provided';
} else {
    $studentService = new StudentService();
    $result = $studentService->getStudentByInvoice($invoice_number);
    
    if ($result['success']) {
        $student_data = $result['data'];
        
        if ($student_data['session_completed']) {
            $error_message = 'Photo session already completed for this student';
        }
    } else {
        $error_message = $result['message'];
    }
}

// Handle form submission
if ($_SERVER['REQUEST_METHOD'] === 'POST' && $student_data && !$student_data['session_completed']) {
    if (!verify_csrf_token($_POST['csrf_token'] ?? '')) {
        $error_message = 'Invalid security token';
    } else {
        $photo_sequence = sanitize_input($_POST['photo_sequence'] ?? '');
        $notes = sanitize_input($_POST['notes'] ?? '');
        
        if (empty($photo_sequence)) {
            $error_message = 'Photo sequence number is required';
        } elseif (!is_numeric($photo_sequence)) {
            $error_message = 'Photo sequence number must be numeric';
        } else {
            $photoSessionService = new PhotoSessionService();
            $session_data = [
                'invoice_number' => $student_data['invoice_number'],
                'student_nic' => $student_data['nic_number'],
                'student_name' => $student_data['full_name'],
                'student_address' => $student_data['address'],
                'whatsapp_number' => $student_data['whatsapp_number'],
                'alternate_phone' => $student_data['alternate_phone'],
                'session' => $student_data['session'],
                'seat_number' => $student_data['seat_number'],
                'photo_types' => $student_data['photo_types'],
                'photo_sequence_number' => $photo_sequence,
                'operator_id' => $_SESSION['user_id'],
                'notes' => $notes
            ];
            
            $result = $photoSessionService->recordPhotoSession($session_data);
            
            if ($result['success']) {
                $success_message = 'Photo session recorded successfully!';
                $student_data['session_completed'] = true;
            } else {
                $error_message = $result['message'];
            }
        }
    }
}
?>
<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Student Lookup - <?php echo APP_NAME; ?></title>
    <link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/bulma@0.9.4/css/bulma.min.css">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css">
    <style>
        .student-card {
            border-left: 4px solid #667eea;
        }
        .photo-type-tag {
            margin: 2px;
        }
        .back-button {
            position: fixed;
            top: 20px;
            left: 20px;
            z-index: 1000;
        }
        .sequence-input {
            font-size: 1.5rem;
            text-align: center;
            font-weight: bold;
        }
        .completed-overlay {
            background: rgba(72, 199, 142, 0.1);
            border: 2px solid #48c78e;
        }
    </style>
</head>
<body>
    <!-- Back Button -->
    <a href="dashboard.php" class="button back-button">
        <i class="fas fa-arrow-left mr-2"></i>
        Back
    </a>

    <div class="container mt-6">
        <div class="columns is-centered">
            <div class="column is-8-desktop is-10-tablet">
                
                <!-- Header -->
                <div class="box">
                    <h1 class="title is-4">
                        <i class="fas fa-search mr-2"></i>
                        Student Information
                    </h1>
                    <p class="subtitle is-6">
                        Invoice: <strong><?php echo htmlspecialchars($invoice_number); ?></strong>
                    </p>
                </div>

                <!-- Messages -->
                <?php if ($error_message): ?>
                    <div class="notification is-danger">
                        <i class="fas fa-exclamation-triangle mr-2"></i>
                        <?php echo htmlspecialchars($error_message); ?>
                        <div class="mt-3">
                            <a href="qr-scanner.php" class="button is-light">
                                <i class="fas fa-qrcode mr-2"></i>
                                Scan Another QR Code
                            </a>
                        </div>
                    </div>
                <?php endif; ?>

                <?php if ($success_message): ?>
                    <div class="notification is-success">
                        <i class="fas fa-check-circle mr-2"></i>
                        <?php echo htmlspecialchars($success_message); ?>
                        <div class="mt-3">
                            <a href="dashboard.php" class="button is-light mr-2">
                                <i class="fas fa-home mr-2"></i>
                                Dashboard
                            </a>
                            <a href="qr-scanner.php" class="button is-light">
                                <i class="fas fa-qrcode mr-2"></i>
                                Scan Next QR Code
                            </a>
                        </div>
                    </div>
                <?php endif; ?>

                <!-- Student Information -->
                <?php if ($student_data): ?>
                    <div class="box student-card <?php echo $student_data['session_completed'] ? 'completed-overlay' : ''; ?>">
                        <?php if ($student_data['session_completed']): ?>
                            <div class="notification is-success is-light">
                                <i class="fas fa-check-circle mr-2"></i>
                                <strong>Photo session already completed for this student</strong>
                            </div>
                        <?php endif; ?>

                        <div class="columns">
                            <div class="column is-6">
                                <h2 class="title is-5">
                                    <i class="fas fa-user mr-2"></i>
                                    <?php echo htmlspecialchars($student_data['full_name']); ?>
                                </h2>
                                
                                <div class="content">
                                    <p><strong>NIC Number:</strong> <?php echo htmlspecialchars($student_data['nic_number']); ?></p>
                                    <p><strong>Address:</strong> <?php echo htmlspecialchars($student_data['address']); ?></p>
                                    <p><strong>WhatsApp:</strong> 
                                        <?php if ($student_data['whatsapp_number']): ?>
                                            <a href="tel:<?php echo htmlspecialchars($student_data['whatsapp_number']); ?>">
                                                <?php echo htmlspecialchars($student_data['whatsapp_number']); ?>
                                            </a>
                                        <?php else: ?>
                                            <span class="has-text-grey">Not provided</span>
                                        <?php endif; ?>
                                    </p>
                                    <p><strong>Alternate Phone:</strong> 
                                        <?php if ($student_data['alternate_phone']): ?>
                                            <a href="tel:<?php echo htmlspecialchars($student_data['alternate_phone']); ?>">
                                                <?php echo htmlspecialchars($student_data['alternate_phone']); ?>
                                            </a>
                                        <?php else: ?>
                                            <span class="has-text-grey">Not provided</span>
                                        <?php endif; ?>
                                    </p>
                                </div>
                            </div>
                            
                            <div class="column is-6">
                                <h3 class="title is-6">Session Details</h3>
                                <div class="content">
                                    <p><strong>Session:</strong> <?php echo htmlspecialchars($student_data['session_name'] ?? $student_data['session']); ?></p>
                                    <p><strong>Seat Number:</strong> <?php echo htmlspecialchars($student_data['seat_number']); ?></p>
                                    <?php if (isset($student_data['session_date'])): ?>
                                        <p><strong>Date:</strong> <?php echo date('F j, Y', strtotime($student_data['session_date'])); ?></p>
                                        <p><strong>Time:</strong> <?php echo date('g:i A', strtotime($student_data['session_time'])); ?></p>
                                    <?php endif; ?>
                                    <?php if (isset($student_data['venue'])): ?>
                                        <p><strong>Venue:</strong> <?php echo htmlspecialchars($student_data['venue']); ?></p>
                                    <?php endif; ?>
                                </div>
                            </div>
                        </div>

                        <!-- Photo Types -->
                        <div class="mt-4">
                            <h3 class="title is-6">Selected Photo Types</h3>
                            <div class="tags">
                                <?php if (!empty($student_data['photo_type_details'])): ?>
                                    <?php foreach ($student_data['photo_type_details'] as $photo_type): ?>
                                        <span class="tag is-info is-medium photo-type-tag">
                                            <i class="fas fa-camera mr-2"></i>
                                            <?php echo htmlspecialchars($photo_type['type_name']); ?>
                                            <span class="ml-2 has-text-weight-bold">
                                                Rs. <?php echo number_format($photo_type['price'], 2); ?>
                                            </span>
                                        </span>
                                    <?php endforeach; ?>
                                <?php else: ?>
                                    <?php foreach ($student_data['photo_types'] as $type_code): ?>
                                        <span class="tag is-light is-medium photo-type-tag">
                                            <?php echo htmlspecialchars($type_code); ?>
                                        </span>
                                    <?php endforeach; ?>
                                <?php endif; ?>
                            </div>
                        </div>
                    </div>

                    <!-- Photo Sequence Form -->
                    <?php if (!$student_data['session_completed'] && !$success_message): ?>
                        <div class="box">
                            <h2 class="title is-5">
                                <i class="fas fa-camera mr-2"></i>
                                Record Photo Session
                            </h2>
                            
                            <form method="POST" action="">
                                <input type="hidden" name="csrf_token" value="<?php echo generate_csrf_token(); ?>">
                                
                                <div class="field">
                                    <label class="label">Photo Sequence Number *</label>
                                    <div class="control">
                                        <input class="input is-large sequence-input" 
                                               type="number" 
                                               name="photo_sequence" 
                                               placeholder="Enter sequence number"
                                               min="1"
                                               required
                                               autofocus>
                                    </div>
                                    <p class="help">Enter the numeric sequence number assigned by the photographer</p>
                                </div>
                                
                                <div class="field">
                                    <label class="label">Notes (Optional)</label>
                                    <div class="control">
                                        <textarea class="textarea" 
                                                  name="notes" 
                                                  placeholder="Any additional notes about the photo session"
                                                  rows="3"></textarea>
                                    </div>
                                </div>
                                
                                <div class="field is-grouped">
                                    <div class="control">
                                        <button class="button is-primary is-large" type="submit">
                                            <i class="fas fa-save mr-2"></i>
                                            Record Photo Session
                                        </button>
                                    </div>
                                    <div class="control">
                                        <a href="dashboard.php" class="button is-light is-large">
                                            <i class="fas fa-times mr-2"></i>
                                            Cancel
                                        </a>
                                    </div>
                                </div>
                            </form>
                        </div>
                    <?php endif; ?>
                <?php endif; ?>
            </div>
        </div>
    </div>

    <script>
        // Auto-focus on sequence number input
        document.addEventListener('DOMContentLoaded', function() {
            const sequenceInput = document.querySelector('input[name="photo_sequence"]');
            if (sequenceInput) {
                sequenceInput.focus();
            }
        });
    </script>
</body>
</html>
