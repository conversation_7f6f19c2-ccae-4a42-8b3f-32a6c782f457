<?php
require_once '../config/config.php';
require_login();

$photoSessionService = new PhotoSessionService();
$todayCount = $photoSessionService->getTodaySessionsCount();
$myTodayCount = count($photoSessionService->getSessionsByOperator($_SESSION['user_id'], date('Y-m-d')));
?>
<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Operator Dashboard - <?php echo APP_NAME; ?></title>
    <link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/bulma@0.9.4/css/bulma.min.css">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css">
    <style>
        .dashboard-header {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
        }
        .navbar-brand .navbar-item {
            color: white !important;
            font-weight: bold;
        }
        .navbar-item.has-dropdown .navbar-link {
            color: white !important;
        }
        .navbar-item.has-dropdown .navbar-link:hover {
            background-color: rgba(255,255,255,0.05) !important;
            color: white !important;
        }
        .navbar-item.has-dropdown.is-active .navbar-link,
        .navbar-item.has-dropdown:hover .navbar-link {
            background-color: rgba(255,255,255,0.05) !important;
            color: white !important;
        }
        .navbar-dropdown {
            background-color: white;
            border: 1px solid #dbdbdb;
            box-shadow: 0 8px 16px rgba(10,10,10,.1);
        }
        .navbar-dropdown .navbar-item {
            color: #363636 !important;
            padding: 0.75rem 1rem;
        }
        .navbar-dropdown .navbar-item:hover {
            background-color: #f5f5f5;
            color: #363636 !important;
        }
        .navbar-dropdown .navbar-divider {
            background-color: #dbdbdb;
        }
        .stat-card {
            border-left: 4px solid #667eea;
            transition: transform 0.2s;
        }
        .stat-card:hover {
            transform: translateY(-2px);
        }
        .qr-scanner-btn {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            border: none;
            color: white;
            font-size: 1.2rem;
            padding: 1.5rem;
            border-radius: 10px;
            transition: all 0.3s;
        }
        .qr-scanner-btn:hover {
            transform: scale(1.05);
            box-shadow: 0 10px 20px rgba(0,0,0,0.2);
        }
        .recent-sessions {
            max-height: 400px;
            overflow-y: auto;
        }
    </style>
</head>
<body>
    <!-- Navigation -->
    <nav class="navbar dashboard-header" role="navigation">
        <div class="navbar-brand">
            <div class="navbar-item">
                <i class="fas fa-camera mr-2"></i>
                <strong>Photo Session Management</strong>
            </div>
        </div>
        
        <div class="navbar-menu">
            <div class="navbar-end">
                <div class="navbar-item has-dropdown is-hoverable">
                    <a class="navbar-link has-text-white">
                        <i class="fas fa-user mr-2"></i>
                        <?php echo htmlspecialchars($_SESSION['full_name']); ?>
                    </a>
                    <div class="navbar-dropdown">
                        <a class="navbar-item" href="profile.php">
                            <i class="fas fa-user-cog mr-2"></i>
                            Profile
                        </a>
                        <hr class="navbar-divider">
                        <a class="navbar-item" href="../logout.php">
                            <i class="fas fa-sign-out-alt mr-2"></i>
                            Logout
                        </a>
                    </div>
                </div>
            </div>
        </div>
    </nav>

    <div class="container mt-4">
        <!-- Welcome Section -->
        <div class="box">
            <h1 class="title is-4">
                <i class="fas fa-tachometer-alt mr-2"></i>
                Welcome, <?php echo htmlspecialchars($_SESSION['full_name']); ?>!
            </h1>
            <p class="subtitle is-6">Ready to scan QR codes and manage photo sessions</p>
        </div>

        <!-- Statistics Cards -->
        <div class="columns">
            <div class="column">
                <div class="box stat-card">
                    <div class="level">
                        <div class="level-left">
                            <div>
                                <p class="heading">Total Today</p>
                                <p class="title is-3"><?php echo $todayCount; ?></p>
                            </div>
                        </div>
                        <div class="level-right">
                            <i class="fas fa-calendar-day fa-2x has-text-info"></i>
                        </div>
                    </div>
                </div>
            </div>
            <div class="column">
                <div class="box stat-card">
                    <div class="level">
                        <div class="level-left">
                            <div>
                                <p class="heading">My Sessions Today</p>
                                <p class="title is-3"><?php echo $myTodayCount; ?></p>
                            </div>
                        </div>
                        <div class="level-right">
                            <i class="fas fa-user-check fa-2x has-text-success"></i>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- Main Action Section -->
        <div class="columns">
            <div class="column is-8">
                <div class="box">
                    <h2 class="title is-5">
                        <i class="fas fa-qrcode mr-2"></i>
                        QR Code Scanner
                    </h2>
                    <div class="has-text-centered py-6">
                        <button class="button qr-scanner-btn is-large" onclick="startQRScanner()">
                            <span class="icon is-large">
                                <i class="fas fa-qrcode"></i>
                            </span>
                            <span>Scan QR Code</span>
                        </button>
                        <p class="mt-4 has-text-grey">
                            Click to start scanning student QR codes
                        </p>
                    </div>
                    
                    <!-- Manual Invoice Entry -->
                    <div class="box has-background-light">
                        <h3 class="title is-6">Manual Invoice Entry</h3>
                        <div class="field has-addons">
                            <div class="control is-expanded">
                                <input class="input" type="text" id="manual-invoice" placeholder="Enter invoice number manually">
                            </div>
                            <div class="control">
                                <button class="button is-info" onclick="lookupInvoice()">
                                    <i class="fas fa-search mr-2"></i>
                                    Lookup
                                </button>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
            
            <div class="column is-4">
                <div class="box">
                    <h2 class="title is-5">
                        <i class="fas fa-clock mr-2"></i>
                        Recent Sessions
                    </h2>
                    <div class="recent-sessions">
                        <?php
                        $recentSessions = $photoSessionService->getSessionsByOperator($_SESSION['user_id'], date('Y-m-d'));
                        if (empty($recentSessions)):
                        ?>
                            <div class="has-text-centered py-4">
                                <i class="fas fa-inbox fa-2x has-text-grey-light mb-3"></i>
                                <p class="has-text-grey">No sessions recorded today</p>
                            </div>
                        <?php else: ?>
                            <?php foreach (array_slice($recentSessions, 0, 5) as $session): ?>
                                <div class="box is-small mb-2">
                                    <div class="is-size-7">
                                        <strong><?php echo htmlspecialchars($session['student_name']); ?></strong><br>
                                        <span class="has-text-grey">
                                            <?php echo htmlspecialchars($session['invoice_number']); ?> | 
                                            Seq: <?php echo htmlspecialchars($session['photo_sequence_number']); ?>
                                        </span><br>
                                        <span class="has-text-grey is-size-7">
                                            <?php echo date('H:i', strtotime($session['completed_at'])); ?>
                                        </span>
                                    </div>
                                </div>
                            <?php endforeach; ?>
                            
                            <?php if (count($recentSessions) > 5): ?>
                                <div class="has-text-centered">
                                    <a href="sessions.php" class="button is-small is-text">
                                        View All Sessions
                                    </a>
                                </div>
                            <?php endif; ?>
                        <?php endif; ?>
                    </div>
                </div>
            </div>
        </div>

        <!-- Quick Actions -->
        <div class="box">
            <h2 class="title is-5">
                <i class="fas fa-bolt mr-2"></i>
                Quick Actions
            </h2>
            <div class="buttons">
                <a href="sessions.php" class="button is-info">
                    <i class="fas fa-list mr-2"></i>
                    View All Sessions
                </a>
                <a href="profile.php" class="button is-light">
                    <i class="fas fa-user-cog mr-2"></i>
                    Profile Settings
                </a>
            </div>
        </div>
    </div>

    <script>
        function startQRScanner() {
            // Redirect to QR scanner page
            window.location.href = 'qr-scanner.php';
        }
        
        function lookupInvoice() {
            const invoiceNumber = document.getElementById('manual-invoice').value.trim();
            if (!invoiceNumber) {
                alert('Please enter an invoice number');
                return;
            }
            
            // Redirect to student lookup page
            window.location.href = `student-lookup.php?invoice=${encodeURIComponent(invoiceNumber)}`;
        }
        
        // Allow Enter key to trigger lookup
        document.getElementById('manual-invoice').addEventListener('keypress', function(e) {
            if (e.key === 'Enter') {
                lookupInvoice();
            }
        });
    </script>
</body>
</html>
