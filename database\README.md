# Database Migration Guide

This directory contains the database migration scripts to upgrade your Photo Management System to the unified database structure that supports both operator management and student registration systems.

## 📋 Migration Overview

The migration will:
- Create a unified database structure
- Migrate existing operator and session data
- Add new tables for student registration system
- Insert default data (events, photo types, delivery methods)
- Set up proper relationships and indexes

## 🔧 Prerequisites

Before running the migration, ensure you have:

1. **PHP 7.4+** with required extensions:
   - PDO
   - PDO MySQL
   - mbstring
   - json

2. **MySQL 5.7+** or **MariaDB 10.2+**

3. **Database Access** with CREATE, ALTER, DROP privileges

4. **Backup** of your existing database (recommended)

## 🚀 Migration Methods

### Method 1: Web-based Migration (Recommended)

1. **Check Configuration:**
   ```
   http://localhost/your-project/database/check_config.php
   ```
   This will verify your system meets all requirements.

2. **Run Migration:**
   ```
   http://localhost/your-project/database/migrate.php
   ```
   Follow the web interface to complete the migration.

### Method 2: Command Line Migration

1. **Navigate to database directory:**
   ```bash
   cd /path/to/your/project/database/
   ```

2. **Run SQL script directly:**
   ```bash
   mysql -u username -p database_name < migrate_to_unified.sql
   ```

## 📊 Database Structure

### New Tables Created:

#### Student Registration System:
- `events` - Event management (Convocation, Graduation, etc.)
- `photo_types` - Photo type definitions
- `event_photo_pricing` - Event-specific pricing
- `delivery_methods` - Delivery options
- `students` - Student registrations
- `student_photo_orders` - Photo orders per student

#### Unified System:
- `operators` - Renamed from `users`, enhanced structure
- `photo_sessions` - Enhanced with student linking
- `session_logs` - Enhanced activity logging
- `system_settings` - Application configuration

### Default Data Inserted:

#### Events:
- University Convocation 2024
- Graduation Ceremony 2024
- Orientation Program 2024

#### Photo Types:
- Single Full Photo
- Single Bust Photo
- Stage Photo
- Family Photo
- Couple Photo
- Group Photo

#### Delivery Methods:
- Courier Service (LKR 500.00)
- Collection Center Pickup (Free)

#### Sample Pricing:
- Convocation 2024: LKR 1,200 - 3,500 per photo type
- Graduation 2024: LKR 1,100 - 3,200 per photo type
- Orientation 2024: LKR 1,000 - 2,800 per photo type

## 🔄 Migration Process

### Step 1: Backup
- Existing tables are automatically backed up with timestamp
- Format: `table_name_backup_YYYY_MM_DD_HH_MM_SS`

### Step 2: Structure Creation
- Creates all new tables with proper relationships
- Sets up foreign key constraints
- Creates performance indexes

### Step 3: Data Migration
- Migrates existing users to operators table
- Preserves all photo session data
- Maintains session logs

### Step 4: Default Data
- Inserts sample events and photo types
- Sets up delivery methods
- Creates default pricing structure

### Step 5: Verification
- Checks all tables were created
- Verifies data integrity
- Reports migration status

## ⚠️ Important Notes

### Security:
- Web migration scripts only work from localhost
- Database credentials must be properly configured
- Backup your data before migration

### Data Preservation:
- All existing operator accounts are preserved
- Photo session history is maintained
- Activity logs are retained
- Original tables are backed up, not deleted

### Post-Migration:
- Update your application configuration if needed
- Test all functionality thoroughly
- Set up photo pricing for your events
- Configure payment gateway settings

## 🛠️ Troubleshooting

### Common Issues:

1. **Permission Denied:**
   - Ensure database user has CREATE/ALTER privileges
   - Check file permissions on migration scripts

2. **Connection Failed:**
   - Verify database credentials in `config/database.php`
   - Ensure MySQL server is running

3. **Foreign Key Errors:**
   - Check for orphaned records in existing data
   - Ensure referential integrity

4. **Charset Issues:**
   - Migration uses UTF8MB4 for full Unicode support
   - Existing data is preserved with proper encoding

### Getting Help:

If you encounter issues:
1. Check the migration log for specific error messages
2. Verify your database configuration
3. Ensure all prerequisites are met
4. Review the backup tables if data recovery is needed

## 📁 File Structure

```
database/
├── README.md                 # This file
├── check_config.php         # Configuration checker
├── migrate.php              # Web-based migration interface
├── migrate_to_unified.sql   # SQL migration script
└── unified_database.sql     # Complete database schema
```

## ✅ Post-Migration Checklist

After successful migration:

- [ ] Verify admin login works
- [ ] Check operator accounts are accessible
- [ ] Test photo session recording
- [ ] Verify student registration portal
- [ ] Configure photo pricing for events
- [ ] Test status checking functionality
- [ ] Set up payment gateway (if needed)
- [ ] Update any custom configurations

## 🔗 Next Steps

1. **Admin Dashboard:** `http://localhost/your-project/admin/dashboard.php`
2. **Student Portal:** `http://localhost/your-project/student/index.php`
3. **Photo Pricing:** `http://localhost/your-project/admin/photo-pricing.php`

---

**Note:** This migration is designed to be safe and reversible. Your original data is backed up and preserved throughout the process.
