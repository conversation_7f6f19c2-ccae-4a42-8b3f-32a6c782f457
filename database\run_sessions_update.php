<?php
/**
 * Add Sessions Table Update
 * Run this once to add the sessions functionality
 */

// Security check
$allowed_ips = ['127.0.0.1', '::1', 'localhost'];
if (!in_array($_SERVER['REMOTE_ADDR'], $allowed_ips) && !in_array($_SERVER['HTTP_HOST'], ['localhost', '127.0.0.1'])) {
    die('Access denied. Update can only be run from localhost.');
}

// Include configuration
require_once '../config/database.php';

$update_log = [];
$errors = [];

function log_message($message, $type = 'info') {
    global $update_log;
    $update_log[] = [
        'time' => date('Y-m-d H:i:s'),
        'type' => $type,
        'message' => $message
    ];
    
    if ($type === 'error') {
        global $errors;
        $errors[] = $message;
    }
}

// Start update process
if ($_SERVER['REQUEST_METHOD'] === 'POST' && isset($_POST['run_update'])) {
    log_message("Starting sessions table update...", 'info');
    
    try {
        $pdo = DatabaseConfig::getUnifiedConnection();
        log_message("Database connection established", 'success');
        
        // Read and execute SQL file
        $sql_file = __DIR__ . '/add_sessions_table.sql';
        if (!file_exists($sql_file)) {
            log_message("SQL file not found: {$sql_file}", 'error');
        } else {
            $sql = file_get_contents($sql_file);
            $statements = array_filter(array_map('trim', explode(';', $sql)));
            
            $success_count = 0;
            $error_count = 0;
            
            foreach ($statements as $statement) {
                if (empty($statement) || strpos($statement, '--') === 0) {
                    continue;
                }
                
                try {
                    $pdo->exec($statement);
                    $success_count++;
                    
                    if (stripos($statement, 'CREATE TABLE') !== false) {
                        log_message("Created event_sessions table", 'success');
                    } elseif (stripos($statement, 'ALTER TABLE') !== false) {
                        log_message("Updated students table structure", 'success');
                    } elseif (stripos($statement, 'INSERT') !== false) {
                        log_message("Inserted sample session data", 'success');
                    }
                    
                } catch (PDOException $e) {
                    if (strpos($e->getMessage(), 'already exists') !== false || 
                        strpos($e->getMessage(), 'Duplicate') !== false) {
                        log_message("Skipped (already exists): " . substr($statement, 0, 50) . "...", 'warning');
                    } else {
                        $error_count++;
                        log_message("SQL Error: " . $e->getMessage(), 'error');
                    }
                }
            }
            
            log_message("Processed {$success_count} statements successfully, {$error_count} errors", 
                       $error_count > 0 ? 'error' : 'success');
            
            if ($error_count === 0) {
                log_message("Sessions table update completed successfully!", 'success');
            }
        }
        
    } catch (Exception $e) {
        log_message("Update failed: " . $e->getMessage(), 'error');
    }
}
?>
<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Sessions Table Update - Photo Management System</title>
    <link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/bulma@0.9.4/css/bulma.min.css">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css">
    <style>
        .update-header {
            background: linear-gradient(135deg, #48c78e 0%, #00d1b2 100%);
            color: white;
            padding: 2rem;
            border-radius: 8px;
            margin-bottom: 2rem;
        }
        .log-entry {
            padding: 0.5rem;
            margin-bottom: 0.25rem;
            border-radius: 4px;
            font-family: monospace;
            font-size: 0.875rem;
        }
        .log-info { background-color: #f0f8ff; }
        .log-success { background-color: #f0fff0; color: #006400; }
        .log-warning { background-color: #fff8dc; color: #b8860b; }
        .log-error { background-color: #ffe4e1; color: #dc143c; }
    </style>
</head>
<body>
    <div class="container mt-4">
        <!-- Header -->
        <div class="update-header">
            <h1 class="title is-2 has-text-white">
                <i class="fas fa-calendar-alt mr-3"></i>
                Sessions Table Update
            </h1>
            <p class="subtitle is-4 has-text-white">
                Add event sessions functionality to the system
            </p>
        </div>

        <!-- Update Status -->
        <?php if (!empty($update_log)): ?>
            <div class="box">
                <h2 class="title is-5">
                    <i class="fas fa-list mr-2"></i>
                    Update Log
                </h2>
                
                <?php if (empty($errors)): ?>
                    <div class="notification is-success">
                        <i class="fas fa-check-circle mr-2"></i>
                        Sessions table update completed successfully!
                    </div>
                <?php else: ?>
                    <div class="notification is-danger">
                        <i class="fas fa-exclamation-circle mr-2"></i>
                        Update completed with <?php echo count($errors); ?> error(s).
                    </div>
                <?php endif; ?>
                
                <div style="max-height: 400px; overflow-y: auto; border: 1px solid #ddd; padding: 1rem;">
                    <?php foreach ($update_log as $entry): ?>
                        <div class="log-entry log-<?php echo $entry['type']; ?>">
                            <strong>[<?php echo $entry['time']; ?>]</strong> 
                            <?php echo htmlspecialchars($entry['message']); ?>
                        </div>
                    <?php endforeach; ?>
                </div>
            </div>
        <?php endif; ?>

        <!-- Update Form -->
        <?php if (empty($update_log) || !empty($errors)): ?>
            <div class="box">
                <h2 class="title is-5">
                    <i class="fas fa-play mr-2"></i>
                    Run Sessions Update
                </h2>
                
                <div class="content">
                    <p>This update will add:</p>
                    <ul>
                        <li>✅ <strong>event_sessions</strong> table for managing sessions/classes per event</li>
                        <li>✅ <strong>session_id</strong> column to students table</li>
                        <li>✅ Sample session data for existing events</li>
                        <li>✅ Proper foreign key relationships</li>
                    </ul>
                    
                    <div class="notification is-info">
                        <strong>Note:</strong> This is safe to run multiple times. Existing data will not be affected.
                    </div>
                </div>
                
                <form method="POST" action="">
                    <div class="field">
                        <div class="control">
                            <label class="checkbox">
                                <input type="checkbox" required>
                                I understand this will modify the database structure
                            </label>
                        </div>
                    </div>
                    
                    <div class="field">
                        <div class="control">
                            <button class="button is-success is-large" type="submit" name="run_update">
                                <i class="fas fa-play mr-2"></i>
                                Run Sessions Update
                            </button>
                        </div>
                    </div>
                </form>
            </div>
        <?php endif; ?>

        <!-- Success Actions -->
        <?php if (!empty($update_log) && empty($errors)): ?>
            <div class="box">
                <h2 class="title is-5">
                    <i class="fas fa-check-circle mr-2"></i>
                    Update Complete!
                </h2>
                
                <div class="content">
                    <p>The sessions functionality has been successfully added to your system!</p>
                    
                    <h3>What's New:</h3>
                    <ul>
                        <li><strong>Event Sessions:</strong> Each event can now have multiple sessions/classes</li>
                        <li><strong>Session Management:</strong> Admins can manage sessions in the admin panel</li>
                        <li><strong>Student Assignment:</strong> Students can be assigned to specific sessions</li>
                        <li><strong>Sample Data:</strong> Sample sessions have been created for existing events</li>
                    </ul>
                </div>
                
                <div class="buttons">
                    <a href="../admin/dashboard.php" class="button is-primary">
                        <i class="fas fa-tachometer-alt mr-2"></i>
                        Admin Dashboard
                    </a>
                    <a href="../admin/events.php" class="button is-info">
                        <i class="fas fa-calendar-alt mr-2"></i>
                        Manage Events & Sessions
                    </a>
                    <button class="button is-light" onclick="location.reload()">
                        <i class="fas fa-refresh mr-2"></i>
                        Refresh Page
                    </button>
                </div>
            </div>
        <?php endif; ?>

        <!-- Footer -->
        <div class="has-text-centered mt-6 mb-4">
            <p class="has-text-grey">
                <i class="fas fa-shield-alt mr-2"></i>
                Photo Management System - Sessions Update Tool
            </p>
        </div>
    </div>
</body>
</html>
