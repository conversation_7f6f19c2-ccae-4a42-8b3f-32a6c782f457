<?php
// Authentication check - must be first
require_once 'auth-check.php';
require_admin();

$report_type = sanitize_input($_GET['type'] ?? '');
$date_from = sanitize_input($_GET['date_from'] ?? '');
$date_to = sanitize_input($_GET['date_to'] ?? '');
$format = sanitize_input($_GET['format'] ?? 'view');

// Set default dates if not provided
if (empty($date_from)) {
    switch ($report_type) {
        case 'today_summary':
            $date_from = date('Y-m-d');
            $date_to = date('Y-m-d');
            break;
        case 'weekly_summary':
            $date_from = date('Y-m-d', strtotime('monday this week'));
            $date_to = date('Y-m-d');
            break;
        case 'monthly_summary':
            $date_from = date('Y-m-01');
            $date_to = date('Y-m-d');
            break;
        default:
            $date_from = date('Y-m-01');
            $date_to = date('Y-m-d');
    }
}

// Initialize services
$photoSessionService = new PhotoSessionService();
$operatorService = new OperatorService();
$studentService = new StudentService();

// Generate report data based on type
$reportData = [];
$reportTitle = '';

switch ($report_type) {
    case 'photo_sessions':
        $reportTitle = 'Photo Sessions Report';
        $filters = [
            'date_from' => $date_from,
            'date_to' => $date_to
        ];
        $reportData['sessions'] = $photoSessionService->getPhotoSessions($filters, 1000, 0);
        $reportData['summary'] = [
            'total_sessions' => count($reportData['sessions']),
            'date_range' => $date_from . ' to ' . $date_to
        ];
        break;
        
    case 'operator_performance':
        $reportTitle = 'Operator Performance Report';
        $operators = $operatorService->getAllOperators();
        $reportData['operators'] = [];
        
        foreach ($operators as $operator) {
            $sessions = $photoSessionService->getSessionsByOperator($operator['id']);
            $todaySessions = $photoSessionService->getSessionsByOperator($operator['id'], date('Y-m-d'));
            
            $reportData['operators'][] = [
                'name' => $operator['full_name'],
                'username' => $operator['username'],
                'total_sessions' => count($sessions),
                'today_sessions' => count($todaySessions),
                'status' => $operator['is_active'] ? 'Active' : 'Inactive',
                'last_login' => $operator['last_login']
            ];
        }
        break;
        
    case 'daily_summary':
    case 'today_summary':
        $reportTitle = 'Daily Summary Report';
        $targetDate = $report_type === 'today_summary' ? date('Y-m-d') : $date_from;
        
        $filters = [
            'date_from' => $targetDate,
            'date_to' => $targetDate
        ];
        $sessions = $photoSessionService->getPhotoSessions($filters, 1000, 0);
        
        $reportData['summary'] = [
            'date' => $targetDate,
            'total_sessions' => count($sessions),
            'operators_active' => count(array_unique(array_column($sessions, 'operator_id'))),
            'sessions_by_hour' => []
        ];
        
        // Group by hour
        foreach ($sessions as $session) {
            $hour = date('H', strtotime($session['completed_at']));
            if (!isset($reportData['summary']['sessions_by_hour'][$hour])) {
                $reportData['summary']['sessions_by_hour'][$hour] = 0;
            }
            $reportData['summary']['sessions_by_hour'][$hour]++;
        }
        
        $reportData['sessions'] = $sessions;
        break;
        
    case 'completion_status':
        $reportTitle = 'Completion Status Report';
        $totalRegistered = $studentService->getTotalRegisteredCount();
        $totalCompleted = $photoSessionService->getCompletedSessionsCount();
        
        $reportData['summary'] = [
            'total_registered' => $totalRegistered,
            'total_completed' => $totalCompleted,
            'pending' => $totalRegistered - $totalCompleted,
            'completion_rate' => $totalRegistered > 0 ? round(($totalCompleted / $totalRegistered) * 100, 1) : 0
        ];
        break;
        
    case 'session_analytics':
        $reportTitle = 'Session Analytics Report';
        $filters = [
            'date_from' => $date_from,
            'date_to' => $date_to
        ];
        $sessions = $photoSessionService->getPhotoSessions($filters, 1000, 0);
        
        $analytics = [
            'total_sessions' => count($sessions),
            'sessions_by_operator' => [],
            'sessions_by_day' => [],
            'average_per_day' => 0
        ];
        
        foreach ($sessions as $session) {
            // By operator
            $operatorName = $session['operator_name'];
            if (!isset($analytics['sessions_by_operator'][$operatorName])) {
                $analytics['sessions_by_operator'][$operatorName] = 0;
            }
            $analytics['sessions_by_operator'][$operatorName]++;
            
            // By day
            $day = date('Y-m-d', strtotime($session['completed_at']));
            if (!isset($analytics['sessions_by_day'][$day])) {
                $analytics['sessions_by_day'][$day] = 0;
            }
            $analytics['sessions_by_day'][$day]++;
        }
        
        // Calculate average per day
        if (!empty($analytics['sessions_by_day'])) {
            $analytics['average_per_day'] = round(array_sum($analytics['sessions_by_day']) / count($analytics['sessions_by_day']), 1);
        }
        
        $reportData['analytics'] = $analytics;
        break;
        
    default:
        $reportTitle = 'Unknown Report';
        $reportData['error'] = 'Invalid report type specified';
}

// Handle different output formats
if ($format === 'csv') {
    // Generate CSV
    header('Content-Type: text/csv');
    header('Content-Disposition: attachment; filename="' . strtolower(str_replace(' ', '_', $reportTitle)) . '_' . date('Y-m-d') . '.csv"');
    
    $output = fopen('php://output', 'w');
    
    switch ($report_type) {
        case 'photo_sessions':
            fputcsv($output, ['Invoice', 'Student Name', 'NIC', 'Session', 'Sequence', 'Operator', 'Completed At']);
            foreach ($reportData['sessions'] as $session) {
                fputcsv($output, [
                    $session['invoice_number'],
                    $session['student_name'],
                    $session['student_nic'],
                    $session['session'],
                    $session['photo_sequence_number'],
                    $session['operator_name'],
                    $session['completed_at']
                ]);
            }
            break;
            
        case 'operator_performance':
            fputcsv($output, ['Operator Name', 'Username', 'Total Sessions', 'Today Sessions', 'Status', 'Last Login']);
            foreach ($reportData['operators'] as $operator) {
                fputcsv($output, [
                    $operator['name'],
                    $operator['username'],
                    $operator['total_sessions'],
                    $operator['today_sessions'],
                    $operator['status'],
                    $operator['last_login']
                ]);
            }
            break;
    }
    
    fclose($output);
    exit;
}

// For view format, continue to display HTML
?>
<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title><?php echo htmlspecialchars($reportTitle); ?> - <?php echo APP_NAME; ?></title>
    <link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/bulma@0.9.4/css/bulma.min.css">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css">
    <script src="https://cdn.jsdelivr.net/npm/chart.js"></script>
    <style>
        .report-header {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            padding: 2rem;
            border-radius: 8px;
            margin-bottom: 2rem;
        }
        .stat-card {
            border-left: 4px solid #667eea;
        }
        .chart-container {
            position: relative;
            height: 300px;
        }
        @media print {
            .no-print { display: none !important; }
            .report-header { background: #667eea !important; }
        }
    </style>
</head>
<body>
    <div class="container mt-4">
        <!-- Header -->
        <div class="report-header">
            <div class="level">
                <div class="level-left">
                    <div>
                        <h1 class="title is-3 has-text-white"><?php echo htmlspecialchars($reportTitle); ?></h1>
                        <p class="subtitle is-5 has-text-white">
                            Generated on <?php echo date('F j, Y \a\t g:i A'); ?>
                        </p>
                        <?php if ($date_from && $date_to): ?>
                            <p class="has-text-white">
                                <i class="fas fa-calendar mr-2"></i>
                                Period: <?php echo date('M j, Y', strtotime($date_from)); ?> - <?php echo date('M j, Y', strtotime($date_to)); ?>
                            </p>
                        <?php endif; ?>
                    </div>
                </div>
                <div class="level-right no-print">
                    <div class="buttons">
                        <a href="reports.php" class="button is-light">
                            <i class="fas fa-arrow-left mr-2"></i>
                            Back to Reports
                        </a>
                        <button class="button is-info" onclick="window.print()">
                            <i class="fas fa-print mr-2"></i>
                            Print
                        </button>
                        <a href="?<?php echo http_build_query(array_merge($_GET, ['format' => 'csv'])); ?>" class="button is-success">
                            <i class="fas fa-download mr-2"></i>
                            Download CSV
                        </a>
                    </div>
                </div>
            </div>
        </div>

        <?php if (isset($reportData['error'])): ?>
            <div class="notification is-danger">
                <i class="fas fa-exclamation-triangle mr-2"></i>
                <?php echo htmlspecialchars($reportData['error']); ?>
            </div>
        <?php else: ?>
            <!-- Report Content -->
            <?php include 'report-templates/' . str_replace('_', '-', $report_type) . '.php'; ?>
        <?php endif; ?>
    </div>
</body>
</html>
