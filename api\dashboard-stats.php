<?php
/**
 * API endpoint for dashboard statistics
 */

require_once '../config/config.php';

// Set JSON response headers
header('Content-Type: application/json');
header('Access-Control-Allow-Origin: *');
header('Access-Control-Allow-Methods: GET, OPTIONS');
header('Access-Control-Allow-Headers: Content-Type');

// Handle preflight requests
if ($_SERVER['REQUEST_METHOD'] === 'OPTIONS') {
    http_response_code(200);
    exit;
}

// Require authentication
if (!is_logged_in()) {
    json_response(['success' => false, 'message' => 'Authentication required'], 401);
}

try {
    $studentService = new StudentService();
    $photoSessionService = new PhotoSessionService();
    
    $stats = [
        'total_registered' => $studentService->getTotalRegisteredCount(),
        'total_completed' => $photoSessionService->getCompletedSessionsCount(),
        'today_completed' => $photoSessionService->getTodaySessionsCount(),
        'session_statistics' => $photoSessionService->getSessionStatistics()
    ];
    
    $stats['pending_count'] = $stats['total_registered'] - $stats['total_completed'];
    
    // Add operator-specific stats if not admin
    if (!is_admin()) {
        $operator_sessions = $photoSessionService->getSessionsByOperator($_SESSION['user_id'], date('Y-m-d'));
        $stats['my_today_count'] = count($operator_sessions);
        $stats['my_recent_sessions'] = array_slice($operator_sessions, 0, 5);
    }
    
    json_response(['success' => true, 'data' => $stats]);
    
} catch (Exception $e) {
    error_log("Dashboard stats API error: " . $e->getMessage());
    json_response(['success' => false, 'message' => 'Internal server error'], 500);
}
?>
