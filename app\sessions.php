<?php
// Authentication check - must be first
require_once 'auth-check.php';
require_admin();

$photoSessionService = new PhotoSessionService();
$operatorService = new OperatorService();

// Handle CSV export
if (isset($_GET['export']) && $_GET['export'] === 'csv') {
    $filters = [
        'search' => sanitize_input($_GET['search'] ?? ''),
        'session' => sanitize_input($_GET['session'] ?? ''),
        'operator_id' => intval($_GET['operator_id'] ?? 0) ?: null,
        'date_from' => sanitize_input($_GET['date_from'] ?? ''),
        'date_to' => sanitize_input($_GET['date_to'] ?? '')
    ];
    
    $csv_data = $photoSessionService->exportToCSV($filters);
    
    // Set CSV headers
    header('Content-Type: text/csv');
    header('Content-Disposition: attachment; filename="photo_sessions_' . date('Y-m-d_H-i-s') . '.csv"');
    
    $output = fopen('php://output', 'w');
    foreach ($csv_data as $row) {
        fputcsv($output, $row);
    }
    fclose($output);
    exit;
}

// Get filters
$filters = [
    'search' => sanitize_input($_GET['search'] ?? ''),
    'session' => sanitize_input($_GET['session'] ?? ''),
    'operator_id' => intval($_GET['operator_id'] ?? 0) ?: null,
    'date_from' => sanitize_input($_GET['date_from'] ?? ''),
    'date_to' => sanitize_input($_GET['date_to'] ?? '')
];

$page = max(1, intval($_GET['page'] ?? 1));
$limit = 25;
$offset = ($page - 1) * $limit;

$sessions = $photoSessionService->getPhotoSessions($filters, $limit, $offset);
$operators = $operatorService->getAllOperators();

// Get unique sessions for filter dropdown
$studentService = new StudentService();
$sessionStats = $studentService->getRegistrationStatsBySession();
?>
<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Photo Sessions - <?php echo APP_NAME; ?></title>
    <link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/bulma@0.9.4/css/bulma.min.css">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css">
    <style>
        .dashboard-header {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
        }
        .navbar-item.has-text-white:hover {
            background-color: rgba(255,255,255,0.1);
            color: white !important;
        }
        .navbar-item.is-active {
            background-color: rgba(255,255,255,0.2);
            font-weight: bold;
        }
        .navbar-dropdown {
            background-color: white;
            border: 1px solid #dbdbdb;
            box-shadow: 0 8px 16px rgba(10,10,10,.1);
        }
        .navbar-dropdown .navbar-item {
            color: #363636 !important;
            padding: 0.75rem 1rem;
        }
        .navbar-dropdown .navbar-item:hover {
            background-color: #f5f5f5;
            color: #363636 !important;
        }
        .navbar-dropdown .navbar-divider {
            background-color: #dbdbdb;
        }
        .navbar-brand .navbar-item {
            color: white !important;
            font-weight: bold;
        }
        .navbar-item.has-dropdown .navbar-link {
            color: white !important;
        }
        .navbar-item.has-dropdown .navbar-link:hover {
            background-color: rgba(255,255,255,0.05) !important;
            color: white !important;
        }
        .navbar-item.has-dropdown.is-active .navbar-link,
        .navbar-item.has-dropdown:hover .navbar-link {
            background-color: rgba(255,255,255,0.05) !important;
            color: white !important;
        }
        .navbar-link::after {
            border-color: white !important;
        }
        .filter-section {
            background: #f8f9fa;
            border-radius: 8px;
            padding: 1rem;
            margin-bottom: 1rem;
        }
        .session-row:hover {
            background-color: #f5f5f5;
        }

        /* Mobile responsiveness for admin pages */
        @media screen and (max-width: 1024px) {
            .navbar-menu {
                background-color: #667eea;
            }

            .container {
                padding: 0 1rem;
            }
        }

        @media screen and (max-width: 768px) {
            .title.is-4 {
                font-size: 1.5rem;
            }

            .title.is-5 {
                font-size: 1.25rem;
            }

            .subtitle.is-6 {
                font-size: 1rem;
            }

            .box {
                padding: 1rem;
            }

            .columns .column {
                padding: 0.5rem;
            }

            .table-container {
                overflow-x: auto;
            }

            .table {
                font-size: 0.875rem;
            }

            .table th,
            .table td {
                padding: 0.5rem;
            }

            .buttons .button {
                margin-bottom: 0.5rem;
            }

            .field.is-grouped {
                flex-direction: column;
            }

            .field.is-grouped .field {
                margin-bottom: 0.5rem;
            }
        }

        @media screen and (max-width: 480px) {
            .container {
                padding: 0 0.5rem;
            }

            .title.is-4 {
                font-size: 1.25rem;
            }

            .box {
                padding: 0.75rem;
            }

            .buttons {
                flex-direction: column;
            }

            .buttons .button {
                width: 100%;
                justify-content: flex-start;
            }

            .table {
                font-size: 0.8rem;
            }

            .table th,
            .table td {
                padding: 0.25rem;
            }

            .tag {
                font-size: 0.7rem;
            }

            .tags.are-small .tag {
                font-size: 0.65rem;
            }
        }
    </style>
</head>
<body>
    <!-- Navigation -->
    <nav class="navbar dashboard-header" role="navigation">
        <div class="navbar-brand">
            <div class="navbar-item" style="border-right: 1px solid #ccc; padding-right: 1rem; margin-right: 1rem;">
                <i class="fas fa-shield-alt mr-2" style="color: #ffffffff;"></i>
                <span style="font-weight: bold; color: #ffffffff;">Admin Dashboard</span>
            </div>

            <a role="button" class="navbar-burger has-text-white" data-target="navbarMenu">
                <span></span>
                <span></span>
                <span></span>
            </a>
        </div>

        <div class="navbar-menu" id="navbarMenu">
            <div class="navbar-start">
                <a class="navbar-item has-text-white" href="dashboard.php">
                    <i class="fas fa-tachometer-alt mr-2"></i>
                    Dashboard
                </a>
                <a class="navbar-item has-text-white" href="operators.php">
                    <i class="fas fa-users mr-2"></i>
                    Operators
                </a>
                <a class="navbar-item has-text-white is-active" href="sessions.php">
                    <i class="fas fa-camera mr-2"></i>
                    Photo Sessions
                </a>
                <a class="navbar-item has-text-white" href="event-sessions.php">
                    <i class="fas fa-calendar-alt mr-2"></i>
                    Event Sessions
                </a>
                <a class="navbar-item has-text-white" href="students.php">
                    <i class="fas fa-graduation-cap mr-2"></i>
                    Students
                </a>
                <a class="navbar-item has-text-white" href="photo-pricing.php">
                    <i class="fas fa-tags mr-2"></i>
                    Photo Pricing
                </a>
                <a class="navbar-item has-text-white" href="reports.php">
                    <i class="fas fa-chart-bar mr-2"></i>
                    Reports
                </a>
            </div>
            
            <div class="navbar-end">
                <div class="navbar-item has-dropdown is-hoverable">
                    <a class="navbar-link has-text-white">
                        <i class="fas fa-user-shield mr-2"></i>
                        <?php echo htmlspecialchars($_SESSION['full_name']); ?>
                    </a>
                    <div class="navbar-dropdown">
                        <a class="navbar-item" href="profile.php">
                            <i class="fas fa-user-cog mr-2"></i>
                            Profile
                        </a>
                        <a class="navbar-item" href="settings.php">
                            <i class="fas fa-cog mr-2"></i>
                            Settings
                        </a>
                        <hr class="navbar-divider">
                        <a class="navbar-item" href="../logout.php">
                            <i class="fas fa-sign-out-alt mr-2"></i>
                            Logout
                        </a>
                    </div>
                </div>
            </div>
        </div>
    </nav>

    <div class="container mt-4">
        <!-- Header -->
        <div class="box">
            <div class="level">
                <div class="level-left">
                    <div>
                        <h1 class="title is-4">
                            <i class="fas fa-camera mr-2"></i>
                            Photo Sessions
                        </h1>
                        <p class="subtitle is-6">View and manage completed photo sessions</p>
                    </div>
                </div>
                <div class="level-right">
                    <a href="?<?php echo http_build_query(array_merge($filters, ['export' => 'csv'])); ?>" 
                       class="button is-success">
                        <i class="fas fa-download mr-2"></i>
                        Export CSV
                    </a>
                </div>
            </div>
        </div>

        <!-- Filters -->
        <div class="filter-section">
            <form method="GET" action="">
                <div class="columns">
                    <div class="column is-3">
                        <div class="field">
                            <label class="label is-small">Search</label>
                            <div class="control">
                                <input class="input is-small" type="text" name="search" 
                                       placeholder="Name, NIC, or Invoice" 
                                       value="<?php echo htmlspecialchars($filters['search']); ?>">
                            </div>
                        </div>
                    </div>
                    <div class="column is-2">
                        <div class="field">
                            <label class="label is-small">Session</label>
                            <div class="control">
                                <div class="select is-small is-fullwidth">
                                    <select name="session">
                                        <option value="">All Sessions</option>
                                        <?php foreach ($sessionStats as $session): ?>
                                            <option value="<?php echo htmlspecialchars($session['session']); ?>"
                                                    <?php echo $filters['session'] === $session['session'] ? 'selected' : ''; ?>>
                                                <?php echo htmlspecialchars($session['session_name'] ?? $session['session']); ?>
                                            </option>
                                        <?php endforeach; ?>
                                    </select>
                                </div>
                            </div>
                        </div>
                    </div>
                    <div class="column is-2">
                        <div class="field">
                            <label class="label is-small">Operator</label>
                            <div class="control">
                                <div class="select is-small is-fullwidth">
                                    <select name="operator_id">
                                        <option value="">All Operators</option>
                                        <?php foreach ($operators as $operator): ?>
                                            <option value="<?php echo $operator['id']; ?>"
                                                    <?php echo $filters['operator_id'] == $operator['id'] ? 'selected' : ''; ?>>
                                                <?php echo htmlspecialchars($operator['full_name']); ?>
                                            </option>
                                        <?php endforeach; ?>
                                    </select>
                                </div>
                            </div>
                        </div>
                    </div>
                    <div class="column is-2">
                        <div class="field">
                            <label class="label is-small">From Date</label>
                            <div class="control">
                                <input class="input is-small" type="date" name="date_from" 
                                       value="<?php echo htmlspecialchars($filters['date_from']); ?>">
                            </div>
                        </div>
                    </div>
                    <div class="column is-2">
                        <div class="field">
                            <label class="label is-small">To Date</label>
                            <div class="control">
                                <input class="input is-small" type="date" name="date_to" 
                                       value="<?php echo htmlspecialchars($filters['date_to']); ?>">
                            </div>
                        </div>
                    </div>
                    <div class="column is-1">
                        <div class="field">
                            <label class="label is-small">&nbsp;</label>
                            <div class="control">
                                <button class="button is-primary is-small is-fullwidth" type="submit">
                                    <i class="fas fa-search"></i>
                                </button>
                            </div>
                        </div>
                    </div>
                </div>
                
                <?php if (array_filter($filters)): ?>
                    <div class="has-text-centered">
                        <a href="sessions.php" class="button is-small is-light">
                            <i class="fas fa-times mr-1"></i>
                            Clear Filters
                        </a>
                    </div>
                <?php endif; ?>
            </form>
        </div>

        <!-- Sessions Table -->
        <div class="box">
            <?php if (empty($sessions)): ?>
                <div class="has-text-centered py-6">
                    <i class="fas fa-inbox fa-3x has-text-grey-light mb-4"></i>
                    <p class="title is-5 has-text-grey">No photo sessions found</p>
                    <p class="has-text-grey">Try adjusting your search filters</p>
                </div>
            <?php else: ?>
                <div class="table-container">
                    <table class="table is-fullwidth is-hoverable">
                        <thead>
                            <tr>
                                <th>Invoice</th>
                                <th>Student</th>
                                <th>Contact</th>
                                <th>Session</th>
                                <th>Photo Types</th>
                                <th>Sequence #</th>
                                <th>Operator</th>
                                <th>Completed</th>
                            </tr>
                        </thead>
                        <tbody>
                            <?php foreach ($sessions as $session): ?>
                                <tr class="session-row">
                                    <td>
                                        <strong><?php echo htmlspecialchars($session['invoice_number']); ?></strong>
                                    </td>
                                    <td>
                                        <div>
                                            <strong><?php echo htmlspecialchars($session['student_name']); ?></strong><br>
                                            <small class="has-text-grey">
                                                NIC: <?php echo htmlspecialchars($session['student_nic']); ?>
                                            </small>
                                        </div>
                                    </td>
                                    <td>
                                        <?php if ($session['whatsapp_number']): ?>
                                            <a href="tel:<?php echo htmlspecialchars($session['whatsapp_number']); ?>" 
                                               class="has-text-success">
                                                <i class="fab fa-whatsapp mr-1"></i>
                                                <?php echo htmlspecialchars($session['whatsapp_number']); ?>
                                            </a><br>
                                        <?php endif; ?>
                                        <?php if ($session['alternate_phone']): ?>
                                            <a href="tel:<?php echo htmlspecialchars($session['alternate_phone']); ?>" 
                                               class="has-text-info">
                                                <i class="fas fa-phone mr-1"></i>
                                                <?php echo htmlspecialchars($session['alternate_phone']); ?>
                                            </a>
                                        <?php endif; ?>
                                    </td>
                                    <td>
                                        <div>
                                            <strong><?php echo htmlspecialchars($session['session']); ?></strong><br>
                                            <?php if ($session['seat_number']): ?>
                                                <small class="has-text-grey">
                                                    Seat: <?php echo htmlspecialchars($session['seat_number']); ?>
                                                </small>
                                            <?php endif; ?>
                                        </div>
                                    </td>
                                    <td>
                                        <div class="tags are-small">
                                            <?php foreach ($session['photo_types'] as $type): ?>
                                                <span class="tag is-info"><?php echo htmlspecialchars($type); ?></span>
                                            <?php endforeach; ?>
                                        </div>
                                    </td>
                                    <td>
                                        <span class="tag is-primary is-medium">
                                            <?php echo htmlspecialchars($session['photo_sequence_number']); ?>
                                        </span>
                                    </td>
                                    <td>
                                        <div>
                                            <strong><?php echo htmlspecialchars($session['operator_name']); ?></strong><br>
                                            <small class="has-text-grey">
                                                @<?php echo htmlspecialchars($session['operator_username']); ?>
                                            </small>
                                        </div>
                                    </td>
                                    <td>
                                        <div>
                                            <strong><?php echo date('M j, Y', strtotime($session['completed_at'])); ?></strong><br>
                                            <small class="has-text-grey">
                                                <?php echo date('H:i', strtotime($session['completed_at'])); ?>
                                            </small>
                                        </div>
                                    </td>
                                </tr>
                            <?php endforeach; ?>
                        </tbody>
                    </table>
                </div>
                
                <!-- Pagination -->
                <nav class="pagination is-centered mt-4" role="navigation">
                    <?php if ($page > 1): ?>
                        <a href="?<?php echo http_build_query(array_merge($filters, ['page' => $page - 1])); ?>" 
                           class="pagination-previous">Previous</a>
                    <?php endif; ?>
                    
                    <?php if (count($sessions) == $limit): ?>
                        <a href="?<?php echo http_build_query(array_merge($filters, ['page' => $page + 1])); ?>" 
                           class="pagination-next">Next</a>
                    <?php endif; ?>
                    
                    <ul class="pagination-list">
                        <li><span class="pagination-link is-current">Page <?php echo $page; ?></span></li>
                    </ul>
                </nav>
            <?php endif; ?>
        </div>
    </div>

    <script>
        // Mobile navigation toggle
        document.addEventListener('DOMContentLoaded', function() {
            const burger = document.querySelector('.navbar-burger');
            const menu = document.querySelector('.navbar-menu');

            if (burger && menu) {
                burger.addEventListener('click', function() {
                    burger.classList.toggle('is-active');
                    menu.classList.toggle('is-active');
                });
            }
        });
    </script>
</body>
</html>
