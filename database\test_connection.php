<?php
/**
 * Simple Database Connection Test
 * Use this to diagnose database connection issues
 */

// Security check
$allowed_ips = ['127.0.0.1', '::1', 'localhost'];
if (!in_array($_SERVER['REMOTE_ADDR'], $allowed_ips) && !in_array($_SERVER['HTTP_HOST'], ['localhost', '127.0.0.1'])) {
    die('Access denied. Connection test can only be run from localhost.');
}

echo "<h1>Database Connection Test</h1>";
echo "<p>Testing database connection step by step...</p>";

// Step 1: Check if config file exists
echo "<h2>Step 1: Configuration File Check</h2>";
$config_file = '../config/database.php';
if (file_exists($config_file)) {
    echo "✅ Configuration file exists: {$config_file}<br>";
    require_once $config_file;
} else {
    echo "❌ Configuration file not found: {$config_file}<br>";
    die("Cannot proceed without configuration file.");
}

// Step 2: Check constants
echo "<h2>Step 2: Database Constants Check</h2>";
$required_constants = ['DB_HOST', 'DB_USER', 'DB_PASS', 'DB_NAME'];
$missing_constants = [];

foreach ($required_constants as $constant) {
    if (defined($constant)) {
        $value = constant($constant);
        if ($constant === 'DB_PASS') {
            $display_value = empty($value) ? '(empty)' : '(hidden)';
        } else {
            $display_value = $value;
        }
        echo "✅ {$constant}: {$display_value}<br>";
    } else {
        echo "❌ {$constant}: Not defined<br>";
        $missing_constants[] = $constant;
    }
}

if (!empty($missing_constants)) {
    die("Missing constants: " . implode(', ', $missing_constants));
}

// Step 3: Test basic MySQL connection (without database)
echo "<h2>Step 3: MySQL Server Connection Test</h2>";
try {
    $dsn = "mysql:host=" . DB_HOST;
    $pdo = new PDO($dsn, DB_USER, DB_PASS, [PDO::ATTR_ERRMODE => PDO::ERRMODE_EXCEPTION]);
    echo "✅ Successfully connected to MySQL server at " . DB_HOST . "<br>";
    
    // Get MySQL version
    $stmt = $pdo->query("SELECT VERSION() as version");
    $version = $stmt->fetch()['version'];
    echo "✅ MySQL Version: {$version}<br>";
    
} catch (PDOException $e) {
    echo "❌ Failed to connect to MySQL server<br>";
    echo "Error: " . $e->getMessage() . "<br>";
    
    // Common solutions
    echo "<h3>Common Solutions:</h3>";
    echo "<ul>";
    echo "<li>Make sure XAMPP/WAMP is running</li>";
    echo "<li>Check if MySQL service is started</li>";
    echo "<li>Verify DB_HOST is correct (usually 'localhost' or '127.0.0.1')</li>";
    echo "<li>Check DB_USER and DB_PASS credentials</li>";
    echo "</ul>";
    
    die("Cannot proceed without MySQL connection.");
}

// Step 4: Check if database exists
echo "<h2>Step 4: Database Existence Check</h2>";
try {
    $stmt = $pdo->query("SHOW DATABASES LIKE '" . DB_NAME . "'");
    if ($stmt->rowCount() > 0) {
        echo "✅ Database '" . DB_NAME . "' exists<br>";
    } else {
        echo "⚠️ Database '" . DB_NAME . "' does not exist<br>";
        echo "This is normal for first-time setup. The migration will create it.<br>";
    }
} catch (PDOException $e) {
    echo "❌ Error checking database existence: " . $e->getMessage() . "<br>";
}

// Step 5: Test full connection with database
echo "<h2>Step 5: Full Database Connection Test</h2>";
try {
    $dsn = "mysql:host=" . DB_HOST . ";dbname=" . DB_NAME . ";charset=utf8mb4";
    $pdo_full = new PDO($dsn, DB_USER, DB_PASS, [
        PDO::ATTR_ERRMODE => PDO::ERRMODE_EXCEPTION,
        PDO::ATTR_DEFAULT_FETCH_MODE => PDO::FETCH_ASSOC
    ]);
    echo "✅ Successfully connected to database '" . DB_NAME . "'<br>";
    
    // Check existing tables
    $stmt = $pdo_full->query("SHOW TABLES");
    $tables = $stmt->fetchAll(PDO::FETCH_COLUMN);
    
    if (empty($tables)) {
        echo "ℹ️ Database is empty (no tables found)<br>";
        echo "This is normal for a fresh installation.<br>";
    } else {
        echo "ℹ️ Found " . count($tables) . " existing tables:<br>";
        echo "<ul>";
        foreach (array_slice($tables, 0, 10) as $table) {
            echo "<li>{$table}</li>";
        }
        if (count($tables) > 10) {
            echo "<li>... and " . (count($tables) - 10) . " more</li>";
        }
        echo "</ul>";
    }
    
} catch (PDOException $e) {
    if (strpos($e->getMessage(), 'Unknown database') !== false) {
        echo "⚠️ Database '" . DB_NAME . "' does not exist yet<br>";
        echo "This is normal. The migration script will create it.<br>";
        
        // Try to create the database
        echo "<h3>Attempting to create database...</h3>";
        try {
            $pdo->exec("CREATE DATABASE IF NOT EXISTS `" . DB_NAME . "` DEFAULT CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci");
            echo "✅ Database '" . DB_NAME . "' created successfully<br>";
        } catch (PDOException $e2) {
            echo "❌ Failed to create database: " . $e2->getMessage() . "<br>";
        }
    } else {
        echo "❌ Database connection failed: " . $e->getMessage() . "<br>";
    }
}

// Step 6: Test the get_pdo_connection function
echo "<h2>Step 6: Function Test</h2>";
try {
    if (function_exists('get_pdo_connection')) {
        $test_pdo = get_pdo_connection();
        echo "✅ get_pdo_connection() function works correctly<br>";
    } else {
        echo "❌ get_pdo_connection() function not found<br>";
    }
} catch (Exception $e) {
    echo "❌ get_pdo_connection() failed: " . $e->getMessage() . "<br>";
}

// Step 7: Environment information
echo "<h2>Step 7: Environment Information</h2>";
echo "PHP Version: " . PHP_VERSION . "<br>";
echo "PDO Available: " . (extension_loaded('pdo') ? 'Yes' : 'No') . "<br>";
echo "PDO MySQL Available: " . (extension_loaded('pdo_mysql') ? 'Yes' : 'No') . "<br>";
echo "Server Software: " . ($_SERVER['SERVER_SOFTWARE'] ?? 'Unknown') . "<br>";
echo "Document Root: " . ($_SERVER['DOCUMENT_ROOT'] ?? 'Unknown') . "<br>";

// Final recommendation
echo "<h2>Summary & Next Steps</h2>";
if (isset($pdo)) {
    echo "<div style='background: #d4edda; padding: 15px; border: 1px solid #c3e6cb; border-radius: 5px;'>";
    echo "<strong>✅ Connection Test Successful!</strong><br>";
    echo "Your database configuration is working correctly.<br>";
    echo "You can now proceed with the migration:<br>";
    echo "<ul>";
    echo "<li><a href='check_config.php'>Run Configuration Check</a></li>";
    echo "<li><a href='migrate.php'>Start Database Migration</a></li>";
    echo "</ul>";
    echo "</div>";
} else {
    echo "<div style='background: #f8d7da; padding: 15px; border: 1px solid #f5c6cb; border-radius: 5px;'>";
    echo "<strong>❌ Connection Issues Found</strong><br>";
    echo "Please resolve the connection issues above before proceeding with migration.<br>";
    echo "</div>";
}
?>

<style>
body {
    font-family: Arial, sans-serif;
    max-width: 800px;
    margin: 0 auto;
    padding: 20px;
    line-height: 1.6;
}
h1, h2, h3 {
    color: #333;
}
h2 {
    border-bottom: 2px solid #667eea;
    padding-bottom: 5px;
}
ul {
    margin: 10px 0;
}
li {
    margin: 5px 0;
}
</style>
