<?php
require_once '../config/config.php';

if (session_status() === PHP_SESSION_NONE) {
    session_start();
}

// Check if user came from previous steps
if (!isset($_SESSION['registration_nic']) || !isset($_SESSION['registration_event_id']) || 
    !isset($_SESSION['registration_data']) || !isset($_SESSION['photo_orders'])) {
    redirect('registration.php');
}

$message = '';
$message_type = '';
$registration_complete = false;

// Handle form submission (Complete Registration)
if ($_SERVER['REQUEST_METHOD'] === 'POST' && isset($_POST['action']) && $_POST['action'] === 'complete_registration') {
    try {
        $pdo = get_pdo_connection();
        $pdo->beginTransaction();
        
        // Generate invoice number
        $invoice_number = 'INV-' . date('Y') . '-' . str_pad(rand(1, 99999), 5, '0', STR_PAD_LEFT);
        
        // Insert student record
        $stmt = $pdo->prepare("
            INSERT INTO students (
                invoice_number, nic_number, full_name, email, phone_number, 
                whatsapp_number, address, event_id, session, seat_number, 
                delivery_method_id, delivery_address, total_amount, 
                payment_status, registration_status
            ) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, 'pending', 'registered')
        ");
        
        $stmt->execute([
            $invoice_number,
            $_SESSION['registration_nic'],
            $_SESSION['registration_data']['full_name'],
            $_SESSION['registration_data']['email'],
            $_SESSION['registration_data']['phone_number'],
            $_SESSION['registration_data']['whatsapp_number'],
            $_SESSION['registration_data']['address'],
            $_SESSION['registration_event_id'],
            $_SESSION['registration_data']['session'],
            $_SESSION['registration_data']['seat_number'],
            $_SESSION['delivery_method']['id'],
            $_SESSION['delivery_address'],
            $_SESSION['total_amount']
        ]);
        
        $student_id = $pdo->lastInsertId();
        
        // Insert photo orders
        $stmt = $pdo->prepare("
            INSERT INTO student_photo_orders (student_id, photo_type_id, quantity, unit_price, total_price)
            VALUES (?, ?, ?, ?, ?)
        ");
        
        foreach ($_SESSION['photo_orders'] as $order) {
            $stmt->execute([
                $student_id,
                $order['photo_type_id'],
                $order['quantity'],
                $order['unit_price'],
                $order['total_price']
            ]);
        }
        
        $pdo->commit();
        
        // Store success data
        $_SESSION['registration_success'] = [
            'invoice_number' => $invoice_number,
            'student_id' => $student_id
        ];
        
        $registration_complete = true;
        $message = 'Registration completed successfully!';
        $message_type = 'is-success';
        
    } catch (Exception $e) {
        $pdo->rollBack();
        $message = 'Error completing registration. Please try again.';
        $message_type = 'is-danger';
        error_log("Registration completion error: " . $e->getMessage());
    }
}

// Get event details
try {
    $pdo = get_pdo_connection();
    $stmt = $pdo->prepare("SELECT * FROM events WHERE id = ?");
    $stmt->execute([$_SESSION['registration_event_id']]);
    $event = $stmt->fetch(PDO::FETCH_ASSOC);
} catch (Exception $e) {
    $event = null;
}
?>
<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Registration Step 4 - <?php echo APP_NAME; ?></title>
    <link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/bulma@0.9.4/css/bulma.min.css">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css">
    <style>
        .hero-section {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
        }
        .registration-card {
            border-radius: 15px;
            box-shadow: 0 10px 30px rgba(0,0,0,0.1);
        }
        .step-indicator {
            display: flex;
            justify-content: center;
            margin-bottom: 2rem;
        }
        .step {
            display: flex;
            align-items: center;
            margin: 0 1rem;
        }
        .step-number {
            width: 40px;
            height: 40px;
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
            font-weight: bold;
            margin-right: 0.5rem;
        }
        .step.active .step-number {
            background-color: #3273dc;
            color: white;
        }
        .step.completed .step-number {
            background-color: #48c78e;
            color: white;
        }
        .step.pending .step-number {
            background-color: #dbdbdb;
            color: #7a7a7a;
        }
        .summary-card {
            background: linear-gradient(135deg, #f8f9fa 0%, #e9ecef 100%);
            border-radius: 12px;
            padding: 1.5rem;
            margin-bottom: 1rem;
        }
        .payment-info {
            background: linear-gradient(135deg, #fff3cd 0%, #ffeaa7 100%);
            border-radius: 12px;
            padding: 1.5rem;
            border-left: 5px solid #ffc107;
        }
        .success-animation {
            animation: bounceIn 1s ease-out;
        }
        @keyframes bounceIn {
            0% { transform: scale(0.3); opacity: 0; }
            50% { transform: scale(1.05); }
            70% { transform: scale(0.9); }
            100% { transform: scale(1); opacity: 1; }
        }
    </style>
</head>
<body>
    <section class="hero hero-section">
        <div class="hero-body">
            <div class="container">
                <!-- Header -->
                <div class="has-text-centered mb-6">
                    <?php if ($registration_complete): ?>
                        <div class="success-animation">
                            <i class="fas fa-check-circle fa-4x has-text-success mb-4"></i>
                            <h1 class="title is-2 has-text-white">Registration Complete!</h1>
                            <p class="subtitle is-4 has-text-white">Thank you for registering</p>
                        </div>
                    <?php else: ?>
                        <h1 class="title is-2 has-text-white">
                            <i class="fas fa-credit-card mr-3"></i>
                            Review & Payment
                        </h1>
                        <p class="subtitle is-4 has-text-white">
                            Review your order and complete registration
                        </p>
                    <?php endif; ?>
                </div>

                <!-- Messages -->
                <?php if ($message): ?>
                    <div class="columns is-centered">
                        <div class="column is-10">
                            <div class="notification <?php echo $message_type; ?>">
                                <button class="delete" onclick="this.parentElement.style.display='none'"></button>
                                <?php echo htmlspecialchars($message); ?>
                            </div>
                        </div>
                    </div>
                <?php endif; ?>

                <!-- Registration Form -->
                <div class="columns is-centered">
                    <div class="column is-10">
                        <div class="box registration-card">
                            
                            <?php if ($registration_complete): ?>
                                <!-- Success View -->
                                <div class="has-text-centered">
                                    <div class="notification is-success is-light">
                                        <h2 class="title is-4">
                                            <i class="fas fa-check-circle mr-2"></i>
                                            Registration Successful!
                                        </h2>
                                        <p class="subtitle is-6">Your registration has been completed successfully.</p>
                                        
                                        <div class="content">
                                            <p><strong>Invoice Number:</strong> <?php echo htmlspecialchars($_SESSION['registration_success']['invoice_number']); ?></p>
                                            <p><strong>Student Name:</strong> <?php echo htmlspecialchars($_SESSION['registration_data']['full_name']); ?></p>
                                            <p><strong>Event:</strong> <?php echo htmlspecialchars($event['event_name'] ?? 'Unknown'); ?></p>
                                            <p><strong>Total Amount:</strong> LKR <?php echo number_format($_SESSION['total_amount'], 2); ?></p>
                                        </div>
                                    </div>

                                    <div class="notification is-info is-light">
                                        <h3 class="title is-5">What's Next?</h3>
                                        <div class="content">
                                            <ol>
                                                <li>You will receive a confirmation email (if provided)</li>
                                                <li>Payment instructions will be sent separately</li>
                                                <li>Attend your scheduled photo session</li>
                                                <li>Track your order status using your NIC number</li>
                                            </ol>
                                        </div>
                                    </div>

                                    <div class="buttons is-centered">
                                        <a href="status-check.php" class="button is-primary is-large">
                                            <i class="fas fa-search mr-2"></i>
                                            Check Status
                                        </a>
                                        <a href="index.php" class="button is-light is-large">
                                            <i class="fas fa-home mr-2"></i>
                                            Back to Home
                                        </a>
                                    </div>
                                </div>

                            <?php else: ?>
                                <!-- Review and Payment View -->
                                <!-- Step Indicator -->
                                <div class="step-indicator">
                                    <div class="step completed">
                                        <div class="step-number"><i class="fas fa-check"></i></div>
                                        <span>Verification</span>
                                    </div>
                                    <div class="step completed">
                                        <div class="step-number"><i class="fas fa-check"></i></div>
                                        <span>Details</span>
                                    </div>
                                    <div class="step completed">
                                        <div class="step-number"><i class="fas fa-check"></i></div>
                                        <span>Photos</span>
                                    </div>
                                    <div class="step active">
                                        <div class="step-number">4</div>
                                        <span>Payment</span>
                                    </div>
                                </div>

                                <div class="columns">
                                    <div class="column is-8">
                                        <!-- Registration Summary -->
                                        <h2 class="title is-4">
                                            <i class="fas fa-user mr-2"></i>
                                            Registration Summary
                                        </h2>
                                        
                                        <div class="summary-card">
                                            <div class="columns">
                                                <div class="column is-6">
                                                    <h3 class="title is-6">Personal Information</h3>
                                                    <p><strong>Name:</strong> <?php echo htmlspecialchars($_SESSION['registration_data']['full_name']); ?></p>
                                                    <p><strong>NIC:</strong> <?php echo htmlspecialchars($_SESSION['registration_nic']); ?></p>
                                                    <p><strong>Email:</strong> <?php echo htmlspecialchars($_SESSION['registration_data']['email'] ?: 'Not provided'); ?></p>
                                                    <p><strong>Phone:</strong> <?php echo htmlspecialchars($_SESSION['registration_data']['phone_number'] ?: 'Not provided'); ?></p>
                                                </div>
                                                <div class="column is-6">
                                                    <h3 class="title is-6">Event Details</h3>
                                                    <p><strong>Event:</strong> <?php echo htmlspecialchars($event['event_name'] ?? 'Unknown'); ?></p>
                                                    <p><strong>Session:</strong> <?php echo htmlspecialchars($_SESSION['registration_data']['session'] ?: 'Not specified'); ?></p>
                                                    <p><strong>Delivery:</strong> <?php echo htmlspecialchars($_SESSION['delivery_method']['method_name']); ?></p>
                                                    <?php if (!empty($_SESSION['delivery_address'])): ?>
                                                        <p><strong>Address:</strong> <?php echo htmlspecialchars($_SESSION['delivery_address']); ?></p>
                                                    <?php endif; ?>
                                                </div>
                                            </div>
                                        </div>

                                        <!-- Photo Orders -->
                                        <h2 class="title is-4">
                                            <i class="fas fa-images mr-2"></i>
                                            Photo Orders
                                        </h2>
                                        
                                        <div class="table-container">
                                            <table class="table is-fullwidth is-striped">
                                                <thead>
                                                    <tr>
                                                        <th>Photo Type</th>
                                                        <th>Quantity</th>
                                                        <th>Unit Price</th>
                                                        <th>Total</th>
                                                    </tr>
                                                </thead>
                                                <tbody>
                                                    <?php foreach ($_SESSION['photo_orders'] as $order): ?>
                                                        <tr>
                                                            <td><strong><?php echo htmlspecialchars($order['type_name']); ?></strong></td>
                                                            <td><?php echo $order['quantity']; ?></td>
                                                            <td>LKR <?php echo number_format($order['unit_price'], 2); ?></td>
                                                            <td><strong>LKR <?php echo number_format($order['total_price'], 2); ?></strong></td>
                                                        </tr>
                                                    <?php endforeach; ?>
                                                    <?php if ($_SESSION['delivery_cost'] > 0): ?>
                                                        <tr>
                                                            <td><strong><?php echo htmlspecialchars($_SESSION['delivery_method']['method_name']); ?></strong></td>
                                                            <td>1</td>
                                                            <td>LKR <?php echo number_format($_SESSION['delivery_cost'], 2); ?></td>
                                                            <td><strong>LKR <?php echo number_format($_SESSION['delivery_cost'], 2); ?></strong></td>
                                                        </tr>
                                                    <?php endif; ?>
                                                </tbody>
                                                <tfoot>
                                                    <tr class="has-background-primary-light">
                                                        <th colspan="3">Total Amount</th>
                                                        <th class="title is-5">LKR <?php echo number_format($_SESSION['total_amount'], 2); ?></th>
                                                    </tr>
                                                </tfoot>
                                            </table>
                                        </div>
                                    </div>

                                    <div class="column is-4">
                                        <!-- Payment Information -->
                                        <div class="payment-info">
                                            <h3 class="title is-5">
                                                <i class="fas fa-info-circle mr-2"></i>
                                                Payment Information
                                            </h3>
                                            <div class="content">
                                                <p><strong>Total Amount:</strong><br>
                                                <span class="title is-4">LKR <?php echo number_format($_SESSION['total_amount'], 2); ?></span></p>
                                                
                                                <p><strong>Payment Status:</strong><br>
                                                <span class="tag is-warning">Pending</span></p>
                                                
                                                <hr>
                                                
                                                <p class="is-size-7">
                                                    <i class="fas fa-exclamation-triangle mr-1"></i>
                                                    Payment gateway integration is currently being set up. 
                                                    You will receive payment instructions via email or SMS.
                                                </p>
                                            </div>
                                        </div>

                                        <!-- Action Buttons -->
                                        <div class="buttons is-fullwidth mt-4">
                                            <a href="registration-step3.php" class="button is-light is-large">
                                                <i class="fas fa-arrow-left mr-2"></i>
                                                Back
                                            </a>
                                            <form method="POST" action="" style="width: 100%;">
                                                <input type="hidden" name="action" value="complete_registration">
                                                <button class="button is-success is-large is-fullwidth" type="submit">
                                                    <i class="fas fa-check mr-2"></i>
                                                    Complete Registration
                                                </button>
                                            </form>
                                        </div>
                                    </div>
                                </div>
                            <?php endif; ?>
                        </div>
                    </div>
                </div>

                <!-- Back to Home -->
                <div class="has-text-centered mt-6">
                    <a href="index.php" class="button is-light is-large">
                        <i class="fas fa-home mr-2"></i>
                        Back to Home
                    </a>
                </div>
            </div>
        </div>
    </section>

    <?php if ($registration_complete): ?>
        <script>
            // Clear registration session data after successful completion
            <?php 
            unset($_SESSION['registration_nic']);
            unset($_SESSION['registration_event_id']);
            unset($_SESSION['registration_data']);
            unset($_SESSION['photo_orders']);
            unset($_SESSION['delivery_method']);
            unset($_SESSION['delivery_address']);
            unset($_SESSION['total_amount']);
            unset($_SESSION['delivery_cost']);
            ?>
        </script>
    <?php endif; ?>
</body>
</html>
