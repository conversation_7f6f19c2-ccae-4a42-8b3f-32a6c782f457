<?php
/**
 * Student Service Class
 * Handles student data retrieval from external database
 */

class StudentService {
    private $external_db;
    private $local_db;
    
    public function __construct() {
        $this->external_db = DatabaseConfig::getExternalConnection();
        $this->local_db = DatabaseConfig::getLocalConnection();
    }
    
    /**
     * Get student information by invoice number
     */
    public function getStudentByInvoice($invoiceNumber) {
        try {
            $stmt = $this->external_db->prepare("
                SELECT 
                    s.invoice_number,
                    s.nic_number,
                    s.full_name,
                    s.address,
                    s.whatsapp_number,
                    s.alternate_phone,
                    s.email,
                    s.session,
                    s.seat_number,
                    s.photo_types,
                    s.registration_fee,
                    s.payment_status,
                    cs.session_name,
                    cs.session_date,
                    cs.session_time,
                    cs.venue
                FROM students s
                LEFT JOIN convocation_sessions cs ON s.session = cs.session_code
                WHERE s.invoice_number = ? AND s.is_active = 1 AND s.payment_status = 'paid'
            ");
            
            $stmt->execute([$invoiceNumber]);
            $student = $stmt->fetch();
            
            if (!$student) {
                return [
                    'success' => false,
                    'message' => 'Student not found or payment not completed'
                ];
            }
            
            // Decode photo types JSON
            $student['photo_types'] = json_decode($student['photo_types'], true);
            
            // Get photo type details
            $student['photo_type_details'] = $this->getPhotoTypeDetails($student['photo_types']);
            
            // Check if photo session already completed
            $student['session_completed'] = $this->isSessionCompleted($invoiceNumber);
            
            return [
                'success' => true,
                'data' => $student
            ];
            
        } catch (Exception $e) {
            error_log("Error fetching student data: " . $e->getMessage());
            return [
                'success' => false,
                'message' => 'Error retrieving student information'
            ];
        }
    }
    
    /**
     * Get photo type details
     */
    private function getPhotoTypeDetails($photoTypeCodes) {
        if (empty($photoTypeCodes)) {
            return [];
        }
        
        try {
            $placeholders = str_repeat('?,', count($photoTypeCodes) - 1) . '?';
            $stmt = $this->external_db->prepare("
                SELECT type_code, type_name, price, description
                FROM photo_types 
                WHERE type_code IN ($placeholders) AND is_active = 1
            ");
            $stmt->execute($photoTypeCodes);
            
            return $stmt->fetchAll();
        } catch (Exception $e) {
            error_log("Error fetching photo type details: " . $e->getMessage());
            return [];
        }
    }
    
    /**
     * Check if photo session is already completed
     */
    private function isSessionCompleted($invoiceNumber) {
        try {
            $stmt = $this->local_db->prepare("
                SELECT id FROM photo_sessions WHERE invoice_number = ?
            ");
            $stmt->execute([$invoiceNumber]);
            
            return $stmt->fetch() !== false;
        } catch (Exception $e) {
            error_log("Error checking session completion: " . $e->getMessage());
            return false;
        }
    }
    
    /**
     * Get total registered students count
     */
    public function getTotalRegisteredCount() {
        try {
            $stmt = $this->external_db->prepare("
                SELECT COUNT(*) as total 
                FROM students 
                WHERE is_active = 1 AND payment_status = 'paid'
            ");
            $stmt->execute();
            $result = $stmt->fetch();
            
            return $result['total'];
        } catch (Exception $e) {
            error_log("Error getting total registered count: " . $e->getMessage());
            return 0;
        }
    }
    
    /**
     * Get registration statistics by session
     */
    public function getRegistrationStatsBySession() {
        try {
            $stmt = $this->external_db->prepare("
                SELECT 
                    s.session,
                    cs.session_name,
                    cs.session_date,
                    cs.session_time,
                    COUNT(*) as registered_count,
                    SUM(s.registration_fee) as total_revenue
                FROM students s
                LEFT JOIN convocation_sessions cs ON s.session = cs.session_code
                WHERE s.is_active = 1 AND s.payment_status = 'paid'
                GROUP BY s.session, cs.session_name, cs.session_date, cs.session_time
                ORDER BY cs.session_date, cs.session_time
            ");
            $stmt->execute();
            
            return $stmt->fetchAll();
        } catch (Exception $e) {
            error_log("Error getting registration stats: " . $e->getMessage());
            return [];
        }
    }
    
    /**
     * Search students by various criteria
     */
    public function searchStudents($searchTerm, $session = null, $limit = 50, $offset = 0) {
        try {
            $whereConditions = ["s.is_active = 1"];
            $params = [];
            
            if (!empty($searchTerm)) {
                $whereConditions[] = "(s.full_name LIKE ? OR s.nic_number LIKE ? OR s.invoice_number LIKE ?)";
                $searchPattern = "%{$searchTerm}%";
                $params[] = $searchPattern;
                $params[] = $searchPattern;
                $params[] = $searchPattern;
            }
            
            if (!empty($session)) {
                $whereConditions[] = "s.session = ?";
                $params[] = $session;
            }
            
            $whereClause = implode(" AND ", $whereConditions);
            
            $stmt = $this->external_db->prepare("
                SELECT 
                    s.invoice_number,
                    s.nic_number,
                    s.full_name,
                    s.session,
                    s.seat_number,
                    s.payment_status,
                    s.registration_date,
                    cs.session_name
                FROM students s
                LEFT JOIN convocation_sessions cs ON s.session = cs.session_code
                WHERE {$whereClause}
                ORDER BY s.registration_date DESC
                LIMIT ? OFFSET ?
            ");
            
            $params[] = $limit;
            $params[] = $offset;
            $stmt->execute($params);
            
            return $stmt->fetchAll();
        } catch (Exception $e) {
            error_log("Error searching students: " . $e->getMessage());
            return [];
        }
    }
}
?>
