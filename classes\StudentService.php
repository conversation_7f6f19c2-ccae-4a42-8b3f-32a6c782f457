<?php
/**
 * Student Service Class
 * Handles student data operations for unified database
 */

class StudentService {
    private $pdo;

    public function __construct() {
        $this->pdo = DatabaseConfig::getUnifiedConnection();
    }
    
    /**
     * Get student information by invoice number
     */
    public function getStudentByInvoice($invoiceNumber) {
        try {
            $stmt = $this->pdo->prepare("
                SELECT
                    s.id,
                    s.invoice_number,
                    s.nic_number,
                    s.full_name,
                    s.email,
                    s.phone_number,
                    s.whatsapp_number,
                    s.address,
                    s.session,
                    s.seat_number,
                    s.total_amount,
                    s.payment_status,
                    s.registration_status,
                    s.delivery_address,
                    s.notes,
                    s.created_at,
                    s.updated_at,
                    e.event_name,
                    e.event_date,
                    dm.method_name as delivery_method
                FROM students s
                LEFT JOIN events e ON s.event_id = e.id
                LEFT JOIN delivery_methods dm ON s.delivery_method_id = dm.id
                WHERE s.invoice_number = ?
            ");

            $stmt->execute([$invoiceNumber]);
            $student = $stmt->fetch(PDO::FETCH_ASSOC);

            if (!$student) {
                return [
                    'success' => false,
                    'message' => 'Student not found'
                ];
            }

            // Get photo orders for this student
            $photoStmt = $this->pdo->prepare("
                SELECT
                    spo.quantity,
                    spo.unit_price,
                    spo.total_price,
                    pt.type_name,
                    pt.type_code,
                    pt.description
                FROM student_photo_orders spo
                JOIN photo_types pt ON spo.photo_type_id = pt.id
                WHERE spo.student_id = ?
            ");
            $photoStmt->execute([$student['id']]);
            $student['photo_orders'] = $photoStmt->fetchAll(PDO::FETCH_ASSOC);

            // Check if photo session already completed
            $student['session_completed'] = $this->isSessionCompleted($invoiceNumber);

            return [
                'success' => true,
                'data' => $student
            ];

        } catch (Exception $e) {
            error_log("Error fetching student data: " . $e->getMessage());
            return [
                'success' => false,
                'message' => 'Error retrieving student information'
            ];
        }
    }
    
    /**
     * Get photo type details
     */
    private function getPhotoTypeDetails($photoTypeCodes) {
        if (empty($photoTypeCodes)) {
            return [];
        }

        try {
            $placeholders = str_repeat('?,', count($photoTypeCodes) - 1) . '?';
            $stmt = $this->pdo->prepare("
                SELECT type_code, type_name, description
                FROM photo_types
                WHERE type_code IN ($placeholders) AND is_active = 1
            ");
            $stmt->execute($photoTypeCodes);

            return $stmt->fetchAll();
        } catch (Exception $e) {
            error_log("Error fetching photo type details: " . $e->getMessage());
            return [];
        }
    }
    
    /**
     * Check if photo session is already completed
     */
    private function isSessionCompleted($invoiceNumber) {
        try {
            $stmt = $this->pdo->prepare("
                SELECT id FROM photo_sessions WHERE invoice_number = ?
            ");
            $stmt->execute([$invoiceNumber]);

            return $stmt->fetch() !== false;
        } catch (Exception $e) {
            error_log("Error checking session completion: " . $e->getMessage());
            return false;
        }
    }
    
    /**
     * Get total registered students count
     */
    public function getTotalRegisteredCount() {
        try {
            $stmt = $this->pdo->prepare("
                SELECT COUNT(*) as total
                FROM students
                WHERE payment_status = 'paid'
            ");
            $stmt->execute();
            $result = $stmt->fetch();

            return $result['total'];
        } catch (Exception $e) {
            error_log("Error getting total registered count: " . $e->getMessage());
            return 0;
        }
    }
    
    /**
     * Get registration statistics by session
     */
    public function getRegistrationStatsBySession() {
        try {
            $stmt = $this->pdo->prepare("
                SELECT 
                    s.session,
                    cs.session_name,
                    cs.session_date,
                    cs.session_time,
                    COUNT(*) as registered_count,
                    SUM(s.registration_fee) as total_revenue
                FROM students s
                LEFT JOIN convocation_sessions cs ON s.session = cs.session_code
                WHERE s.is_active = 1 AND s.payment_status = 'paid'
                GROUP BY s.session, cs.session_name, cs.session_date, cs.session_time
                ORDER BY cs.session_date, cs.session_time
            ");
            $stmt->execute();
            
            return $stmt->fetchAll();
        } catch (Exception $e) {
            error_log("Error getting registration stats: " . $e->getMessage());
            return [];
        }
    }
    
    /**
     * Search students by various criteria
     */
    public function searchStudents($searchTerm, $session = null, $limit = 50, $offset = 0) {
        try {
            $whereConditions = ["s.is_active = 1"];
            $params = [];
            
            if (!empty($searchTerm)) {
                $whereConditions[] = "(s.full_name LIKE ? OR s.nic_number LIKE ? OR s.invoice_number LIKE ?)";
                $searchPattern = "%{$searchTerm}%";
                $params[] = $searchPattern;
                $params[] = $searchPattern;
                $params[] = $searchPattern;
            }
            
            if (!empty($session)) {
                $whereConditions[] = "s.session = ?";
                $params[] = $session;
            }
            
            $whereClause = implode(" AND ", $whereConditions);
            
            $stmt = $this->external_db->prepare("
                SELECT 
                    s.invoice_number,
                    s.nic_number,
                    s.full_name,
                    s.session,
                    s.seat_number,
                    s.payment_status,
                    s.registration_date,
                    cs.session_name
                FROM students s
                LEFT JOIN convocation_sessions cs ON s.session = cs.session_code
                WHERE {$whereClause}
                ORDER BY s.registration_date DESC
                LIMIT ? OFFSET ?
            ");
            
            $params[] = $limit;
            $params[] = $offset;
            $stmt->execute($params);
            
            return $stmt->fetchAll();
        } catch (Exception $e) {
            error_log("Error searching students: " . $e->getMessage());
            return [];
        }
    }
}
?>
