<?php
/**
 * Student Service Class
 * Handles student data operations for unified database
 */

class StudentService {
    private $pdo;

    public function __construct() {
        $this->pdo = DatabaseConfig::getUnifiedConnection();
    }
    
    /**
     * Get student information by invoice number
     */
    public function getStudentByInvoice($invoiceNumber) {
        try {
            $stmt = $this->pdo->prepare("
                SELECT
                    s.id,
                    s.invoice_number,
                    s.nic_number,
                    s.full_name,
                    s.email,
                    s.phone_number,
                    s.whatsapp_number,
                    s.address,
                    s.session,
                    s.seat_number,
                    s.total_amount,
                    s.payment_status,
                    s.registration_status,
                    s.delivery_address,
                    s.notes,
                    s.created_at,
                    s.updated_at,
                    e.event_name,
                    e.event_date,
                    dm.method_name as delivery_method
                FROM students s
                LEFT JOIN events e ON s.event_id = e.id
                LEFT JOIN delivery_methods dm ON s.delivery_method_id = dm.id
                WHERE s.invoice_number = ?
            ");

            $stmt->execute([$invoiceNumber]);
            $student = $stmt->fetch(PDO::FETCH_ASSOC);

            if (!$student) {
                return [
                    'success' => false,
                    'message' => 'Student not found'
                ];
            }

            // Get photo orders for this student
            $photoStmt = $this->pdo->prepare("
                SELECT
                    spo.quantity,
                    spo.unit_price,
                    spo.total_price,
                    pt.type_name,
                    pt.type_code,
                    pt.description
                FROM student_photo_orders spo
                JOIN photo_types pt ON spo.photo_type_id = pt.id
                WHERE spo.student_id = ?
            ");
            $photoStmt->execute([$student['id']]);
            $student['photo_orders'] = $photoStmt->fetchAll(PDO::FETCH_ASSOC);

            // Check if photo session already completed
            $student['session_completed'] = $this->isSessionCompleted($invoiceNumber);

            return [
                'success' => true,
                'data' => $student
            ];

        } catch (Exception $e) {
            error_log("Error fetching student data: " . $e->getMessage());
            return [
                'success' => false,
                'message' => 'Error retrieving student information'
            ];
        }
    }
    
    /**
     * Get photo type details
     */
    private function getPhotoTypeDetails($photoTypeCodes) {
        if (empty($photoTypeCodes)) {
            return [];
        }

        try {
            $placeholders = str_repeat('?,', count($photoTypeCodes) - 1) . '?';
            $stmt = $this->pdo->prepare("
                SELECT type_code, type_name, description
                FROM photo_types
                WHERE type_code IN ($placeholders) AND is_active = 1
            ");
            $stmt->execute($photoTypeCodes);

            return $stmt->fetchAll();
        } catch (Exception $e) {
            error_log("Error fetching photo type details: " . $e->getMessage());
            return [];
        }
    }
    
    /**
     * Check if photo session is already completed
     */
    private function isSessionCompleted($invoiceNumber) {
        try {
            $stmt = $this->pdo->prepare("
                SELECT id FROM photo_sessions WHERE invoice_number = ?
            ");
            $stmt->execute([$invoiceNumber]);

            return $stmt->fetch() !== false;
        } catch (Exception $e) {
            error_log("Error checking session completion: " . $e->getMessage());
            return false;
        }
    }
    
    /**
     * Get total registered students count
     */
    public function getTotalRegisteredCount() {
        try {
            $stmt = $this->pdo->prepare("
                SELECT COUNT(*) as total
                FROM students
                WHERE payment_status = 'paid'
            ");
            $stmt->execute();
            $result = $stmt->fetch();

            return $result['total'];
        } catch (Exception $e) {
            error_log("Error getting total registered count: " . $e->getMessage());
            return 0;
        }
    }
    
    /**
     * Get registration statistics by session
     */
    public function getRegistrationStatsBySession() {
        try {
            $stmt = $this->pdo->prepare("
                SELECT 
                    s.session,
                    cs.session_name,
                    cs.session_date,
                    cs.session_time,
                    COUNT(*) as registered_count,
                    SUM(s.registration_fee) as total_revenue
                FROM students s
                LEFT JOIN convocation_sessions cs ON s.session = cs.session_code
                WHERE s.is_active = 1 AND s.payment_status = 'paid'
                GROUP BY s.session, cs.session_name, cs.session_date, cs.session_time
                ORDER BY cs.session_date, cs.session_time
            ");
            $stmt->execute();
            
            return $stmt->fetchAll();
        } catch (Exception $e) {
            error_log("Error getting registration stats: " . $e->getMessage());
            return [];
        }
    }
    
    /**
     * Search students by various criteria
     */
    public function searchStudents($searchTerm, $session = null, $limit = 50, $offset = 0) {
        try {
            $whereConditions = ["s.is_active = 1"];
            $params = [];
            
            if (!empty($searchTerm)) {
                $whereConditions[] = "(s.full_name LIKE ? OR s.nic_number LIKE ? OR s.invoice_number LIKE ?)";
                $searchPattern = "%{$searchTerm}%";
                $params[] = $searchPattern;
                $params[] = $searchPattern;
                $params[] = $searchPattern;
            }
            
            if (!empty($session)) {
                $whereConditions[] = "s.session = ?";
                $params[] = $session;
            }
            
            $whereClause = implode(" AND ", $whereConditions);
            
            $stmt = $this->pdo->prepare("
                SELECT
                    s.invoice_number,
                    s.nic_number,
                    s.full_name,
                    s.session,
                    s.seat_number,
                    s.payment_status,
                    s.created_at as registration_date,
                    es.session_name
                FROM students s
                LEFT JOIN event_sessions es ON s.session_id = es.id
                WHERE {$whereClause}
                ORDER BY s.created_at DESC
                LIMIT ? OFFSET ?
            ");
            
            $params[] = $limit;
            $params[] = $offset;
            $stmt->execute($params);
            
            return $stmt->fetchAll();
        } catch (Exception $e) {
            error_log("Error searching students: " . $e->getMessage());
            return [];
        }
    }

    /**
     * Create new student registration
     */
    public function createStudent($data) {
        try {
            $this->pdo->beginTransaction();

            // Generate invoice number
            $invoice_number = $this->generateInvoiceNumber();

            $stmt = $this->pdo->prepare("
                INSERT INTO students (
                    invoice_number, nic_number, full_name, email, phone_number,
                    whatsapp_number, address, event_id, session_id, session, seat_number,
                    delivery_method_id, delivery_address, total_amount,
                    payment_status, registration_status, created_at
                ) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, NOW())
            ");

            $stmt->execute([
                $invoice_number,
                $data['nic_number'],
                $data['full_name'],
                $data['email'],
                $data['phone_number'],
                $data['whatsapp_number'],
                $data['address'],
                $data['event_id'],
                $data['session_id'],
                $data['session'],
                $data['seat_number'],
                $data['delivery_method_id'],
                $data['delivery_address'],
                $data['total_amount'],
                $data['payment_status'] ?? 'pending',
                $data['registration_status'] ?? 'registered'
            ]);

            $student_id = $this->pdo->lastInsertId();

            // Insert photo orders
            if (!empty($data['photo_orders'])) {
                $stmt = $this->pdo->prepare("
                    INSERT INTO student_photo_orders (student_id, photo_type_id, quantity, unit_price, total_price)
                    VALUES (?, ?, ?, ?, ?)
                ");

                foreach ($data['photo_orders'] as $order) {
                    $stmt->execute([
                        $student_id,
                        $order['photo_type_id'],
                        $order['quantity'],
                        $order['unit_price'],
                        $order['total_price']
                    ]);
                }
            }

            $this->pdo->commit();

            return [
                'success' => true,
                'student_id' => $student_id,
                'invoice_number' => $invoice_number
            ];

        } catch (Exception $e) {
            $this->pdo->rollBack();
            error_log("Error creating student: " . $e->getMessage());
            return [
                'success' => false,
                'error' => $e->getMessage()
            ];
        }
    }

    /**
     * Generate unique invoice number
     */
    private function generateInvoiceNumber() {
        $prefix = 'INV-' . date('Y') . '-';
        $max_attempts = 10;

        for ($i = 0; $i < $max_attempts; $i++) {
            $number = $prefix . str_pad(rand(1, 99999), 5, '0', STR_PAD_LEFT);

            // Check if this invoice number already exists
            $stmt = $this->pdo->prepare("SELECT COUNT(*) FROM students WHERE invoice_number = ?");
            $stmt->execute([$number]);

            if ($stmt->fetchColumn() == 0) {
                return $number;
            }
        }

        // Fallback: use timestamp
        return $prefix . time();
    }

    /**
     * Get students with filters
     */
    public function getStudents($filters = [], $limit = 50, $offset = 0) {
        try {
            $whereConditions = ["1=1"];
            $params = [];

            if (!empty($filters['event_id'])) {
                $whereConditions[] = "s.event_id = ?";
                $params[] = $filters['event_id'];
            }

            if (!empty($filters['registration_status'])) {
                $whereConditions[] = "s.registration_status = ?";
                $params[] = $filters['registration_status'];
            }

            if (!empty($filters['payment_status'])) {
                $whereConditions[] = "s.payment_status = ?";
                $params[] = $filters['payment_status'];
            }

            if (!empty($filters['search'])) {
                $whereConditions[] = "(s.full_name LIKE ? OR s.nic_number LIKE ? OR s.invoice_number LIKE ?)";
                $searchPattern = "%{$filters['search']}%";
                $params[] = $searchPattern;
                $params[] = $searchPattern;
                $params[] = $searchPattern;
            }

            $whereClause = implode(" AND ", $whereConditions);

            $stmt = $this->pdo->prepare("
                SELECT
                    s.*,
                    e.event_name,
                    es.session_name,
                    dm.method_name as delivery_method
                FROM students s
                LEFT JOIN events e ON s.event_id = e.id
                LEFT JOIN event_sessions es ON s.session_id = es.id
                LEFT JOIN delivery_methods dm ON s.delivery_method_id = dm.id
                WHERE {$whereClause}
                ORDER BY s.created_at DESC
                LIMIT ? OFFSET ?
            ");

            $params[] = $limit;
            $params[] = $offset;
            $stmt->execute($params);

            return $stmt->fetchAll(PDO::FETCH_ASSOC);
        } catch (Exception $e) {
            error_log("Error getting students: " . $e->getMessage());
            return [];
        }
    }

    /**
     * Get student statistics
     */
    public function getStudentStatistics() {
        try {
            $stats = [];

            // Total students
            $stmt = $this->pdo->query("SELECT COUNT(*) as total FROM students");
            $stats['total'] = $stmt->fetchColumn();

            // By registration status
            $stmt = $this->pdo->query("
                SELECT registration_status, COUNT(*) as count
                FROM students
                GROUP BY registration_status
            ");
            $statusCounts = $stmt->fetchAll(PDO::FETCH_ASSOC);
            foreach ($statusCounts as $status) {
                $stats['by_status'][$status['registration_status']] = $status['count'];
            }

            // By payment status
            $stmt = $this->pdo->query("
                SELECT payment_status, COUNT(*) as count
                FROM students
                GROUP BY payment_status
            ");
            $paymentCounts = $stmt->fetchAll(PDO::FETCH_ASSOC);
            foreach ($paymentCounts as $payment) {
                $stats['by_payment'][$payment['payment_status']] = $payment['count'];
            }

            // By event
            $stmt = $this->pdo->query("
                SELECT e.event_name, COUNT(s.id) as count
                FROM events e
                LEFT JOIN students s ON e.id = s.event_id
                GROUP BY e.id, e.event_name
            ");
            $eventCounts = $stmt->fetchAll(PDO::FETCH_ASSOC);
            foreach ($eventCounts as $event) {
                $stats['by_event'][$event['event_name']] = $event['count'];
            }

            return $stats;
        } catch (Exception $e) {
            error_log("Error getting student statistics: " . $e->getMessage());
            return [];
        }
    }

    /**
     * Update student status
     */
    public function updateStudentStatus($studentId, $newStatus) {
        try {
            $stmt = $this->pdo->prepare("
                UPDATE students
                SET registration_status = ?, updated_at = CURRENT_TIMESTAMP
                WHERE id = ?
            ");
            $stmt->execute([$newStatus, $studentId]);

            return $stmt->rowCount() > 0;
        } catch (Exception $e) {
            error_log("Error updating student status: " . $e->getMessage());
            return false;
        }
    }
}
?>
