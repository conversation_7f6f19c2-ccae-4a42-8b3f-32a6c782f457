<?php

class StudentService {
    private $pdo;
    
    public function __construct() {
        $this->pdo = get_pdo_connection();
    }
    
    /**
     * Get total registered students count
     */
    public function getTotalRegisteredCount() {
        try {
            $stmt = $this->pdo->query("SELECT COUNT(*) FROM students");
            return $stmt->fetchColumn();
        } catch (Exception $e) {
            error_log("Error getting total registered count: " . $e->getMessage());
            return 0;
        }
    }
    
    /**
     * Get student by NIC and event
     */
    public function getStudentByNicAndEvent($nic_number, $event_id) {
        try {
            $stmt = $this->pdo->prepare("
                SELECT s.*, e.event_name, e.event_date, dm.method_name as delivery_method
                FROM students s
                LEFT JOIN events e ON s.event_id = e.id
                LEFT JOIN delivery_methods dm ON s.delivery_method_id = dm.id
                WHERE s.nic_number = ? AND s.event_id = ?
            ");
            $stmt->execute([$nic_number, $event_id]);
            return $stmt->fetch(PDO::FETCH_ASSOC);
        } catch (Exception $e) {
            error_log("Error getting student by NIC and event: " . $e->getMessage());
            return null;
        }
    }
    
    /**
     * Get student by NIC (any event)
     */
    public function getStudentByNic($nic_number) {
        try {
            $stmt = $this->pdo->prepare("
                SELECT s.*, e.event_name, e.event_date, dm.method_name as delivery_method,
                       ps.photo_sequence_number, ps.completed_at as photo_completed_at
                FROM students s
                LEFT JOIN events e ON s.event_id = e.id
                LEFT JOIN delivery_methods dm ON s.delivery_method_id = dm.id
                LEFT JOIN photo_sessions ps ON s.id = ps.student_id
                WHERE s.nic_number = ?
                ORDER BY s.created_at DESC
                LIMIT 1
            ");
            $stmt->execute([$nic_number]);
            return $stmt->fetch(PDO::FETCH_ASSOC);
        } catch (Exception $e) {
            error_log("Error getting student by NIC: " . $e->getMessage());
            return null;
        }
    }
    
    /**
     * Create new student registration
     */
    public function createStudent($data) {
        try {
            $this->pdo->beginTransaction();
            
            // Generate invoice number
            $invoice_number = $this->generateInvoiceNumber();
            
            $stmt = $this->pdo->prepare("
                INSERT INTO students (
                    invoice_number, nic_number, full_name, email, phone_number, 
                    whatsapp_number, address, event_id, session, seat_number, 
                    delivery_method_id, delivery_address, total_amount, 
                    payment_status, registration_status, created_at
                ) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, NOW())
            ");
            
            $stmt->execute([
                $invoice_number,
                $data['nic_number'],
                $data['full_name'],
                $data['email'],
                $data['phone_number'],
                $data['whatsapp_number'],
                $data['address'],
                $data['event_id'],
                $data['session'],
                $data['seat_number'],
                $data['delivery_method_id'],
                $data['delivery_address'],
                $data['total_amount'],
                $data['payment_status'] ?? 'pending',
                $data['registration_status'] ?? 'registered'
            ]);
            
            $student_id = $this->pdo->lastInsertId();
            
            // Insert photo orders
            if (!empty($data['photo_orders'])) {
                $stmt = $this->pdo->prepare("
                    INSERT INTO student_photo_orders (student_id, photo_type_id, quantity, unit_price, total_price)
                    VALUES (?, ?, ?, ?, ?)
                ");
                
                foreach ($data['photo_orders'] as $order) {
                    $stmt->execute([
                        $student_id,
                        $order['photo_type_id'],
                        $order['quantity'],
                        $order['unit_price'],
                        $order['total_price']
                    ]);
                }
            }
            
            $this->pdo->commit();
            
            return [
                'success' => true,
                'student_id' => $student_id,
                'invoice_number' => $invoice_number
            ];
            
        } catch (Exception $e) {
            $this->pdo->rollBack();
            error_log("Error creating student: " . $e->getMessage());
            return [
                'success' => false,
                'error' => $e->getMessage()
            ];
        }
    }
    
    /**
     * Update student status
     */
    public function updateStudentStatus($student_id, $status) {
        try {
            $stmt = $this->pdo->prepare("
                UPDATE students 
                SET registration_status = ?, updated_at = NOW() 
                WHERE id = ?
            ");
            $stmt->execute([$status, $student_id]);
            return $stmt->rowCount() > 0;
        } catch (Exception $e) {
            error_log("Error updating student status: " . $e->getMessage());
            return false;
        }
    }
    
    /**
     * Get student photo orders
     */
    public function getStudentPhotoOrders($student_id) {
        try {
            $stmt = $this->pdo->prepare("
                SELECT spo.*, pt.type_name, pt.description
                FROM student_photo_orders spo
                JOIN photo_types pt ON spo.photo_type_id = pt.id
                WHERE spo.student_id = ?
                ORDER BY pt.type_name
            ");
            $stmt->execute([$student_id]);
            return $stmt->fetchAll(PDO::FETCH_ASSOC);
        } catch (Exception $e) {
            error_log("Error getting student photo orders: " . $e->getMessage());
            return [];
        }
    }
    
    /**
     * Get all students with filters
     */
    public function getStudents($filters = [], $limit = 50, $offset = 0) {
        try {
            $where_conditions = [];
            $params = [];
            
            if (!empty($filters['event_id'])) {
                $where_conditions[] = "s.event_id = ?";
                $params[] = $filters['event_id'];
            }
            
            if (!empty($filters['registration_status'])) {
                $where_conditions[] = "s.registration_status = ?";
                $params[] = $filters['registration_status'];
            }
            
            if (!empty($filters['payment_status'])) {
                $where_conditions[] = "s.payment_status = ?";
                $params[] = $filters['payment_status'];
            }
            
            if (!empty($filters['search'])) {
                $where_conditions[] = "(s.full_name LIKE ? OR s.nic_number LIKE ? OR s.invoice_number LIKE ?)";
                $search_term = '%' . $filters['search'] . '%';
                $params[] = $search_term;
                $params[] = $search_term;
                $params[] = $search_term;
            }
            
            $where_clause = !empty($where_conditions) ? 'WHERE ' . implode(' AND ', $where_conditions) : '';
            
            $sql = "
                SELECT s.*, e.event_name, dm.method_name as delivery_method
                FROM students s
                LEFT JOIN events e ON s.event_id = e.id
                LEFT JOIN delivery_methods dm ON s.delivery_method_id = dm.id
                {$where_clause}
                ORDER BY s.created_at DESC
                LIMIT ? OFFSET ?
            ";
            
            $params[] = $limit;
            $params[] = $offset;
            
            $stmt = $this->pdo->prepare($sql);
            $stmt->execute($params);
            return $stmt->fetchAll(PDO::FETCH_ASSOC);
            
        } catch (Exception $e) {
            error_log("Error getting students: " . $e->getMessage());
            return [];
        }
    }
    
    /**
     * Generate unique invoice number
     */
    private function generateInvoiceNumber() {
        $prefix = 'INV-' . date('Y') . '-';
        $max_attempts = 10;
        
        for ($i = 0; $i < $max_attempts; $i++) {
            $number = $prefix . str_pad(rand(1, 99999), 5, '0', STR_PAD_LEFT);
            
            // Check if this invoice number already exists
            $stmt = $this->pdo->prepare("SELECT COUNT(*) FROM students WHERE invoice_number = ?");
            $stmt->execute([$number]);
            
            if ($stmt->fetchColumn() == 0) {
                return $number;
            }
        }
        
        // Fallback: use timestamp
        return $prefix . time();
    }
    
    /**
     * Get student statistics
     */
    public function getStudentStatistics() {
        try {
            $stats = [];
            
            // Total registered
            $stmt = $this->pdo->query("SELECT COUNT(*) FROM students");
            $stats['total_registered'] = $stmt->fetchColumn();
            
            // By status
            $stmt = $this->pdo->query("
                SELECT registration_status, COUNT(*) as count 
                FROM students 
                GROUP BY registration_status
            ");
            $status_counts = $stmt->fetchAll(PDO::FETCH_ASSOC);
            
            foreach ($status_counts as $status) {
                $stats['by_status'][$status['registration_status']] = $status['count'];
            }
            
            // By payment status
            $stmt = $this->pdo->query("
                SELECT payment_status, COUNT(*) as count 
                FROM students 
                GROUP BY payment_status
            ");
            $payment_counts = $stmt->fetchAll(PDO::FETCH_ASSOC);
            
            foreach ($payment_counts as $payment) {
                $stats['by_payment'][$payment['payment_status']] = $payment['count'];
            }
            
            // Total revenue
            $stmt = $this->pdo->query("
                SELECT SUM(total_amount) as total_revenue 
                FROM students 
                WHERE payment_status = 'paid'
            ");
            $stats['total_revenue'] = $stmt->fetchColumn() ?: 0;
            
            return $stats;
            
        } catch (Exception $e) {
            error_log("Error getting student statistics: " . $e->getMessage());
            return [];
        }
    }
}
