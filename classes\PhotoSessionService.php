<?php
/**
 * Photo Session Service Class
 * Handles photo session management and tracking
 */

class PhotoSessionService {
    private $db;
    
    public function __construct() {
        $this->db = DatabaseConfig::getLocalConnection();
    }
    
    /**
     * Record a completed photo session
     */
    public function recordPhotoSession($data) {
        try {
            // Validate required fields
            $required_fields = ['invoice_number', 'student_nic', 'student_name', 'photo_sequence_number', 'operator_id'];
            foreach ($required_fields as $field) {
                if (empty($data[$field])) {
                    return [
                        'success' => false,
                        'message' => "Missing required field: {$field}"
                    ];
                }
            }
            
            // Check if session already exists
            if ($this->sessionExists($data['invoice_number'])) {
                return [
                    'success' => false,
                    'message' => 'Photo session already recorded for this invoice'
                ];
            }
            
            // Validate photo sequence number (numeric only)
            if (!is_numeric($data['photo_sequence_number'])) {
                return [
                    'success' => false,
                    'message' => 'Photo sequence number must be numeric'
                ];
            }
            
            // Insert photo session record
            $stmt = $this->db->prepare("
                INSERT INTO photo_sessions (
                    invoice_number, student_nic, student_name, student_address,
                    whatsapp_number, alternate_phone, session, seat_number,
                    photo_types, photo_sequence_number, operator_id, notes
                ) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
            ");
            
            $photo_types_json = json_encode($data['photo_types'] ?? []);
            
            $stmt->execute([
                $data['invoice_number'],
                $data['student_nic'],
                $data['student_name'],
                $data['student_address'] ?? '',
                $data['whatsapp_number'] ?? '',
                $data['alternate_phone'] ?? '',
                $data['session'] ?? '',
                $data['seat_number'] ?? '',
                $photo_types_json,
                $data['photo_sequence_number'],
                $data['operator_id'],
                $data['notes'] ?? ''
            ]);
            
            $session_id = $this->db->lastInsertId();
            
            // Log the activity
            log_activity(
                'PHOTO_SESSION_RECORDED',
                "Photo session recorded for invoice: {$data['invoice_number']}, sequence: {$data['photo_sequence_number']}",
                $data['operator_id']
            );
            
            return [
                'success' => true,
                'message' => 'Photo session recorded successfully',
                'session_id' => $session_id
            ];
            
        } catch (Exception $e) {
            error_log("Error recording photo session: " . $e->getMessage());
            return [
                'success' => false,
                'message' => 'Error recording photo session'
            ];
        }
    }
    
    /**
     * Check if photo session already exists
     */
    private function sessionExists($invoiceNumber) {
        $stmt = $this->db->prepare("SELECT id FROM photo_sessions WHERE invoice_number = ?");
        $stmt->execute([$invoiceNumber]);
        return $stmt->fetch() !== false;
    }
    
    /**
     * Get completed sessions count
     */
    public function getCompletedSessionsCount() {
        try {
            $stmt = $this->db->prepare("SELECT COUNT(*) as total FROM photo_sessions");
            $stmt->execute();
            $result = $stmt->fetch();
            
            return $result['total'];
        } catch (Exception $e) {
            error_log("Error getting completed sessions count: " . $e->getMessage());
            return 0;
        }
    }
    
    /**
     * Get sessions completed today
     */
    public function getTodaySessionsCount() {
        try {
            $stmt = $this->db->prepare("
                SELECT COUNT(*) as total 
                FROM photo_sessions 
                WHERE DATE(completed_at) = CURDATE()
            ");
            $stmt->execute();
            $result = $stmt->fetch();
            
            return $result['total'];
        } catch (Exception $e) {
            error_log("Error getting today's sessions count: " . $e->getMessage());
            return 0;
        }
    }
    
    /**
     * Get sessions by operator
     */
    public function getSessionsByOperator($operatorId, $date = null) {
        try {
            $whereClause = "operator_id = ?";
            $params = [$operatorId];
            
            if ($date) {
                $whereClause .= " AND DATE(completed_at) = ?";
                $params[] = $date;
            }
            
            $stmt = $this->db->prepare("
                SELECT 
                    ps.*,
                    o.full_name as operator_name
                FROM photo_sessions ps
                JOIN operators o ON ps.operator_id = o.id
                WHERE {$whereClause}
                ORDER BY ps.completed_at DESC
            ");
            $stmt->execute($params);
            
            return $stmt->fetchAll();
        } catch (Exception $e) {
            error_log("Error getting sessions by operator: " . $e->getMessage());
            return [];
        }
    }
    
    /**
     * Get all photo sessions with filtering and pagination
     */
    public function getPhotoSessions($filters = [], $limit = 50, $offset = 0) {
        try {
            $whereConditions = ["1=1"];
            $params = [];
            
            // Apply filters
            if (!empty($filters['search'])) {
                $whereConditions[] = "(ps.student_name LIKE ? OR ps.student_nic LIKE ? OR ps.invoice_number LIKE ?)";
                $searchPattern = "%{$filters['search']}%";
                $params[] = $searchPattern;
                $params[] = $searchPattern;
                $params[] = $searchPattern;
            }
            
            if (!empty($filters['session'])) {
                $whereConditions[] = "ps.session = ?";
                $params[] = $filters['session'];
            }
            
            if (!empty($filters['operator_id'])) {
                $whereConditions[] = "ps.operator_id = ?";
                $params[] = $filters['operator_id'];
            }
            
            if (!empty($filters['date_from'])) {
                $whereConditions[] = "DATE(ps.completed_at) >= ?";
                $params[] = $filters['date_from'];
            }
            
            if (!empty($filters['date_to'])) {
                $whereConditions[] = "DATE(ps.completed_at) <= ?";
                $params[] = $filters['date_to'];
            }
            
            $whereClause = implode(" AND ", $whereConditions);
            
            $stmt = $this->db->prepare("
                SELECT 
                    ps.*,
                    o.full_name as operator_name,
                    o.username as operator_username
                FROM photo_sessions ps
                JOIN operators o ON ps.operator_id = o.id
                WHERE {$whereClause}
                ORDER BY ps.completed_at DESC
                LIMIT ? OFFSET ?
            ");
            
            $params[] = $limit;
            $params[] = $offset;
            $stmt->execute($params);
            
            $sessions = $stmt->fetchAll();
            
            // Decode photo_types JSON for each session
            foreach ($sessions as &$session) {
                $session['photo_types'] = json_decode($session['photo_types'], true) ?? [];
            }
            
            return $sessions;
        } catch (Exception $e) {
            error_log("Error getting photo sessions: " . $e->getMessage());
            return [];
        }
    }
    
    /**
     * Get photo session statistics
     */
    public function getSessionStatistics() {
        try {
            $stats = [];
            
            // Total sessions
            $stmt = $this->db->prepare("SELECT COUNT(*) as total FROM photo_sessions");
            $stmt->execute();
            $stats['total_sessions'] = $stmt->fetch()['total'];
            
            // Today's sessions
            $stmt = $this->db->prepare("
                SELECT COUNT(*) as total 
                FROM photo_sessions 
                WHERE DATE(completed_at) = CURDATE()
            ");
            $stmt->execute();
            $stats['today_sessions'] = $stmt->fetch()['total'];
            
            // Sessions by operator today
            $stmt = $this->db->prepare("
                SELECT 
                    o.full_name,
                    COUNT(*) as session_count
                FROM photo_sessions ps
                JOIN operators o ON ps.operator_id = o.id
                WHERE DATE(ps.completed_at) = CURDATE()
                GROUP BY ps.operator_id, o.full_name
                ORDER BY session_count DESC
            ");
            $stmt->execute();
            $stats['operator_stats'] = $stmt->fetchAll();
            
            // Hourly distribution today
            $stmt = $this->db->prepare("
                SELECT 
                    HOUR(completed_at) as hour,
                    COUNT(*) as session_count
                FROM photo_sessions
                WHERE DATE(completed_at) = CURDATE()
                GROUP BY HOUR(completed_at)
                ORDER BY hour
            ");
            $stmt->execute();
            $stats['hourly_distribution'] = $stmt->fetchAll();
            
            return $stats;
        } catch (Exception $e) {
            error_log("Error getting session statistics: " . $e->getMessage());
            return [];
        }
    }
    
    /**
     * Export photo sessions to CSV
     */
    public function exportToCSV($filters = []) {
        $sessions = $this->getPhotoSessions($filters, 10000, 0); // Get all matching records
        
        $csv_data = [];
        $csv_data[] = [
            'Invoice Number', 'Student NIC', 'Student Name', 'Address',
            'WhatsApp', 'Alternate Phone', 'Session', 'Seat Number',
            'Photo Types', 'Sequence Number', 'Operator', 'Completed At', 'Notes'
        ];
        
        foreach ($sessions as $session) {
            $photo_types = is_array($session['photo_types']) ? 
                implode(', ', $session['photo_types']) : 
                $session['photo_types'];
                
            $csv_data[] = [
                $session['invoice_number'],
                $session['student_nic'],
                $session['student_name'],
                $session['student_address'],
                $session['whatsapp_number'],
                $session['alternate_phone'],
                $session['session'],
                $session['seat_number'],
                $photo_types,
                $session['photo_sequence_number'],
                $session['operator_name'],
                $session['completed_at'],
                $session['notes']
            ];
        }
        
        return $csv_data;
    }
}
?>
