[19-Jul-2025 12:38:08 Europe/Berlin] Error getting operators: SQLSTATE[23000]: Integrity constraint violation: 1052 Column 'is_active' in where clause is ambiguous
[19-Jul-2025 12:38:40 Europe/Berlin] Error getting operators: SQLSTATE[23000]: Integrity constraint violation: 1052 Column 'is_active' in where clause is ambiguous
[19-Jul-2025 12:38:46 Europe/Berlin] Error getting operators: SQLSTATE[23000]: Integrity constraint violation: 1052 Column 'is_active' in where clause is ambiguous
[19-Jul-2025 12:38:50 Europe/Berlin] Error getting operators: SQLSTATE[23000]: Integrity constraint violation: 1052 Column 'is_active' in where clause is ambiguous
[19-Jul-2025 14:15:02 Europe/Berlin] Error getting operators: SQLSTATE[23000]: Integrity constraint violation: 1052 Column 'is_active' in where clause is ambiguous
[19-Jul-2025 14:39:03 Europe/Berlin] Error getting operators: SQLSTATE[23000]: Integrity constraint violation: 1052 Column 'is_active' in where clause is ambiguous
[19-Jul-2025 14:39:07 Europe/Berlin] Error getting operators: SQLSTATE[23000]: Integrity constraint violation: 1052 Column 'is_active' in where clause is ambiguous
[19-Jul-2025 14:39:09 Europe/Berlin] Error getting operators: SQLSTATE[23000]: Integrity constraint violation: 1052 Column 'is_active' in where clause is ambiguous
[19-Jul-2025 14:39:12 Europe/Berlin] Error getting operators: SQLSTATE[23000]: Integrity constraint violation: 1052 Column 'is_active' in where clause is ambiguous
[19-Jul-2025 14:41:40 Europe/Berlin] Error getting operators: SQLSTATE[23000]: Integrity constraint violation: 1052 Column 'is_active' in where clause is ambiguous
[19-Jul-2025 14:42:54 Europe/Berlin] Error getting operators: SQLSTATE[23000]: Integrity constraint violation: 1052 Column 'is_active' in where clause is ambiguous
[19-Jul-2025 15:00:56 Europe/Berlin] Error getting operators: SQLSTATE[23000]: Integrity constraint violation: 1052 Column 'is_active' in where clause is ambiguous
[19-Jul-2025 15:02:44 Europe/Berlin] Error getting operators: SQLSTATE[23000]: Integrity constraint violation: 1052 Column 'is_active' in where clause is ambiguous
[19-Jul-2025 15:02:47 Europe/Berlin] Error getting operators: SQLSTATE[23000]: Integrity constraint violation: 1052 Column 'is_active' in where clause is ambiguous
[19-Jul-2025 15:02:53 Europe/Berlin] Error getting operators: SQLSTATE[23000]: Integrity constraint violation: 1052 Column 'is_active' in where clause is ambiguous
[19-Jul-2025 15:02:59 Europe/Berlin] Error getting operators: SQLSTATE[23000]: Integrity constraint violation: 1052 Column 'is_active' in where clause is ambiguous
[19-Jul-2025 16:47:47 Europe/Berlin] Error getting operators: SQLSTATE[23000]: Integrity constraint violation: 1052 Column 'is_active' in where clause is ambiguous
[19-Jul-2025 16:49:11 Europe/Berlin] Error getting operators: SQLSTATE[23000]: Integrity constraint violation: 1052 Column 'is_active' in where clause is ambiguous
[19-Jul-2025 16:51:23 Europe/Berlin] Error getting operators: SQLSTATE[23000]: Integrity constraint violation: 1052 Column 'is_active' in where clause is ambiguous
[19-Jul-2025 16:53:44 Europe/Berlin] Error getting operators: SQLSTATE[23000]: Integrity constraint violation: 1052 Column 'is_active' in where clause is ambiguous
[20-Jul-2025 15:56:13 Europe/Berlin] PHP Fatal error:  Uncaught Error: Call to undefined function get_pdo_connection() in D:\xampp\htdocs\siyerra\student\status-check.php:21
Stack trace:
#0 {main}
  thrown in D:\xampp\htdocs\siyerra\student\status-check.php on line 21
[20-Jul-2025 15:56:30 Europe/Berlin] PHP Fatal error:  Uncaught Error: Call to undefined function get_pdo_connection() in D:\xampp\htdocs\siyerra\student\registration.php:18
Stack trace:
#0 {main}
  thrown in D:\xampp\htdocs\siyerra\student\registration.php on line 18
[20-Jul-2025 16:00:46 Europe/Berlin] PHP Fatal error:  Uncaught Error: Call to undefined function get_pdo_connection() in D:\xampp\htdocs\siyerra\admin\photo-pricing.php:96
Stack trace:
#0 {main}
  thrown in D:\xampp\htdocs\siyerra\admin\photo-pricing.php on line 96
