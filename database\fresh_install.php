<?php
/**
 * Fresh Installation Script
 * For new installations without existing data
 */

// Security check
$allowed_ips = ['127.0.0.1', '::1', 'localhost'];
if (!in_array($_SERVER['REMOTE_ADDR'], $allowed_ips) && !in_array($_SERVER['HTTP_HOST'], ['localhost', '127.0.0.1'])) {
    die('Access denied. Installation can only be run from localhost.');
}

// Include configuration
require_once '../config/database.php';

// Set execution time limit
set_time_limit(300);

$installation_log = [];
$errors = [];

function log_message($message, $type = 'info') {
    global $installation_log;
    $installation_log[] = [
        'time' => date('Y-m-d H:i:s'),
        'type' => $type,
        'message' => $message
    ];
    
    if ($type === 'error') {
        global $errors;
        $errors[] = $message;
    }
}

// Start installation process
if ($_SERVER['REQUEST_METHOD'] === 'POST' && isset($_POST['start_installation'])) {
    log_message("Starting fresh installation...", 'info');
    
    try {
        // Connect to MySQL server (without database)
        $pdo = new PDO(
            "mysql:host=" . DB_HOST . ";charset=utf8mb4",
            DB_USER,
            DB_PASS,
            [
                PDO::ATTR_ERRMODE => PDO::ERRMODE_EXCEPTION,
                PDO::ATTR_DEFAULT_FETCH_MODE => PDO::FETCH_ASSOC,
                PDO::MYSQL_ATTR_INIT_COMMAND => "SET NAMES utf8mb4"
            ]
        );
        
        log_message("Connected to MySQL server", 'success');
        
        // Step 1: Create database
        log_message("Creating database '" . DB_NAME . "'...", 'info');
        $pdo->exec("CREATE DATABASE IF NOT EXISTS `" . DB_NAME . "` DEFAULT CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci");
        $pdo->exec("USE `" . DB_NAME . "`");
        log_message("Database created and selected", 'success');
        
        // Step 2: Create tables
        log_message("Creating database tables...", 'info');
        
        // Events table
        $pdo->exec("
            CREATE TABLE IF NOT EXISTS `events` (
              `id` int(11) NOT NULL AUTO_INCREMENT,
              `event_name` varchar(100) NOT NULL,
              `event_code` varchar(20) NOT NULL UNIQUE,
              `event_date` date DEFAULT NULL,
              `description` text,
              `is_active` tinyint(1) NOT NULL DEFAULT 1,
              `created_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP,
              `updated_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
              PRIMARY KEY (`id`),
              KEY `idx_event_code` (`event_code`),
              KEY `idx_is_active` (`is_active`)
            ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci
        ");
        log_message("Created events table", 'success');
        
        // Photo types table
        $pdo->exec("
            CREATE TABLE IF NOT EXISTS `photo_types` (
              `id` int(11) NOT NULL AUTO_INCREMENT,
              `type_name` varchar(100) NOT NULL,
              `type_code` varchar(20) NOT NULL,
              `description` text,
              `is_active` tinyint(1) NOT NULL DEFAULT 1,
              `created_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP,
              PRIMARY KEY (`id`),
              KEY `idx_type_code` (`type_code`),
              KEY `idx_is_active` (`is_active`)
            ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci
        ");
        log_message("Created photo_types table", 'success');
        
        // Delivery methods table
        $pdo->exec("
            CREATE TABLE IF NOT EXISTS `delivery_methods` (
              `id` int(11) NOT NULL AUTO_INCREMENT,
              `method_name` varchar(100) NOT NULL,
              `method_code` varchar(20) NOT NULL UNIQUE,
              `description` text,
              `additional_cost` decimal(10,2) NOT NULL DEFAULT 0.00,
              `is_active` tinyint(1) NOT NULL DEFAULT 1,
              `created_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP,
              PRIMARY KEY (`id`),
              KEY `idx_method_code` (`method_code`),
              KEY `idx_is_active` (`is_active`)
            ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci
        ");
        log_message("Created delivery_methods table", 'success');
        
        // Operators table
        $pdo->exec("
            CREATE TABLE IF NOT EXISTS `operators` (
              `id` int(11) NOT NULL AUTO_INCREMENT,
              `username` varchar(50) NOT NULL UNIQUE,
              `password_hash` varchar(255) NOT NULL,
              `full_name` varchar(100) NOT NULL,
              `role` enum('admin','operator') NOT NULL DEFAULT 'operator',
              `is_active` tinyint(1) NOT NULL DEFAULT 1,
              `created_by` int(11) DEFAULT NULL,
              `created_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP,
              `updated_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
              `last_login` timestamp NULL DEFAULT NULL,
              PRIMARY KEY (`id`),
              KEY `idx_username` (`username`),
              KEY `idx_role` (`role`),
              KEY `fk_created_by` (`created_by`),
              CONSTRAINT `fk_operators_created_by` FOREIGN KEY (`created_by`) REFERENCES `operators` (`id`) ON DELETE SET NULL
            ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci
        ");
        log_message("Created operators table", 'success');
        
        // Event photo pricing table
        $pdo->exec("
            CREATE TABLE IF NOT EXISTS `event_photo_pricing` (
              `id` int(11) NOT NULL AUTO_INCREMENT,
              `event_id` int(11) NOT NULL,
              `photo_type_id` int(11) NOT NULL,
              `price` decimal(10,2) NOT NULL DEFAULT 0.00,
              `currency` varchar(3) NOT NULL DEFAULT 'LKR',
              `is_active` tinyint(1) NOT NULL DEFAULT 1,
              `created_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP,
              `updated_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
              PRIMARY KEY (`id`),
              UNIQUE KEY `unique_event_photo_type` (`event_id`, `photo_type_id`),
              KEY `fk_event_pricing` (`event_id`),
              KEY `fk_photo_type_pricing` (`photo_type_id`),
              CONSTRAINT `fk_event_pricing` FOREIGN KEY (`event_id`) REFERENCES `events` (`id`) ON DELETE CASCADE,
              CONSTRAINT `fk_photo_type_pricing` FOREIGN KEY (`photo_type_id`) REFERENCES `photo_types` (`id`) ON DELETE CASCADE
            ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci
        ");
        log_message("Created event_photo_pricing table", 'success');
        
        // Students table
        $pdo->exec("
            CREATE TABLE IF NOT EXISTS `students` (
              `id` int(11) NOT NULL AUTO_INCREMENT,
              `invoice_number` varchar(50) NOT NULL UNIQUE,
              `nic_number` varchar(20) NOT NULL,
              `full_name` varchar(150) NOT NULL,
              `email` varchar(100) DEFAULT NULL,
              `phone_number` varchar(20) DEFAULT NULL,
              `whatsapp_number` varchar(20) DEFAULT NULL,
              `address` text,
              `event_id` int(11) NOT NULL,
              `session` varchar(100) DEFAULT NULL,
              `seat_number` varchar(20) DEFAULT NULL,
              `delivery_method_id` int(11) NOT NULL,
              `delivery_address` text,
              `total_amount` decimal(10,2) NOT NULL DEFAULT 0.00,
              `payment_status` enum('pending','paid','failed','refunded') NOT NULL DEFAULT 'pending',
              `registration_status` enum('registered','photo_taken','in_development','dispatched','ready_for_pickup','completed') NOT NULL DEFAULT 'registered',
              `notes` text,
              `created_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP,
              `updated_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
              PRIMARY KEY (`id`),
              UNIQUE KEY `unique_nic_event` (`nic_number`, `event_id`),
              KEY `idx_invoice_number` (`invoice_number`),
              KEY `idx_nic_number` (`nic_number`),
              KEY `idx_registration_status` (`registration_status`),
              KEY `idx_payment_status` (`payment_status`),
              KEY `fk_student_event` (`event_id`),
              KEY `fk_student_delivery` (`delivery_method_id`),
              CONSTRAINT `fk_student_event` FOREIGN KEY (`event_id`) REFERENCES `events` (`id`) ON DELETE RESTRICT,
              CONSTRAINT `fk_student_delivery` FOREIGN KEY (`delivery_method_id`) REFERENCES `delivery_methods` (`id`) ON DELETE RESTRICT
            ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci
        ");
        log_message("Created students table", 'success');
        
        // Student photo orders table
        $pdo->exec("
            CREATE TABLE IF NOT EXISTS `student_photo_orders` (
              `id` int(11) NOT NULL AUTO_INCREMENT,
              `student_id` int(11) NOT NULL,
              `photo_type_id` int(11) NOT NULL,
              `quantity` int(11) NOT NULL DEFAULT 1,
              `unit_price` decimal(10,2) NOT NULL,
              `total_price` decimal(10,2) NOT NULL,
              `created_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP,
              PRIMARY KEY (`id`),
              KEY `fk_order_student` (`student_id`),
              KEY `fk_order_photo_type` (`photo_type_id`),
              CONSTRAINT `fk_order_student` FOREIGN KEY (`student_id`) REFERENCES `students` (`id`) ON DELETE CASCADE,
              CONSTRAINT `fk_order_photo_type` FOREIGN KEY (`photo_type_id`) REFERENCES `photo_types` (`id`) ON DELETE CASCADE
            ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci
        ");
        log_message("Created student_photo_orders table", 'success');
        
        // Photo sessions table
        $pdo->exec("
            CREATE TABLE IF NOT EXISTS `photo_sessions` (
              `id` int(11) NOT NULL AUTO_INCREMENT,
              `student_id` int(11) DEFAULT NULL,
              `operator_id` int(11) NOT NULL,
              `invoice_number` varchar(50) DEFAULT NULL,
              `student_name` varchar(150) DEFAULT NULL,
              `student_nic` varchar(20) DEFAULT NULL,
              `session` varchar(100) DEFAULT NULL,
              `photo_sequence_number` varchar(50) NOT NULL,
              `session_notes` text,
              `completed_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP,
              PRIMARY KEY (`id`),
              KEY `idx_sequence_number` (`photo_sequence_number`),
              KEY `idx_completed_at` (`completed_at`),
              KEY `idx_invoice_number` (`invoice_number`),
              KEY `idx_student_nic` (`student_nic`),
              KEY `fk_session_student` (`student_id`),
              KEY `fk_session_operator` (`operator_id`),
              CONSTRAINT `fk_session_student` FOREIGN KEY (`student_id`) REFERENCES `students` (`id`) ON DELETE SET NULL,
              CONSTRAINT `fk_session_operator` FOREIGN KEY (`operator_id`) REFERENCES `operators` (`id`) ON DELETE RESTRICT
            ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci
        ");
        log_message("Created photo_sessions table", 'success');
        
        // System settings table
        $pdo->exec("
            CREATE TABLE IF NOT EXISTS `system_settings` (
              `id` int(11) NOT NULL AUTO_INCREMENT,
              `setting_key` varchar(100) NOT NULL UNIQUE,
              `setting_value` text,
              `description` varchar(255) DEFAULT NULL,
              `updated_by` int(11) DEFAULT NULL,
              `updated_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
              PRIMARY KEY (`id`),
              KEY `idx_setting_key` (`setting_key`),
              KEY `fk_updated_by` (`updated_by`),
              CONSTRAINT `fk_settings_updated_by` FOREIGN KEY (`updated_by`) REFERENCES `operators` (`id`) ON DELETE SET NULL
            ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci
        ");
        log_message("Created system_settings table", 'success');
        
        // Session logs table
        $pdo->exec("
            CREATE TABLE IF NOT EXISTS `session_logs` (
              `id` int(11) NOT NULL AUTO_INCREMENT,
              `user_id` int(11) DEFAULT NULL,
              `operator_id` int(11) DEFAULT NULL,
              `username` varchar(50) DEFAULT NULL,
              `action` varchar(100) NOT NULL,
              `description` text,
              `details` text,
              `ip_address` varchar(45) DEFAULT NULL,
              `user_agent` text,
              `created_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP,
              PRIMARY KEY (`id`),
              KEY `idx_operator_id` (`operator_id`),
              KEY `idx_action` (`action`),
              KEY `idx_created_at` (`created_at`),
              CONSTRAINT `fk_logs_operator_id` FOREIGN KEY (`operator_id`) REFERENCES `operators` (`id`) ON DELETE SET NULL
            ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci
        ");
        log_message("Created session_logs table", 'success');
        
        // Step 3: Insert default data
        log_message("Inserting default data...", 'info');
        
        // Default admin user
        $pdo->exec("INSERT IGNORE INTO `operators` (`username`, `password_hash`, `full_name`, `role`, `is_active`) VALUES
                   ('admin', '\$2y\$10\$92IXUNpkjO0rOQ5byMi.Ye4oKoEa3Ro9llC/.og/at2.uheWG/igi', 'System Administrator', 'admin', 1)");
        log_message("Created default admin user", 'success');
        
        // Default events
        $pdo->exec("INSERT IGNORE INTO `events` (`event_name`, `event_code`, `event_date`, `description`) VALUES
                   ('University Convocation 2024', 'CONV2024', '2024-12-15', 'Annual university convocation ceremony'),
                   ('Graduation Ceremony 2024', 'GRAD2024', '2024-11-30', 'Faculty graduation ceremony'),
                   ('Orientation Program 2024', 'ORIENT2024', '2024-02-15', 'New student orientation program')");
        log_message("Created default events", 'success');
        
        // Default photo types
        $pdo->exec("INSERT IGNORE INTO `photo_types` (`type_name`, `type_code`, `description`) VALUES
                   ('Single Full Photo', 'SINGLE_FULL', 'Individual full body photograph'),
                   ('Single Bust Photo', 'SINGLE_BUST', 'Individual upper body photograph'),
                   ('Stage Photo', 'STAGE', 'Photograph taken on stage during ceremony'),
                   ('Family Photo', 'FAMILY', 'Family group photograph'),
                   ('Couple Photo', 'COUPLE', 'Photograph with partner/spouse'),
                   ('Group Photo', 'GROUP', 'Group photograph with friends/colleagues')");
        log_message("Created default photo types", 'success');
        
        // Default delivery methods
        $pdo->exec("INSERT IGNORE INTO `delivery_methods` (`method_name`, `method_code`, `description`, `additional_cost`) VALUES
                   ('Courier Service', 'COURIER', 'Home delivery via courier service', 500.00),
                   ('Collection Center Pickup', 'PICKUP', 'Pickup from designated collection center', 0.00)");
        log_message("Created default delivery methods", 'success');
        
        // Default pricing
        $pdo->exec("INSERT IGNORE INTO `event_photo_pricing` (`event_id`, `photo_type_id`, `price`) VALUES
                   (1, 1, 1500.00), (1, 2, 1200.00), (1, 3, 2000.00), (1, 4, 3000.00), (1, 5, 2500.00), (1, 6, 3500.00),
                   (2, 1, 1400.00), (2, 2, 1100.00), (2, 3, 1800.00), (2, 4, 2800.00), (2, 5, 2300.00), (2, 6, 3200.00),
                   (3, 1, 1200.00), (3, 2, 1000.00), (3, 3, 1600.00), (3, 4, 2500.00), (3, 5, 2000.00), (3, 6, 2800.00)");
        log_message("Created default pricing", 'success');
        
        // Default system settings
        $pdo->exec("INSERT IGNORE INTO `system_settings` (`setting_key`, `setting_value`, `description`) VALUES
                   ('app_name', 'Photo Management System', 'Application name'),
                   ('session_timeout', '3600', 'Session timeout in seconds'),
                   ('max_login_attempts', '5', 'Maximum login attempts before lockout'),
                   ('payment_gateway_enabled', '0', 'Enable payment gateway integration'),
                   ('default_currency', 'LKR', 'Default currency for pricing')");
        log_message("Created default system settings", 'success');
        
        log_message("Fresh installation completed successfully!", 'success');
        
    } catch (Exception $e) {
        log_message("Installation failed: " . $e->getMessage(), 'error');
    }
}
?>
<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Fresh Installation - Photo Management System</title>
    <link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/bulma@0.9.4/css/bulma.min.css">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css">
    <style>
        .install-header {
            background: linear-gradient(135deg, #48c78e 0%, #00d1b2 100%);
            color: white;
            padding: 2rem;
            border-radius: 8px;
            margin-bottom: 2rem;
        }
        .log-entry {
            padding: 0.5rem;
            margin-bottom: 0.25rem;
            border-radius: 4px;
            font-family: monospace;
            font-size: 0.875rem;
        }
        .log-info { background-color: #f0f8ff; }
        .log-success { background-color: #f0fff0; color: #006400; }
        .log-warning { background-color: #fff8dc; color: #b8860b; }
        .log-error { background-color: #ffe4e1; color: #dc143c; }
    </style>
</head>
<body>
    <div class="container mt-4">
        <!-- Header -->
        <div class="install-header">
            <h1 class="title is-2 has-text-white">
                <i class="fas fa-rocket mr-3"></i>
                Fresh Installation
            </h1>
            <p class="subtitle is-4 has-text-white">
                Install Photo Management System from scratch
            </p>
        </div>

        <!-- Installation Status -->
        <?php if (!empty($installation_log)): ?>
            <div class="box">
                <h2 class="title is-5">
                    <i class="fas fa-list mr-2"></i>
                    Installation Log
                </h2>
                
                <?php if (empty($errors)): ?>
                    <div class="notification is-success">
                        <i class="fas fa-check-circle mr-2"></i>
                        Installation completed successfully!
                    </div>
                <?php else: ?>
                    <div class="notification is-danger">
                        <i class="fas fa-exclamation-circle mr-2"></i>
                        Installation failed with <?php echo count($errors); ?> error(s).
                    </div>
                <?php endif; ?>
                
                <div style="max-height: 400px; overflow-y: auto; border: 1px solid #ddd; padding: 1rem;">
                    <?php foreach ($installation_log as $entry): ?>
                        <div class="log-entry log-<?php echo $entry['type']; ?>">
                            <strong>[<?php echo $entry['time']; ?>]</strong> 
                            <?php echo htmlspecialchars($entry['message']); ?>
                        </div>
                    <?php endforeach; ?>
                </div>
            </div>
        <?php endif; ?>

        <!-- Installation Form -->
        <?php if (empty($installation_log) || !empty($errors)): ?>
            <div class="box">
                <h2 class="title is-5">
                    <i class="fas fa-play mr-2"></i>
                    Start Fresh Installation
                </h2>
                
                <div class="content">
                    <p>This will create a completely new installation with:</p>
                    <ul>
                        <li>✅ New database with unified structure</li>
                        <li>✅ All required tables and relationships</li>
                        <li>✅ Default admin user (username: admin, password: password)</li>
                        <li>✅ Sample events and photo types</li>
                        <li>✅ Default pricing structure</li>
                        <li>✅ System configuration</li>
                    </ul>
                </div>
                
                <form method="POST" action="">
                    <div class="field">
                        <div class="control">
                            <label class="checkbox">
                                <input type="checkbox" required>
                                I understand this will create a new database and I have verified my database configuration
                            </label>
                        </div>
                    </div>
                    
                    <div class="field">
                        <div class="control">
                            <button class="button is-success is-large" type="submit" name="start_installation">
                                <i class="fas fa-rocket mr-2"></i>
                                Start Fresh Installation
                            </button>
                        </div>
                    </div>
                </form>
            </div>
        <?php endif; ?>

        <!-- Success Actions -->
        <?php if (!empty($installation_log) && empty($errors)): ?>
            <div class="box">
                <h2 class="title is-5">
                    <i class="fas fa-check-circle mr-2"></i>
                    Installation Complete!
                </h2>
                
                <div class="content">
                    <p>Your Photo Management System is now ready to use!</p>
                    
                    <h3>Default Login Credentials:</h3>
                    <ul>
                        <li><strong>Username:</strong> admin</li>
                        <li><strong>Password:</strong> password</li>
                    </ul>
                    
                    <p><strong>Important:</strong> Please change the default password after your first login.</p>
                </div>
                
                <div class="buttons">
                    <a href="../admin/dashboard.php" class="button is-primary is-large">
                        <i class="fas fa-tachometer-alt mr-2"></i>
                        Go to Admin Dashboard
                    </a>
                    <a href="../student/index.php" class="button is-info">
                        <i class="fas fa-user-graduate mr-2"></i>
                        Student Portal
                    </a>
                    <a href="../login.php" class="button is-success">
                        <i class="fas fa-sign-in-alt mr-2"></i>
                        Login Page
                    </a>
                </div>
            </div>
        <?php endif; ?>

        <!-- Footer -->
        <div class="has-text-centered mt-6 mb-4">
            <p class="has-text-grey">
                <i class="fas fa-shield-alt mr-2"></i>
                Photo Management System - Fresh Installation Tool
            </p>
        </div>
    </div>
</body>
</html>
