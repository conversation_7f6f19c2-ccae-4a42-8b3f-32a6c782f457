<?php
/**
 * Authentication Class
 * Handles user authentication, session management, and security
 */

class Auth {
    private $db;
    
    public function __construct() {
        $this->db = DatabaseConfig::getUnifiedConnection();
    }
    
    /**
     * Authenticate user login
     */
    public function login($username, $password) {
        try {
            // Check for too many failed attempts
            if ($this->isAccountLocked($username)) {
                return [
                    'success' => false,
                    'message' => 'Account is temporarily locked due to too many failed attempts. Please try again later.'
                ];
            }
            
            // Get user from database
            $stmt = $this->db->prepare("
                SELECT id, username, password_hash, full_name, role, is_active 
                FROM operators 
                WHERE username = ?
            ");
            $stmt->execute([$username]);
            $user = $stmt->fetch();
            
            if (!$user) {
                $this->recordFailedAttempt($username);
                return [
                    'success' => false,
                    'message' => 'Invalid username or password'
                ];
            }
            
            if (!$user['is_active']) {
                return [
                    'success' => false,
                    'message' => 'Account is disabled. Please contact administrator.'
                ];
            }
            
            // Verify password
            if (!password_verify($password, $user['password_hash'])) {
                $this->recordFailedAttempt($username);
                return [
                    'success' => false,
                    'message' => 'Invalid username or password'
                ];
            }
            
            // Clear failed attempts on successful login
            $this->clearFailedAttempts($username);
            
            // Create session
            $this->createSession($user);
            
            // Update last login
            $this->updateLastLogin($user['id']);
            
            // Log successful login
            log_activity('LOGIN_SUCCESS', "User {$username} logged in successfully", $user['id']);

            // Check for redirect after login
            $redirect_url = '';
            if (isset($_SESSION['redirect_after_login'])) {
                $redirect_url = $_SESSION['redirect_after_login'];
                unset($_SESSION['redirect_after_login']);

                // Validate redirect URL to prevent open redirects
                if ($this->isValidRedirectUrl($redirect_url, $user['role'])) {
                    // Remove the base path to get relative URL
                    $redirect_url = str_replace(BASE_URL, '', $redirect_url);
                } else {
                    // Invalid redirect, use default
                    $redirect_url = $user['role'] === 'admin' ? 'app/dashboard.php' : 'operator/dashboard.php';
                }
            } else {
                // Default redirect based on role
                $redirect_url = $user['role'] === 'admin' ? 'app/dashboard.php' : 'operator/dashboard.php';
            }

            return [
                'success' => true,
                'message' => 'Login successful',
                'redirect' => $redirect_url
            ];
            
        } catch (Exception $e) {
            error_log("Login error: " . $e->getMessage());
            return [
                'success' => false,
                'message' => 'An error occurred during login. Please try again.'
            ];
        }
    }
    
    /**
     * Create user session
     */
    private function createSession($user) {
        session_regenerate_id(true);
        $_SESSION['user_id'] = $user['id'];
        $_SESSION['username'] = $user['username'];
        $_SESSION['full_name'] = $user['full_name'];
        $_SESSION['role'] = $user['role'];
        $_SESSION['last_activity'] = time();
        $_SESSION['csrf_token'] = bin2hex(random_bytes(32));
    }
    
    /**
     * Logout user
     */
    public function logout() {
        if (isset($_SESSION['user_id'])) {
            log_activity('LOGOUT', "User logged out", $_SESSION['user_id']);
        }
        
        session_destroy();
        return [
            'success' => true,
            'message' => 'Logged out successfully'
        ];
    }
    
    /**
     * Check if account is locked due to failed attempts
     */
    private function isAccountLocked($username) {
        $stmt = $this->db->prepare("
            SELECT COUNT(*) as attempt_count, MAX(created_at) as last_attempt
            FROM session_logs 
            WHERE action = 'LOGIN_FAILED' 
            AND details LIKE ? 
            AND created_at > DATE_SUB(NOW(), INTERVAL 15 MINUTE)
        ");
        $stmt->execute(["%username: {$username}%"]);
        $result = $stmt->fetch();
        
        return $result['attempt_count'] >= MAX_LOGIN_ATTEMPTS;
    }
    
    /**
     * Record failed login attempt
     */
    private function recordFailedAttempt($username) {
        log_activity('LOGIN_FAILED', "Failed login attempt for username: {$username}");
    }
    
    /**
     * Clear failed login attempts
     */
    private function clearFailedAttempts($username) {
        // This is handled by the time-based check in isAccountLocked
        // Failed attempts older than 15 minutes are automatically ignored
    }
    
    /**
     * Update last login timestamp
     */
    private function updateLastLogin($userId) {
        $stmt = $this->db->prepare("UPDATE operators SET last_login = NOW() WHERE id = ?");
        $stmt->execute([$userId]);
    }
    
    /**
     * Change user password
     */
    public function changePassword($userId, $currentPassword, $newPassword) {
        try {
            // Get current password hash
            $stmt = $this->db->prepare("SELECT password_hash FROM operators WHERE id = ?");
            $stmt->execute([$userId]);
            $user = $stmt->fetch();
            
            if (!$user) {
                return ['success' => false, 'message' => 'User not found'];
            }
            
            // Verify current password
            if (!password_verify($currentPassword, $user['password_hash'])) {
                return ['success' => false, 'message' => 'Current password is incorrect'];
            }
            
            // Validate new password
            if (strlen($newPassword) < 6) {
                return ['success' => false, 'message' => 'New password must be at least 6 characters long'];
            }
            
            // Update password
            $newPasswordHash = password_hash($newPassword, PASSWORD_DEFAULT);
            $stmt = $this->db->prepare("UPDATE operators SET password_hash = ? WHERE id = ?");
            $stmt->execute([$newPasswordHash, $userId]);
            
            log_activity('PASSWORD_CHANGED', "Password changed successfully", $userId);
            
            return ['success' => true, 'message' => 'Password changed successfully'];
            
        } catch (Exception $e) {
            error_log("Password change error: " . $e->getMessage());
            return ['success' => false, 'message' => 'An error occurred while changing password'];
        }
    }
    
    /**
     * Reset user password (admin only)
     */
    public function resetPassword($userId, $newPassword) {
        try {
            $newPasswordHash = password_hash($newPassword, PASSWORD_DEFAULT);
            $stmt = $this->db->prepare("UPDATE operators SET password_hash = ? WHERE id = ?");
            $stmt->execute([$newPasswordHash, $userId]);

            log_activity('PASSWORD_RESET', "Password reset for user ID: {$userId}");

            return ['success' => true, 'message' => 'Password reset successfully'];

        } catch (Exception $e) {
            error_log("Password reset error: " . $e->getMessage());
            return ['success' => false, 'message' => 'An error occurred while resetting password'];
        }
    }

    /**
     * Validate redirect URL to prevent open redirects
     */
    private function isValidRedirectUrl($url, $userRole) {
        // Remove any leading slash or base URL
        $url = ltrim($url, '/');
        $url = str_replace(BASE_URL, '', $url);

        // Define allowed paths for each role
        $allowedPaths = [
            'admin' => [
                'app/',
                'admin/'  // Legacy admin folder if it exists
            ],
            'operator' => [
                'operator/'
            ]
        ];

        // Check if URL starts with an allowed path for the user's role
        if (isset($allowedPaths[$userRole])) {
            foreach ($allowedPaths[$userRole] as $allowedPath) {
                if (strpos($url, $allowedPath) === 0) {
                    return true;
                }
            }
        }

        return false;
    }
}
?>
