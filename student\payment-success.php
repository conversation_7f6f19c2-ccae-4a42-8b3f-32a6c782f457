<?php
require_once '../config/config.php';

if (session_status() === PHP_SESSION_NONE) {
    session_start();
}

// Check if payment success data exists
if (!isset($_SESSION['payment_success'])) {
    redirect('registration.php');
}

$payment_data = $_SESSION['payment_success'];
$payment_method = $payment_data['method'];
$payment_status = $payment_data['status'] ?? 'paid';

// Clear the success data after displaying
unset($_SESSION['payment_success']);
?>
<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Payment Successful - <?php echo APP_NAME; ?></title>
    <link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/bulma@0.9.4/css/bulma.min.css">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css">
    <style>
        .hero-section {
            background: linear-gradient(135deg, #48c78e 0%, #00d1b2 100%);
            min-height: 100vh;
        }
        .success-card {
            background: white;
            border-radius: 15px;
            box-shadow: 0 10px 30px rgba(0,0,0,0.1);
            overflow: hidden;
        }
        .success-icon {
            font-size: 4rem;
            color: #48c78e;
            margin-bottom: 2rem;
        }
        .invoice-box {
            background: #f8f9fa;
            border: 2px solid #e9ecef;
            border-radius: 12px;
            padding: 1.5rem;
            margin: 2rem 0;
            text-align: center;
        }
        .status-badge {
            font-size: 1rem;
            padding: 0.5rem 1rem;
            border-radius: 20px;
        }
        .next-steps {
            background: #e8f4fd;
            border: 1px solid #bee5eb;
            border-radius: 8px;
            padding: 1.5rem;
            margin: 2rem 0;
        }
        @media print {
            .no-print {
                display: none;
            }
            .hero-section {
                background: white !important;
                color: black !important;
            }
            .success-card {
                box-shadow: none;
                border: 1px solid #ddd;
            }
        }
    </style>
</head>
<body>
    <section class="hero hero-section">
        <div class="hero-body">
            <div class="container">
                <!-- Header -->
                <div class="has-text-centered mb-6">
                    <h1 class="title is-2 has-text-white">
                        <i class="fas fa-check-circle mr-3"></i>
                        Payment Successful!
                    </h1>
                    <p class="subtitle is-4 has-text-white">
                        Your booking has been confirmed
                    </p>
                </div>

                <!-- Success Card -->
                <div class="columns is-centered">
                    <div class="column is-8">
                        <div class="box success-card">
                            
                            <!-- Success Message -->
                            <div class="has-text-centered">
                                <div class="success-icon">
                                    <i class="fas fa-check-circle"></i>
                                </div>
                                
                                <?php if ($payment_status === 'paid'): ?>
                                    <h2 class="title is-3 has-text-success">Payment Completed!</h2>
                                    <p class="subtitle is-5">Your payment has been processed successfully.</p>
                                <?php elseif ($payment_status === 'pending_verification'): ?>
                                    <h2 class="title is-3 has-text-info">Payment Submitted!</h2>
                                    <p class="subtitle is-5">Your bank transfer is being verified.</p>
                                <?php elseif ($payment_status === 'pay_at_event'): ?>
                                    <h2 class="title is-3 has-text-warning">Booking Confirmed!</h2>
                                    <p class="subtitle is-5">Remember to pay at the event venue.</p>
                                <?php endif; ?>
                            </div>
                            
                            <!-- Payment Details -->
                            <div class="invoice-box">
                                <h3 class="title is-5">
                                    <i class="fas fa-receipt mr-2"></i>
                                    Payment Details
                                </h3>
                                
                                <div class="columns">
                                    <div class="column">
                                        <p><strong>Invoice Number:</strong></p>
                                        <p class="title is-4 has-text-primary"><?php echo htmlspecialchars($payment_data['invoice_number']); ?></p>
                                    </div>
                                    <div class="column">
                                        <p><strong>Amount:</strong></p>
                                        <p class="title is-4 has-text-success">LKR <?php echo number_format($payment_data['amount'], 2); ?></p>
                                    </div>
                                    <div class="column">
                                        <p><strong>Payment Method:</strong></p>
                                        <p class="title is-6"><?php echo htmlspecialchars($payment_method); ?></p>
                                    </div>
                                </div>
                                
                                <div class="has-text-centered mt-4">
                                    <p><strong>Payment Status:</strong></p>
                                    <?php if ($payment_status === 'paid'): ?>
                                        <span class="status-badge tag is-success is-large">
                                            <i class="fas fa-check mr-2"></i>Paid
                                        </span>
                                    <?php elseif ($payment_status === 'pending_verification'): ?>
                                        <span class="status-badge tag is-warning is-large">
                                            <i class="fas fa-clock mr-2"></i>Pending Verification
                                        </span>
                                    <?php elseif ($payment_status === 'pay_at_event'): ?>
                                        <span class="status-badge tag is-info is-large">
                                            <i class="fas fa-calendar-check mr-2"></i>Pay at Event
                                        </span>
                                    <?php endif; ?>
                                </div>
                            </div>
                            
                            <!-- Next Steps -->
                            <div class="next-steps">
                                <h3 class="title is-5 has-text-info">
                                    <i class="fas fa-list-check mr-2"></i>
                                    What's Next?
                                </h3>
                                
                                <?php if ($payment_status === 'paid'): ?>
                                    <div class="content">
                                        <ol>
                                            <li><strong>Save this page:</strong> Print or screenshot this confirmation</li>
                                            <li><strong>Check your email:</strong> You'll receive a confirmation email shortly</li>
                                            <li><strong>Attend the event:</strong> Bring your invoice number and ID</li>
                                            <li><strong>Photo session:</strong> Follow the event schedule for your session</li>
                                            <li><strong>Photo delivery:</strong> Photos will be delivered as per your selected method</li>
                                        </ol>
                                    </div>
                                <?php elseif ($payment_status === 'pending_verification'): ?>
                                    <div class="content">
                                        <ol>
                                            <li><strong>Verification process:</strong> We'll verify your bank transfer within 1-2 business days</li>
                                            <li><strong>Email notification:</strong> You'll receive confirmation once verified</li>
                                            <li><strong>Save this page:</strong> Keep this as proof of submission</li>
                                            <li><strong>Contact us:</strong> If not verified within 2 days, please contact support</li>
                                        </ol>
                                    </div>
                                <?php elseif ($payment_status === 'pay_at_event'): ?>
                                    <div class="content">
                                        <ol>
                                            <li><strong>Print this page:</strong> Bring this confirmation to the event</li>
                                            <li><strong>Bring cash:</strong> Exact amount - LKR <?php echo number_format($payment_data['amount'], 2); ?></li>
                                            <li><strong>Bring ID:</strong> Valid NIC or Passport required</li>
                                            <li><strong>Arrive early:</strong> Allow extra time for payment processing</li>
                                            <li><strong>Find payment counter:</strong> Look for "Photo Payment Counter" at the venue</li>
                                        </ol>
                                    </div>
                                <?php endif; ?>
                            </div>
                            
                            <!-- Important Information -->
                            <div class="notification is-info is-light">
                                <h4 class="title is-6">
                                    <i class="fas fa-info-circle mr-2"></i>
                                    Important Information
                                </h4>
                                <ul class="is-size-7">
                                    <li>Keep your invoice number safe - you'll need it for photo collection</li>
                                    <li>Bring a valid ID (NIC or Passport) to the event</li>
                                    <li>Follow the event schedule for your photo session timing</li>
                                    <li>Contact support if you have any questions: <EMAIL></li>
                                </ul>
                            </div>
                            
                            <!-- Action Buttons -->
                            <div class="buttons is-centered no-print">
                                <button class="button is-primary is-large" onclick="window.print()">
                                    <i class="fas fa-print mr-2"></i>
                                    Print Confirmation
                                </button>
                                
                                <a href="registration.php" class="button is-info is-large">
                                    <i class="fas fa-plus mr-2"></i>
                                    New Registration
                                </a>
                                
                                <a href="payment.php" class="button is-light is-large">
                                    <i class="fas fa-search mr-2"></i>
                                    Check Another Booking
                                </a>
                            </div>
                        </div>
                    </div>
                </div>
                
                <!-- Footer -->
                <div class="has-text-centered mt-6 no-print">
                    <p class="has-text-white-ter">
                        <i class="fas fa-shield-alt mr-2"></i>
                        Thank you for choosing our photo services!
                    </p>
                </div>
            </div>
        </div>
    </section>

    <script>
        // Auto-print option (optional)
        document.addEventListener('DOMContentLoaded', function() {
            // Uncomment the line below if you want to auto-print the confirmation
            // setTimeout(() => window.print(), 1000);
            
            // Add some celebration animation
            const successIcon = document.querySelector('.success-icon i');
            if (successIcon) {
                successIcon.style.animation = 'bounce 2s infinite';
            }
        });
        
        // Add bounce animation
        const style = document.createElement('style');
        style.textContent = `
            @keyframes bounce {
                0%, 20%, 50%, 80%, 100% {
                    transform: translateY(0);
                }
                40% {
                    transform: translateY(-10px);
                }
                60% {
                    transform: translateY(-5px);
                }
            }
        `;
        document.head.appendChild(style);
    </script>
</body>
</html>
