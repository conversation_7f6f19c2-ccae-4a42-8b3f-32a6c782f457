# University Convocation Photo Management System

A comprehensive full-stack web application built with PHP and MySQL to manage photo-taking sessions at university convocation events. The system supports two user roles (Admin and Operator) with QR code scanning capabilities for efficient photo session tracking.

## Features

### 🔐 Authentication & Security
- Session-based authentication with role-based access control
- CSRF protection on all forms
- Input sanitization and validation
- Account lockout after failed login attempts
- Secure password hashing

### 👨‍💼 Admin Features
- Real-time dashboard with statistics and charts
- Operator management (add, edit, disable accounts)
- Photo session records with filtering and search
- CSV export functionality
- System settings management
- Activity logging and audit trails

### 📱 Operator Features
- Mobile-friendly QR code scanner using device camera
- Student information lookup by invoice number
- Photo sequence number recording
- Session history and statistics
- Profile management with password change

### 🎓 Student Management
- Integration with external student registration database
- Automatic student data retrieval via invoice number
- Support for multiple photo types (individual, family, couple, etc.)
- Session and seat number tracking
- Contact information management

## Technology Stack

- **Backend**: PHP 7.4+
- **Database**: MySQL 5.7+
- **Frontend**: HTML5, CSS3 (Bulma Framework), JavaScript
- **QR Scanner**: HTML5-QRCode library
- **Charts**: Chart.js
- **Icons**: Font Awesome 6

## Installation

### Prerequisites
- XAMPP, WAMP, or similar LAMP stack
- PHP 7.4 or higher
- MySQL 5.7 or higher
- Web browser with camera access for QR scanning

### Setup Instructions

1. **Clone/Download the project**
   ```bash
   # Place the project files in your web server directory
   # For XAMPP: C:\xampp\htdocs\siyerra\
   ```

2. **Database Setup**
   ```sql
   # Import the database schemas
   # 1. First, create and import the local database:
   mysql -u root -p < database/local_database.sql
   
   # 2. Then, create and import the external database:
   mysql -u root -p < database/external_database.sql
   ```

3. **Configuration**
   - Update database credentials in `config/database.php` if needed
   - Modify `BASE_URL` in `config/config.php` to match your setup
   - Ensure proper file permissions for uploads and logs directories

4. **Access the Application**
   - Navigate to `http://localhost/siyerra/` in your web browser
   - Default admin credentials:
     - Username: `admin`
     - Password: `admin123`

## Database Structure

### Local Database (`photo_session_management`)
- `operators` - System users (admins and operators)
- `photo_sessions` - Completed photo session records
- `system_settings` - Application configuration
- `session_logs` - Activity and security logs

### External Database (`student_registration`)
- `students` - Student registration data with invoice numbers
- `photo_types` - Available photo type options
- `convocation_sessions` - Session scheduling information

## Usage Guide

### For Administrators

1. **Login** with admin credentials
2. **Manage Operators**: Add new operators, edit existing accounts, reset passwords
3. **View Statistics**: Monitor real-time progress and completion rates
4. **Export Data**: Download CSV reports of photo sessions
5. **System Settings**: Configure database connections and system parameters

### For Operators

1. **Login** with operator credentials
2. **Scan QR Codes**: Use mobile device camera to scan student QR codes
3. **Record Sessions**: Enter photo sequence numbers for completed sessions
4. **View History**: Check personal session completion statistics
5. **Manual Entry**: Enter invoice numbers manually if QR scanning fails

### QR Code Format

The system expects QR codes to contain the student's invoice number as plain text. The invoice number is used to fetch student information from the external database.

## Security Features

- **Session Management**: Automatic timeout and regeneration
- **CSRF Protection**: All forms include CSRF tokens
- **Input Validation**: Server-side sanitization of all inputs
- **Access Control**: Role-based permissions throughout the system
- **Activity Logging**: Comprehensive audit trail of user actions
- **Account Security**: Failed login attempt tracking and lockout

## Mobile Optimization

The system is fully responsive and optimized for mobile devices:
- Touch-friendly interface elements
- Camera-based QR code scanning
- Optimized layouts for small screens
- Fast loading and minimal data usage

## API Endpoints

- `GET/POST /api/student-lookup.php` - Fetch student data by invoice
- `POST /api/record-session.php` - Record completed photo session
- `GET /api/dashboard-stats.php` - Retrieve dashboard statistics

## File Structure

```
siyerra/
├── admin/                 # Admin interface files
├── operator/             # Operator interface files
├── api/                  # API endpoints
├── classes/              # PHP service classes
├── config/               # Configuration files
├── database/             # SQL schema files
├── uploads/              # File upload directory
├── logs/                 # Application logs
├── index.php             # Main entry point
├── login.php             # Login page
└── README.md             # This file
```

## Troubleshooting

### Common Issues

1. **Database Connection Failed**
   - Check MySQL service is running
   - Verify database credentials in `config/database.php`
   - Ensure databases are created and imported correctly

2. **QR Scanner Not Working**
   - Ensure HTTPS connection (required for camera access)
   - Check browser permissions for camera access
   - Try different browsers (Chrome/Firefox recommended)

3. **Session Timeout Issues**
   - Check PHP session configuration
   - Verify session timeout settings in `config/config.php`

4. **Permission Errors**
   - Ensure web server has write permissions to uploads/ and logs/ directories
   - Check file ownership and permissions

## Support

For technical support or questions:
- Check the troubleshooting section above
- Review the PHP error logs
- Ensure all prerequisites are met
- Verify database connections and data integrity

## License

This project is developed for educational and institutional use. Please ensure compliance with your organization's policies and applicable laws when deploying in production environments.
