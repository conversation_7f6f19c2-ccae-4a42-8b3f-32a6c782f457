<?php
// Authentication check - must be first
require_once 'auth-check.php';
require_admin();

$message = '';
$message_type = '';

// Handle form submission
if ($_SERVER['REQUEST_METHOD'] === 'POST') {
    if (!verify_csrf_token($_POST['csrf_token'] ?? '')) {
        $message = 'Invalid security token';
        $message_type = 'is-danger';
    } else {
        $action = $_POST['action'] ?? '';
        
        if ($action === 'update_sample_image') {
            $photo_type_id = (int)($_POST['photo_type_id'] ?? 0);
            $sample_image = sanitize_input($_POST['sample_image'] ?? '');
            
            try {
                $pdo = get_pdo_connection();
                $stmt = $pdo->prepare("UPDATE photo_types SET sample_image = ? WHERE id = ?");
                $stmt->execute([$sample_image, $photo_type_id]);
                
                $message = 'Sample image updated successfully';
                $message_type = 'is-success';
                log_activity('update_sample_image', "Updated sample image for photo type ID {$photo_type_id}");
            } catch (Exception $e) {
                $message = 'Error updating sample image';
                $message_type = 'is-danger';
                error_log("Sample image update error: " . $e->getMessage());
            }
        }
    }
}

// Get all photo types
$photo_types = [];
try {
    $pdo = get_pdo_connection();
    $stmt = $pdo->query("SELECT * FROM photo_types ORDER BY type_name");
    $photo_types = $stmt->fetchAll(PDO::FETCH_ASSOC);
} catch (Exception $e) {
    $message = 'Error loading photo types';
    $message_type = 'is-danger';
    error_log("Photo types loading error: " . $e->getMessage());
}
?>
<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Manage Sample Photos - <?php echo APP_NAME; ?></title>
    <link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/bulma@0.9.4/css/bulma.min.css">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css">
    <style>
        .dashboard-header {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
        }
        .sample-preview {
            width: 80px;
            height: 80px;
            border: 2px dashed #dbdbdb;
            border-radius: 8px;
            display: flex;
            align-items: center;
            justify-content: center;
            background-color: #f5f5f5;
            color: #999;
            font-size: 0.8rem;
            text-align: center;
        }
        .sample-preview img {
            width: 100%;
            height: 100%;
            object-fit: cover;
            border-radius: 6px;
        }
    </style>
</head>
<body>
    <!-- Navigation -->
    <nav class="navbar dashboard-header" role="navigation">
        <div class="navbar-brand">
            <div class="navbar-item">
                <i class="fas fa-shield-alt mr-2"></i>
                <span style="font-weight: bold;">Admin Dashboard</span>
            </div>
        </div>
        
        <div class="navbar-menu">
            <div class="navbar-start">
                <a class="navbar-item has-text-white" href="dashboard.php">
                    <i class="fas fa-tachometer-alt mr-2"></i>
                    Dashboard
                </a>
                <a class="navbar-item has-text-white" href="photo-pricing.php">
                    <i class="fas fa-tags mr-2"></i>
                    Photo Pricing
                </a>
                <a class="navbar-item has-text-white is-active" href="manage-sample-photos.php">
                    <i class="fas fa-images mr-2"></i>
                    Sample Photos
                </a>
            </div>
            
            <div class="navbar-end">
                <div class="navbar-item has-dropdown is-hoverable">
                    <a class="navbar-link has-text-white">
                        <i class="fas fa-user mr-2"></i>
                        <?php echo htmlspecialchars($_SESSION['full_name']); ?>
                    </a>
                    <div class="navbar-dropdown">
                        <a class="navbar-item" href="../logout.php">
                            <i class="fas fa-sign-out-alt mr-2"></i>
                            Logout
                        </a>
                    </div>
                </div>
            </div>
        </div>
    </nav>

    <div class="container mt-4">
        <!-- Header -->
        <div class="box">
            <h1 class="title is-4">
                <i class="fas fa-images mr-2"></i>
                Manage Sample Photos
            </h1>
            <p class="subtitle is-6">Add sample images to photo types for better customer experience</p>
        </div>

        <!-- Messages -->
        <?php if ($message): ?>
            <div class="notification <?php echo $message_type; ?>">
                <button class="delete" onclick="this.parentElement.style.display='none'"></button>
                <?php echo htmlspecialchars($message); ?>
            </div>
        <?php endif; ?>

        <!-- Instructions -->
        <div class="box">
            <h2 class="title is-5">
                <i class="fas fa-info-circle mr-2"></i>
                Instructions
            </h2>
            <div class="content">
                <ol>
                    <li><strong>Upload images</strong> to <code>assets/images/photo-samples/</code> folder</li>
                    <li><strong>Enter the path</strong> in the format: <code>assets/images/photo-samples/filename.jpg</code></li>
                    <li><strong>Recommended size:</strong> 400x400 pixels or larger</li>
                    <li><strong>Supported formats:</strong> JPG, PNG, GIF</li>
                </ol>
            </div>
        </div>

        <!-- Photo Types -->
        <div class="box">
            <h2 class="title is-5">
                <i class="fas fa-camera mr-2"></i>
                Photo Types
            </h2>
            
            <?php if (empty($photo_types)): ?>
                <div class="notification is-warning">
                    <i class="fas fa-exclamation-triangle mr-2"></i>
                    No photo types found. Please add photo types first.
                </div>
            <?php else: ?>
                <div class="table-container">
                    <table class="table is-fullwidth is-striped">
                        <thead>
                            <tr>
                                <th>Preview</th>
                                <th>Photo Type</th>
                                <th>Description</th>
                                <th>Sample Image Path</th>
                                <th>Actions</th>
                            </tr>
                        </thead>
                        <tbody>
                            <?php foreach ($photo_types as $photo_type): ?>
                                <tr>
                                    <td>
                                        <div class="sample-preview">
                                            <?php if (!empty($photo_type['sample_image'])): ?>
                                                <img src="../<?php echo htmlspecialchars($photo_type['sample_image']); ?>" 
                                                     alt="<?php echo htmlspecialchars($photo_type['type_name']); ?>"
                                                     onerror="this.style.display='none'; this.parentElement.innerHTML='<span>No Image</span>';">
                                            <?php else: ?>
                                                <span>No Image</span>
                                            <?php endif; ?>
                                        </div>
                                    </td>
                                    <td>
                                        <strong><?php echo htmlspecialchars($photo_type['type_name']); ?></strong>
                                    </td>
                                    <td>
                                        <?php echo htmlspecialchars($photo_type['description']); ?>
                                    </td>
                                    <td>
                                        <form method="POST" action="" class="is-inline">
                                            <input type="hidden" name="csrf_token" value="<?php echo generate_csrf_token(); ?>">
                                            <input type="hidden" name="action" value="update_sample_image">
                                            <input type="hidden" name="photo_type_id" value="<?php echo $photo_type['id']; ?>">
                                            
                                            <div class="field has-addons">
                                                <div class="control is-expanded">
                                                    <input class="input is-small" type="text" 
                                                           name="sample_image" 
                                                           value="<?php echo htmlspecialchars($photo_type['sample_image'] ?? ''); ?>"
                                                           placeholder="assets/images/photo-samples/filename.jpg">
                                                </div>
                                                <div class="control">
                                                    <button class="button is-primary is-small" type="submit">
                                                        <i class="fas fa-save"></i>
                                                    </button>
                                                </div>
                                            </div>
                                        </form>
                                    </td>
                                    <td>
                                        <?php if (!empty($photo_type['sample_image'])): ?>
                                            <a href="../<?php echo htmlspecialchars($photo_type['sample_image']); ?>" 
                                               target="_blank" class="button is-small is-info">
                                                <i class="fas fa-external-link-alt"></i>
                                            </a>
                                        <?php endif; ?>
                                    </td>
                                </tr>
                            <?php endforeach; ?>
                        </tbody>
                    </table>
                </div>
            <?php endif; ?>
        </div>

        <!-- Sample Paths -->
        <div class="box">
            <h2 class="title is-5">
                <i class="fas fa-code mr-2"></i>
                Common Sample Paths
            </h2>
            <div class="content">
                <p>Copy and paste these common paths:</p>
                <div class="tags">
                    <span class="tag is-light" onclick="copyToClipboard(this.textContent)">assets/images/photo-samples/individual.jpg</span>
                    <span class="tag is-light" onclick="copyToClipboard(this.textContent)">assets/images/photo-samples/couple.jpg</span>
                    <span class="tag is-light" onclick="copyToClipboard(this.textContent)">assets/images/photo-samples/family.jpg</span>
                    <span class="tag is-light" onclick="copyToClipboard(this.textContent)">assets/images/photo-samples/group.jpg</span>
                </div>
            </div>
        </div>
    </div>

    <script>
        function copyToClipboard(text) {
            navigator.clipboard.writeText(text).then(function() {
                // Show temporary success message
                const notification = document.createElement('div');
                notification.className = 'notification is-success is-light';
                notification.innerHTML = '<button class="delete"></button>Copied to clipboard: ' + text;
                notification.style.position = 'fixed';
                notification.style.top = '20px';
                notification.style.right = '20px';
                notification.style.zIndex = '9999';
                
                document.body.appendChild(notification);
                
                notification.querySelector('.delete').onclick = function() {
                    notification.remove();
                };
                
                setTimeout(function() {
                    if (notification.parentElement) {
                        notification.remove();
                    }
                }, 3000);
            });
        }
        
        // Auto-hide notifications
        document.addEventListener('DOMContentLoaded', function() {
            const notifications = document.querySelectorAll('.notification');
            notifications.forEach(function(notification) {
                setTimeout(function() {
                    if (notification.parentElement) {
                        notification.style.display = 'none';
                    }
                }, 5000);
            });
        });
    </script>
</body>
</html>
