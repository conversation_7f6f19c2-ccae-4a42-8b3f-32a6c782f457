# Photo Management System - Directory Structure

## 📁 New Directory Structure

The admin functionality has been moved from `admin/` to `app/` for better organization and clarity.

### **Root Directory**
```
siyerra/
├── app/                    # Admin/Management Interface (formerly admin/)
├── student/               # Student Registration & Payment Interface
├── operator/              # Operator Interface (QR scanning, etc.)
├── config/                # Configuration files
├── classes/               # PHP classes and services
├── database/              # Database scripts and migrations
├── assets/                # Static assets (CSS, images, etc.)
├── uploads/               # File uploads directory
├── logs/                  # Application logs
├── api/                   # API endpoints
├── services/              # Service classes
├── temp/                  # Temporary files
├── index.php              # Main landing page
├── login.php              # Login page
├── logout.php             # Logout handler
├── admin.php              # Redirect handler for old admin URLs
└── .htaccess              # URL rewrite rules
```

### **App Directory (Admin Interface)**
```
app/
├── dashboard.php          # Admin dashboard
├── operators.php          # Operator management
├── students.php           # Student management
├── sessions.php           # Photo session management
├── event-sessions.php     # Event session management
├── photo-pricing.php      # Photo type and pricing management
├── reports.php            # Reports and analytics
├── settings.php           # System settings
├── profile.php            # Admin profile management
├── manage-sample-photos.php # Sample photo management
├── report-generator.php   # Report generation utility
└── report-templates/      # Report template files
    ├── completion-status.php
    ├── daily-summary.php
    ├── operator-performance.php
    ├── photo-sessions.php
    └── today-summary.php
```

### **Student Directory**
```
student/
├── index.php              # Student portal home
├── registration.php       # Registration step 1 (NIC & Event)
├── registration-step2.php # Registration step 2 (Personal details)
├── registration-step3.php # Registration step 3 (Photo selection)
├── registration-step4.php # Registration step 4 (Review & complete)
├── payment.php            # Payment method selection
├── payment-card.php       # Credit/debit card payment
├── payment-bank.php       # Bank transfer payment
├── payment-event.php      # Pay at event option
├── payment-invoice.php    # Final invoice display
├── payment-success.php    # Payment success page
└── status-check.php       # Order status checking
```

## 🔄 Migration Changes

### **Updated References**
All references to `admin/` have been updated to `app/` in:
- ✅ `index.php` - Login redirect
- ✅ `login.php` - Login redirect
- ✅ `classes/Auth.php` - Authentication redirect
- ✅ `database/run_sample_image_migration.php` - Navigation link

### **Backward Compatibility**
- ✅ `.htaccess` rules redirect old `admin/` URLs to `app/`
- ✅ `admin.php` provides fallback redirect for direct access
- ✅ All existing functionality preserved

### **URL Changes**
| Old URL | New URL |
|---------|---------|
| `/admin/dashboard.php` | `/app/dashboard.php` |
| `/admin/operators.php` | `/app/operators.php` |
| `/admin/students.php` | `/app/students.php` |
| `/admin/sessions.php` | `/app/sessions.php` |
| `/admin/reports.php` | `/app/reports.php` |
| `/admin/settings.php` | `/app/settings.php` |

## 🚀 Benefits of New Structure

### **1. Clearer Separation**
- **`app/`** - Administrative/management interface
- **`student/`** - Public-facing student interface
- **`operator/`** - Operator-specific tools

### **2. Better Organization**
- More intuitive naming convention
- Easier to understand system architecture
- Cleaner URL structure

### **3. Scalability**
- Easy to add new modules (e.g., `teacher/`, `parent/`)
- Clear separation of concerns
- Modular architecture

### **4. Security**
- Admin interface clearly separated
- Easier to apply different security rules
- Better access control management

## 🔧 Development Notes

### **Navigation Updates**
All navigation menus in app files already use relative paths, so no changes were needed for internal navigation.

### **Session Management**
Session handling remains unchanged - all authentication and authorization logic works the same.

### **Database Connections**
All database connections use the same config files and work identically.

### **File Uploads**
Upload paths remain the same as they use absolute paths from the config.

## 📝 Testing Checklist

- [ ] Admin login redirects to `/app/dashboard.php`
- [ ] All admin navigation links work correctly
- [ ] Old `/admin/` URLs redirect to `/app/`
- [ ] Student interface remains unchanged
- [ ] Operator interface remains unchanged
- [ ] File uploads work correctly
- [ ] Reports generate properly
- [ ] All CRUD operations function normally

## 🔗 Quick Access

- **Admin Dashboard:** `/app/dashboard.php`
- **Student Portal:** `/student/`
- **Operator Tools:** `/operator/`
- **System Login:** `/login.php`
