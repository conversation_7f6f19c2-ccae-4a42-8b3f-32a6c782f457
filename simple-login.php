<?php
require_once 'config/simple-config.php';

$error_message = '';

// Handle login form submission
if ($_SERVER['REQUEST_METHOD'] === 'POST') {
    $username = sanitize_input($_POST['username'] ?? '');
    $password = $_POST['password'] ?? '';
    
    // Simple hardcoded check for testing
    if ($username === 'admin' && $password === 'admin123') {
        $_SESSION['user_id'] = 1;
        $_SESSION['username'] = 'admin';
        $_SESSION['full_name'] = 'System Administrator';
        $_SESSION['role'] = 'admin';
        $_SESSION['last_activity'] = time();
        
        redirect(BASE_URL . 'test-dashboard.php');
    } else {
        $error_message = 'Invalid username or password';
    }
}
?>
<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Simple Login Test - <?php echo APP_NAME; ?></title>
    <link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/bulma@0.9.4/css/bulma.min.css">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css">
    <style>
        .login-container {
            min-height: 100vh;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
        }
        .login-box {
            background: white;
            border-radius: 10px;
            box-shadow: 0 10px 30px rgba(0,0,0,0.2);
        }
        .logo-section {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            border-radius: 10px 10px 0 0;
        }
    </style>
</head>
<body>
    <div class="login-container is-flex is-align-items-center is-justify-content-center">
        <div class="container">
            <div class="columns is-centered">
                <div class="column is-4-desktop is-6-tablet is-10-mobile">
                    <div class="login-box">
                        <div class="logo-section has-text-centered py-6">
                            <i class="fas fa-camera fa-3x mb-4"></i>
                            <h1 class="title is-4 has-text-white mb-2">Simple Login Test</h1>
                            <p class="subtitle is-6 has-text-white">Photo Session Management</p>
                        </div>
                        
                        <div class="box">
                            <?php if ($error_message): ?>
                                <div class="notification is-danger is-light">
                                    <i class="fas fa-exclamation-triangle mr-2"></i>
                                    <?php echo htmlspecialchars($error_message); ?>
                                </div>
                            <?php endif; ?>
                            
                            <form method="POST" action="">
                                <input type="hidden" name="csrf_token" value="<?php echo generate_csrf_token(); ?>">
                                
                                <div class="field">
                                    <label class="label">Username</label>
                                    <div class="control has-icons-left">
                                        <input class="input" type="text" name="username" placeholder="Enter username" 
                                               value="<?php echo htmlspecialchars($_POST['username'] ?? ''); ?>" required>
                                        <span class="icon is-small is-left">
                                            <i class="fas fa-user"></i>
                                        </span>
                                    </div>
                                </div>
                                
                                <div class="field">
                                    <label class="label">Password</label>
                                    <div class="control has-icons-left">
                                        <input class="input" type="password" name="password" placeholder="Enter password" required>
                                        <span class="icon is-small is-left">
                                            <i class="fas fa-lock"></i>
                                        </span>
                                    </div>
                                </div>
                                
                                <div class="field">
                                    <div class="control">
                                        <button class="button is-primary is-fullwidth" type="submit">
                                            <span class="icon">
                                                <i class="fas fa-sign-in-alt"></i>
                                            </span>
                                            <span>Login</span>
                                        </button>
                                    </div>
                                </div>
                            </form>
                            
                            <hr>
                            <div class="has-text-centered">
                                <p class="is-size-7 has-text-grey">
                                    <i class="fas fa-info-circle mr-1"></i>
                                    Test credentials: admin / admin123
                                </p>
                                <p class="is-size-7 has-text-grey mt-2">
                                    <a href="minimal-test.php">← Back to System Test</a>
                                </p>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</body>
</html>
