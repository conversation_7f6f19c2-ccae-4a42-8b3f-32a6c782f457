<?php
require_once '../config/config.php';

if (session_status() === PHP_SESSION_NONE) {
    session_start();
}

// Check if user came from previous steps
if (!isset($_SESSION['registration_nic']) || !isset($_SESSION['registration_event_id']) || !isset($_SESSION['registration_data'])) {
    redirect('registration.php');
}

$message = '';
$message_type = '';
$photo_types = [];
$delivery_methods = [];
$event = null;

// Get event details, photo types with pricing, and delivery methods
try {
    $pdo = get_pdo_connection();
    
    // Get event details
    $stmt = $pdo->prepare("SELECT * FROM events WHERE id = ?");
    $stmt->execute([$_SESSION['registration_event_id']]);
    $event = $stmt->fetch(PDO::FETCH_ASSOC);
    
    // Get photo types with pricing for this event
    $stmt = $pdo->prepare("
        SELECT pt.*, epp.price, epp.currency 
        FROM photo_types pt
        JOIN event_photo_pricing epp ON pt.id = epp.photo_type_id
        WHERE pt.is_active = 1 AND epp.event_id = ? AND epp.is_active = 1
        ORDER BY pt.type_name
    ");
    $stmt->execute([$_SESSION['registration_event_id']]);
    $photo_types = $stmt->fetchAll(PDO::FETCH_ASSOC);
    
    // Get courier service charge from settings first
    $courier_charge = 500; // Default value
    try {
        $local_pdo = DatabaseConfig::getUnifiedConnection();
        $stmt = $local_pdo->prepare("SELECT setting_value FROM system_settings WHERE setting_key = 'courier_service_charge'");
        $stmt->execute();
        $result = $stmt->fetchColumn();
        if ($result !== false) {
            $courier_charge = floatval($result);
        }
    } catch (Exception $e) {
        error_log("Error loading courier charge: " . $e->getMessage());
    }

    // Get delivery methods and update courier charges
    $stmt = $pdo->query("SELECT * FROM delivery_methods WHERE is_active = 1 ORDER BY additional_cost");
    $delivery_methods = $stmt->fetchAll(PDO::FETCH_ASSOC);

    // Update courier delivery methods with settings value
    foreach ($delivery_methods as &$method) {
        if (stripos($method['method_name'], 'courier') !== false) {
            $method['additional_cost'] = $courier_charge;
        }
    }
    unset($method); // Break reference

} catch (Exception $e) {
    $message = 'Error loading photo options. Please try again.';
    $message_type = 'is-danger';
    error_log("Photo selection error: " . $e->getMessage());
}

// Handle form submission
if ($_SERVER['REQUEST_METHOD'] === 'POST') {
    $selected_photos = $_POST['photos'] ?? [];
    $delivery_method_id = (int)($_POST['delivery_method_id'] ?? 0);
    $delivery_address = sanitize_input($_POST['delivery_address'] ?? '');
    
    if (empty($selected_photos)) {
        $message = 'Please select at least one photo type';
        $message_type = 'is-warning';
    } elseif (empty($delivery_method_id)) {
        $message = 'Please select a delivery method';
        $message_type = 'is-warning';
    } else {
        // Calculate total amount
        $total_amount = 0;
        $photo_orders = [];
        
        foreach ($selected_photos as $photo_type_id => $quantity) {
            $quantity = (int)$quantity;
            if ($quantity > 0) {
                // Find the photo type and price
                foreach ($photo_types as $photo_type) {
                    if ($photo_type['id'] == $photo_type_id) {
                        $unit_price = $photo_type['price'];
                        $total_price = $unit_price * $quantity;
                        $total_amount += $total_price;
                        
                        $photo_orders[] = [
                            'photo_type_id' => $photo_type_id,
                            'type_name' => $photo_type['type_name'],
                            'quantity' => $quantity,
                            'unit_price' => $unit_price,
                            'total_price' => $total_price
                        ];
                        break;
                    }
                }
            }
        }
        
        // Add delivery cost
        $delivery_cost = 0;
        foreach ($delivery_methods as $method) {
            if ($method['id'] == $delivery_method_id) {
                $delivery_cost = $method['additional_cost'];
                $_SESSION['delivery_method'] = $method;
                break;
            }
        }
        
        $total_amount += $delivery_cost;
        
        // Store order details in session
        $_SESSION['photo_orders'] = $photo_orders;
        $_SESSION['delivery_address'] = $delivery_address;
        $_SESSION['total_amount'] = $total_amount;
        $_SESSION['delivery_cost'] = $delivery_cost;
        
        redirect('registration-step4.php');
    }
}
?>
<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Registration Step 3 - <?php echo APP_NAME; ?></title>
    <link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/bulma@0.9.4/css/bulma.min.css">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css">
    <style>
        .hero-section {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
        }
        .registration-card {
            border-radius: 15px;
            box-shadow: 0 10px 30px rgba(0,0,0,0.1);
        }
        .step-indicator {
            display: flex;
            justify-content: center;
            margin-bottom: 2rem;
        }
        .step {
            display: flex;
            align-items: center;
            margin: 0 1rem;
        }
        .step-number {
            width: 40px;
            height: 40px;
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
            font-weight: bold;
            margin-right: 0.5rem;
        }
        .step.active .step-number {
            background-color: #3273dc;
            color: white;
        }
        .step.completed .step-number {
            background-color: #48c78e;
            color: white;
        }
        .step.pending .step-number {
            background-color: #dbdbdb;
            color: #7a7a7a;
        }
        .photo-card {
            border: 2px solid #dbdbdb;
            border-radius: 12px;
            padding: 1.5rem;
            margin-bottom: 1rem;
            transition: all 0.3s;
            cursor: pointer;
            min-height: 140px;
        }
        .photo-card:hover {
            border-color: #3273dc;
            transform: translateY(-2px);
            box-shadow: 0 8px 25px rgba(0,0,0,0.1);
        }
        .photo-card.selected {
            border-color: #48c78e;
            background-color: #f0fff4;
        }
        .photo-card-content {
            display: flex;
            align-items: flex-start;
            gap: 1rem;
        }
        .photo-preview {
            flex-shrink: 0;
            width: 80px;
            height: 80px;
            border-radius: 8px;
            background-color: #f5f5f5;
            border: 2px dashed #dbdbdb;
            display: flex;
            align-items: center;
            justify-content: center;
            color: #999;
            font-size: 0.8rem;
            text-align: center;
            line-height: 1.2;
        }
        .photo-preview img {
            width: 100%;
            height: 100%;
            object-fit: cover;
            border-radius: 6px;
        }
        .photo-info {
            flex: 1;
            min-width: 0;
        }
        .photo-info .title.is-5 {
            margin-bottom: 0.5rem;
            line-height: 1.3;
            word-wrap: break-word;
        }
        .photo-info .subtitle.is-6 {
            margin-bottom: 0.75rem;
            line-height: 1.4;
            word-wrap: break-word;
            overflow-wrap: break-word;
            hyphens: auto;
        }
        .photo-info .title.is-4 {
            margin-bottom: 0;
            white-space: nowrap;
        }
        .photo-controls {
            flex-shrink: 0;
            display: flex;
            flex-direction: column;
            align-items: center;
            gap: 0.5rem;
        }
        .quantity-controls {
            display: flex;
            align-items: center;
            border: 1px solid #dbdbdb;
            border-radius: 4px;
            overflow: hidden;
        }
        .quantity-controls .button {
            border: none;
            border-radius: 0;
            height: 40px;
            width: 40px;
            padding: 0;
            display: flex;
            align-items: center;
            justify-content: center;
        }
        .quantity-controls .input {
            border: none;
            text-align: center;
            width: 60px;
            height: 40px;
            border-radius: 0;
            font-weight: bold;
            background-color: #fafafa;
        }
        .quantity-controls .input:focus {
            box-shadow: none;
        }
        /* Hide number input arrows */
        .quantity-controls .input::-webkit-outer-spin-button,
        .quantity-controls .input::-webkit-inner-spin-button {
            -webkit-appearance: none;
            margin: 0;
        }
        .quantity-controls .input[type=number] {
            -moz-appearance: textfield;
        }
        .delivery-card {
            border: 2px solid #dbdbdb;
            border-radius: 12px;
            padding: 1rem;
            margin-bottom: 1rem;
            transition: all 0.3s;
            cursor: pointer;
        }
        .delivery-card:hover {
            border-color: #3273dc;
        }
        .delivery-card.selected {
            border-color: #48c78e;
            background-color: #f0fff4;
        }
        .quantity-input {
            width: 80px;
        }
        .total-summary {
            background: linear-gradient(135deg, #48c78e 0%, #00d1b2 100%);
            color: white;
            border-radius: 12px;
            padding: 1.5rem;
        }

        .input, .select select, .textarea {
            font-size: 0.9rem;
        }

        /* Mobile responsiveness */
        @media screen and (max-width: 768px) {
            .photo-card {
                padding: 1rem;
                min-height: auto;
            }

            .photo-card-content {
                flex-direction: column;
                align-items: center;
                text-align: center;
                gap: 1rem;
            }

            .photo-preview {
                width: 100px;
                height: 100px;
            }

            .photo-info {
                order: 1;
            }

            .photo-controls {
                order: 2;
            }

            .quantity-controls {
                width: 160px;
            }

            .photo-info .title.is-5 {
                font-size: 1.125rem;
            }

            .photo-info .subtitle.is-6 {
                font-size: 1rem;
            }

            .photo-info .title.is-4 {
                font-size: 1.25rem;
            }
        }

        @media screen and (max-width: 480px) {
            .photo-card {
                padding: 0.75rem;
            }

            .photo-preview {
                width: 80px;
                height: 80px;
            }

            .photo-info .title.is-5 {
                font-size: 1rem;
            }

            .photo-info .subtitle.is-6 {
                font-size: 0.9rem;
            }

            .photo-info .title.is-4 {
                font-size: 1.125rem;
            }

            .quantity-controls {
                width: 140px;
            }

            .quantity-controls .button {
                width: 35px;
                height: 35px;
            }

            .quantity-controls .input {
                width: 50px;
                height: 35px;
                font-size: 0.9rem;
            }
        }
    </style>
</head>
<body>
    <section class="hero hero-section">
        <div class="hero-body">
            <div class="container">
                <!-- Header -->
                <div class="has-text-centered mb-6">
                    <h1 class="title is-2 has-text-white">
                        <i class="fas fa-images mr-3"></i>
                        Photo Selection
                    </h1>
                    <p class="subtitle is-4 has-text-white">
                        Choose your photo packages and delivery method
                    </p>
                </div>

                <!-- Messages -->
                <?php if ($message): ?>
                    <div class="columns is-centered">
                        <div class="column is-10">
                            <div class="notification <?php echo $message_type; ?>">
                                <button class="delete" onclick="this.parentElement.style.display='none'"></button>
                                <?php echo htmlspecialchars($message); ?>
                            </div>
                        </div>
                    </div>
                <?php endif; ?>

                <!-- Registration Form -->
                <div class="columns is-centered">
                    <div class="column is-10">
                        <div class="box registration-card">
                            
                            <!-- Step Indicator -->
                            <div class="step-indicator">
                                <div class="step completed">
                                    <div class="step-number"><i class="fas fa-check"></i></div>
                                    <span>Verification</span>
                                </div>
                                <div class="step completed">
                                    <div class="step-number"><i class="fas fa-check"></i></div>
                                    <span>Details</span>
                                </div>
                                <div class="step active">
                                    <div class="step-number">3</div>
                                    <span>Photos</span>
                                </div>
                                <div class="step pending">
                                    <div class="step-number">4</div>
                                    <span>Payment</span>
                                </div>
                            </div>

                            <!-- Registration Info -->
                            <div class="notification is-info is-light mb-5">
                                <div class="level">
                                    <div class="level-left">
                                        <div>
                                            <p><strong>Student:</strong> <?php echo htmlspecialchars($_SESSION['registration_data']['full_name']); ?></p>
                                            <p><strong>Event:</strong> <?php echo htmlspecialchars($event['event_name'] ?? 'Unknown'); ?></p>
                                        </div>
                                    </div>
                                </div>
                            </div>

                            <form method="POST" action="" id="photoSelectionForm">
                                <div class="columns">
                                    <div class="column is-8">
                                        <!-- Photo Types Selection -->
                                        <h2 class="title is-4">
                                            <i class="fas fa-camera mr-2"></i>
                                            Select Photo Types
                                        </h2>
                                        
                                        <?php if (empty($photo_types)): ?>
                                            <div class="notification is-warning">
                                                <i class="fas fa-exclamation-triangle mr-2"></i>
                                                No photo types available for this event. Please contact support.
                                            </div>
                                        <?php else: ?>
                                            <div class="photo-types-grid">
                                                <?php foreach ($photo_types as $photo_type): ?>
                                                    <div class="photo-card" onclick="togglePhotoSelection(<?php echo $photo_type['id']; ?>)">
                                                        <div class="photo-card-content">
                                                            <div class="photo-preview">
                                                                <?php if (!empty($photo_type['sample_image'])): ?>
                                                                    <img src="<?php echo htmlspecialchars($photo_type['sample_image']); ?>"
                                                                         alt="<?php echo htmlspecialchars($photo_type['type_name']); ?>">
                                                                <?php else: ?>
                                                                    <span>Sample<br>Photo</span>
                                                                <?php endif; ?>
                                                            </div>
                                                            <div class="photo-info">
                                                                <h3 class="title is-5"><?php echo htmlspecialchars($photo_type['type_name']); ?></h3>
                                                                <p class="subtitle is-6 has-text-grey"><?php echo htmlspecialchars($photo_type['description']); ?></p>
                                                                <p class="title is-4 has-text-primary">
                                                                    <?php echo $photo_type['currency']; ?> <?php echo number_format($photo_type['price'], 2); ?>
                                                                </p>
                                                            </div>
                                                            <div class="photo-controls">
                                                                <div class="quantity-controls">
                                                                    <button type="button" class="button is-light"
                                                                            onclick="changeQuantity(<?php echo $photo_type['id']; ?>, -1); event.stopPropagation();">
                                                                        <i class="fas fa-minus"></i>
                                                                    </button>
                                                                    <input class="input quantity-input" type="number"
                                                                           name="photos[<?php echo $photo_type['id']; ?>]"
                                                                           min="0" max="10" value="0"
                                                                           onclick="event.stopPropagation()"
                                                                           readonly>
                                                                    <button type="button" class="button is-light"
                                                                            onclick="changeQuantity(<?php echo $photo_type['id']; ?>, 1); event.stopPropagation();">
                                                                        <i class="fas fa-plus"></i>
                                                                    </button>
                                                                </div>
                                                            </div>
                                                        </div>
                                                    </div>
                                                <?php endforeach; ?>
                                            </div>
                                        <?php endif; ?>

                                        <!-- Delivery Method Selection -->
                                        <h2 class="title is-4 mt-6">
                                            <i class="fas fa-truck mr-2"></i>
                                            Delivery Method
                                        </h2>
                                        
                                        <?php foreach ($delivery_methods as $method): ?>
                                            <div class="delivery-card" onclick="selectDeliveryMethod(<?php echo $method['id']; ?>)">
                                                <label class="radio">
                                                    <input type="radio" name="delivery_method_id" value="<?php echo $method['id']; ?>" required>
                                                    <strong><?php echo htmlspecialchars($method['method_name']); ?></strong>
                                                    <?php if ($method['additional_cost'] > 0): ?>
                                                        <span class="tag is-warning ml-2">+LKR <?php echo number_format($method['additional_cost'], 2); ?></span>
                                                    <?php else: ?>
                                                        <span class="tag is-success ml-2">Free</span>
                                                    <?php endif; ?>
                                                </label>
                                                <p class="has-text-grey mt-2"><?php echo htmlspecialchars($method['description']); ?></p>
                                            </div>
                                        <?php endforeach; ?>

                                        <!-- Delivery Address -->
                                        <div class="field mt-4" id="deliveryAddressField" style="display: none;">
                                            <label class="label">Delivery Address</label>
                                            <div class="control">
                                                <textarea class="textarea" name="delivery_address" rows="3" 
                                                          placeholder="Enter complete delivery address for courier service"></textarea>
                                            </div>
                                        </div>
                                    </div>

                                    <!-- Order Summary -->
                                    <div class="column is-4">
                                        <div class="total-summary">
                                            <h3 class="title is-5 has-text-white">
                                                <i class="fas fa-receipt mr-2"></i>
                                                Order Summary
                                            </h3>
                                            <div id="orderSummary">
                                                <p class="has-text-white">Select photos to see pricing</p>
                                            </div>
                                        </div>

                                        <div class="buttons is-fullwidth mt-4">
                                            <a href="registration-step2.php" class="button is-light is-large">
                                                <i class="fas fa-arrow-left mr-2"></i>
                                                Back
                                            </a>
                                            <button class="button is-primary is-large" type="submit">
                                                Continue
                                                <i class="fas fa-arrow-right ml-2"></i>
                                            </button>
                                        </div>
                                    </div>
                                </div>
                            </form>
                        </div>
                    </div>
                </div>

                <!-- Back to Home -->
                <div class="has-text-centered mt-6">
                    <a href="index.php" class="button is-light is-large">
                        <i class="fas fa-home mr-2"></i>
                        Back to Home
                    </a>
                </div>
            </div>
        </div>
    </section>

    <script>
        const photoTypes = <?php echo json_encode($photo_types); ?>;
        const deliveryMethods = <?php echo json_encode($delivery_methods); ?>;

        function changeQuantity(photoTypeId, change) {
            const input = document.querySelector(`input[name="photos[${photoTypeId}]"]`);
            const currentValue = parseInt(input.value) || 0;
            const newValue = Math.max(0, Math.min(10, currentValue + change));

            input.value = newValue;

            const card = input.closest('.photo-card');
            if (newValue > 0) {
                card.classList.add('selected');
            } else {
                card.classList.remove('selected');
            }

            // Force update total calculation
            setTimeout(updateTotal, 10);
        }

        function togglePhotoSelection(photoTypeId) {
            const input = document.querySelector(`input[name="photos[${photoTypeId}]"]`);
            const card = input.closest('.photo-card');
            
            if (input.value == 0) {
                input.value = 1;
                card.classList.add('selected');
            }
            updateTotal();
        }
        
        function selectDeliveryMethod(methodId) {
            const radio = document.querySelector(`input[name="delivery_method_id"][value="${methodId}"]`);
            radio.checked = true;
            
            // Update card styling
            document.querySelectorAll('.delivery-card').forEach(card => card.classList.remove('selected'));
            radio.closest('.delivery-card').classList.add('selected');
            
            // Show/hide delivery address field
            const addressField = document.getElementById('deliveryAddressField');
            const method = deliveryMethods.find(m => m.id == methodId);
            if (method && method.method_code === 'COURIER') {
                addressField.style.display = 'block';
                addressField.querySelector('textarea').required = true;
            } else {
                addressField.style.display = 'none';
                addressField.querySelector('textarea').required = false;
            }
            
            updateTotal();
        }
        
        function updateTotal() {
            let total = 0;
            let summaryHtml = '';
            let hasItems = false;
            
            // Calculate photo costs
            photoTypes.forEach(photoType => {
                const input = document.querySelector(`input[name="photos[${photoType.id}]"]`);
                const quantity = parseInt(input.value) || 0;
                const card = input.closest('.photo-card');
                
                if (quantity > 0) {
                    const itemTotal = quantity * photoType.price;
                    total += itemTotal;
                    summaryHtml += `
                        <div class="level is-mobile mb-2">
                            <div class="level-left">
                                <div>
                                    <p class="has-text-white is-size-7">${photoType.type_name}</p>
                                    <p class="has-text-white-ter is-size-7">${quantity} × LKR ${photoType.price.toFixed(2)}</p>
                                </div>
                            </div>
                            <div class="level-right">
                                <p class="has-text-white">LKR ${itemTotal.toFixed(2)}</p>
                            </div>
                        </div>
                    `;
                    card.classList.add('selected');
                    hasItems = true;
                } else {
                    card.classList.remove('selected');
                }
            });
            
            // Add delivery cost
            const selectedDelivery = document.querySelector('input[name="delivery_method_id"]:checked');
            if (selectedDelivery) {
                const method = deliveryMethods.find(m => m.id == selectedDelivery.value);
                if (method) {
                    const deliveryCost = parseFloat(method.additional_cost) || 0;

                    if (deliveryCost > 0) {
                        total += deliveryCost;
                        summaryHtml += `
                            <div class="level is-mobile mb-2">
                                <div class="level-left">
                                    <p class="has-text-white is-size-7">${method.method_name}</p>
                                </div>
                                <div class="level-right">
                                    <p class="has-text-white">LKR ${deliveryCost.toFixed(2)}</p>
                                </div>
                            </div>
                        `;
                    }
                }
            }
            
            if (hasItems || total > 0) {
                summaryHtml += `
                    <hr style="background-color: rgba(255,255,255,0.3);">
                    <div class="level is-mobile">
                        <div class="level-left">
                            <p class="has-text-white title is-6">Total</p>
                        </div>
                        <div class="level-right">
                            <p class="has-text-white title is-5">LKR ${total.toFixed(2)}</p>
                        </div>
                    </div>
                `;
            } else {
                summaryHtml = '<p class="has-text-white">Select photos to see pricing</p>';
            }
            
            document.getElementById('orderSummary').innerHTML = summaryHtml;
        }
        
        // Initialize
        document.addEventListener('DOMContentLoaded', function() {
            updateTotal();
        });
    </script>
</body>
</html>
