-- External Database Schema for Student Registration System
-- This represents the existing student registration database

CREATE DATABASE IF NOT EXISTS student_registration;
USE student_registration;

-- Students table with registration information
CREATE TABLE students (
    id INT AUTO_INCREMENT PRIMARY KEY,
    invoice_number VARCHAR(50) UNIQUE NOT NULL,
    nic_number VARCHAR(20) UNIQUE NOT NULL,
    full_name VARCHAR(100) NOT NULL,
    address TEXT NOT NULL,
    whatsapp_number VARCHAR(20),
    alternate_phone VARCHAR(20),
    email VARCHAR(100),
    session VARCHAR(50) NOT NULL,
    seat_number VARCHAR(20),
    photo_types JSON NOT NULL, -- Selected photo types as JSON array
    registration_fee DECIMAL(10,2),
    payment_status ENUM('pending', 'paid', 'refunded') DEFAULT 'pending',
    registration_date TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    is_active BOOLEAN DEFAULT TRUE,
    INDEX idx_invoice (invoice_number),
    INDEX idx_nic (nic_number),
    INDEX idx_session (session),
    INDEX idx_registration_date (registration_date)
);

-- Photo types reference table
CREATE TABLE photo_types (
    id INT AUTO_INCREMENT PRIMARY KEY,
    type_name VARCHAR(50) NOT NULL,
    type_code VARCHAR(20) UNIQUE NOT NULL,
    price DECIMAL(8,2) NOT NULL,
    description TEXT,
    is_active BOOLEAN DEFAULT TRUE,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);

-- Sessions reference table
CREATE TABLE convocation_sessions (
    id INT AUTO_INCREMENT PRIMARY KEY,
    session_name VARCHAR(100) NOT NULL,
    session_code VARCHAR(20) UNIQUE NOT NULL,
    session_date DATE NOT NULL,
    session_time TIME NOT NULL,
    venue VARCHAR(100),
    max_capacity INT DEFAULT 1000,
    is_active BOOLEAN DEFAULT TRUE,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);

-- Insert sample photo types
INSERT INTO photo_types (type_name, type_code, price, description) VALUES 
('Individual Photo', 'IND', 500.00, 'Single person graduation photo'),
('Family Photo', 'FAM', 1000.00, 'Family group photo with graduate'),
('Couple Photo', 'COU', 750.00, 'Photo with spouse/partner'),
('Friends Group', 'FRG', 1200.00, 'Photo with friends group'),
('Professional Portrait', 'PRO', 800.00, 'Professional headshot style photo');

-- Insert sample sessions
INSERT INTO convocation_sessions (session_name, session_code, session_date, session_time, venue) VALUES 
('Engineering Faculty - Morning', 'ENG_AM', '2024-08-15', '09:00:00', 'Main Auditorium'),
('Engineering Faculty - Evening', 'ENG_PM', '2024-08-15', '14:00:00', 'Main Auditorium'),
('Medical Faculty - Morning', 'MED_AM', '2024-08-16', '09:00:00', 'Main Auditorium'),
('Medical Faculty - Evening', 'MED_PM', '2024-08-16', '14:00:00', 'Main Auditorium'),
('Arts Faculty - Morning', 'ART_AM', '2024-08-17', '09:00:00', 'Main Auditorium'),
('Arts Faculty - Evening', 'ART_PM', '2024-08-17', '14:00:00', 'Main Auditorium');

-- Insert sample student data for testing
INSERT INTO students (invoice_number, nic_number, full_name, address, whatsapp_number, alternate_phone, email, session, seat_number, photo_types, registration_fee, payment_status) VALUES 
('INV001', '199512345678', 'John Doe Silva', '123 Main Street, Colombo 07', '+94771234567', '+94112345678', '<EMAIL>', 'ENG_AM', 'A001', '["IND", "FAM"]', 1500.00, 'paid'),
('INV002', '199687654321', 'Jane Mary Fernando', '456 Galle Road, Mount Lavinia', '+94779876543', '+94113456789', '<EMAIL>', 'ENG_AM', 'A002', '["IND", "COU", "PRO"]', 2050.00, 'paid'),
('INV003', '199798765432', 'Michael Anthony Perera', '789 Kandy Road, Kadawatha', '+94765432109', '+94114567890', '<EMAIL>', 'MED_AM', 'B001', '["IND", "FAM", "FRG"]', 2700.00, 'paid'),
('INV004', '199856789012', 'Sarah Elizabeth Jayawardena', '321 Negombo Road, Wattala', '+94754321098', '+94115678901', '<EMAIL>', 'MED_AM', 'B002', '["IND"]', 500.00, 'paid'),
('INV005', '199945678901', 'David Kumar Rajapaksa', '654 High Level Road, Nugegoda', '+94743210987', '+94116789012', '<EMAIL>', 'ART_AM', 'C001', '["IND", "FAM", "COU"]', 2250.00, 'paid');
