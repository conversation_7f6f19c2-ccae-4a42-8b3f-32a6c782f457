<?php
require_once '../config/config.php';

if (session_status() === PHP_SESSION_NONE) {
    session_start();
}

$message = '';
$message_type = '';
$student_data = null;
$photo_orders = [];

if ($_SERVER['REQUEST_METHOD'] === 'POST') {
    $nic_number = sanitize_input($_POST['nic_number'] ?? '');
    
    if (empty($nic_number)) {
        $message = 'Please enter your NIC number';
        $message_type = 'is-warning';
    } else {
        try {
            $pdo = get_pdo_connection();
            
            // Search for student by NIC
            $stmt = $pdo->prepare("
                SELECT s.*, e.event_name, e.event_date, dm.method_name as delivery_method,
                       ps.photo_sequence_number, ps.completed_at as photo_completed_at
                FROM students s
                LEFT JOIN events e ON s.event_id = e.id
                LEFT JOIN delivery_methods dm ON s.delivery_method_id = dm.id
                LEFT JOIN photo_sessions ps ON s.id = ps.student_id
                WHERE s.nic_number = ?
                ORDER BY s.created_at DESC
            ");
            $stmt->execute([$nic_number]);
            $student_data = $stmt->fetch(PDO::FETCH_ASSOC);
            
            if ($student_data) {
                // Get photo orders
                $stmt = $pdo->prepare("
                    SELECT spo.*, pt.type_name, pt.description
                    FROM student_photo_orders spo
                    JOIN photo_types pt ON spo.photo_type_id = pt.id
                    WHERE spo.student_id = ?
                ");
                $stmt->execute([$student_data['id']]);
                $photo_orders = $stmt->fetchAll(PDO::FETCH_ASSOC);
            } else {
                $message = 'No registration found for this NIC number. Please check your NIC or register first.';
                $message_type = 'is-danger';
            }
        } catch (Exception $e) {
            $message = 'Error searching for registration. Please try again.';
            $message_type = 'is-danger';
            error_log("Status check error: " . $e->getMessage());
        }
    }
}

// Status mapping for display
$status_info = [
    'registered' => [
        'label' => 'Registered',
        'color' => 'is-info',
        'icon' => 'fas fa-user-check',
        'description' => 'Your registration is complete. Please wait for your photo session.'
    ],
    'photo_taken' => [
        'label' => 'Photo Taken',
        'color' => 'is-primary',
        'icon' => 'fas fa-camera',
        'description' => 'Your photo session is complete. Photos are being processed.'
    ],
    'in_development' => [
        'label' => 'In Development',
        'color' => 'is-warning',
        'icon' => 'fas fa-cogs',
        'description' => 'Your photos are being professionally processed and prepared.'
    ],
    'dispatched' => [
        'label' => 'Dispatched via Courier',
        'color' => 'is-success',
        'icon' => 'fas fa-truck',
        'description' => 'Your photos have been dispatched and are on the way to you.'
    ],
    'ready_for_pickup' => [
        'label' => 'Ready for Pickup',
        'color' => 'is-success',
        'icon' => 'fas fa-box',
        'description' => 'Your photos are ready for pickup at the collection center.'
    ],
    'completed' => [
        'label' => 'Completed',
        'color' => 'is-success',
        'icon' => 'fas fa-check-circle',
        'description' => 'Order completed successfully. Thank you!'
    ]
];
?>
<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Check Photo Status - <?php echo APP_NAME; ?></title>
    <link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/bulma@0.9.4/css/bulma.min.css">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css">
    <style>
        .hero-section {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
        }
        .status-card {
            border-left: 5px solid #667eea;
            transition: transform 0.2s;
        }
        .status-card:hover {
            transform: translateY(-2px);
        }
        .progress-step {
            display: flex;
            align-items: center;
            margin-bottom: 1rem;
        }
        .progress-step.active {
            color: #48c78e;
            font-weight: bold;
        }
        .progress-step.completed {
            color: #48c78e;
        }
        .progress-step.pending {
            color: #dbdbdb;
        }

        /* Custom select dropdown arrow positioning */
        .select:not(.is-multiple):not(.is-loading)::after {
            margin-top: -0.9em !important;
            transform: none !important;
        }
    </style>
</head>
<body>
    <section class="hero hero-section">
        <div class="hero-body">
            <div class="container">
                <!-- Header -->
                <div class="has-text-centered mb-6">
                    <h1 class="title is-2 has-text-white">
                        <i class="fas fa-search mr-3"></i>
                        Check Photo Status
                    </h1>
                    <p class="subtitle is-4 has-text-white">
                        Enter your NIC number to track your photo processing status
                    </p>
                </div>

                <!-- Search Form -->
                <div class="columns is-centered">
                    <div class="column is-6">
                        <div class="box">
                            <form method="POST" action="">
                                <div class="field">
                                    <label class="label">NIC Number</label>
                                    <div class="control has-icons-left">
                                        <input class="input is-large" type="text" name="nic_number" 
                                               placeholder="Enter your NIC number (e.g., 123456789V)" 
                                               value="<?php echo htmlspecialchars($_POST['nic_number'] ?? ''); ?>" required>
                                        <span class="icon is-left">
                                            <i class="fas fa-id-card"></i>
                                        </span>
                                    </div>
                                </div>
                                <div class="field">
                                    <div class="control">
                                        <button class="button is-primary is-large is-fullwidth" type="submit">
                                            <i class="fas fa-search mr-2"></i>
                                            Check Status
                                        </button>
                                    </div>
                                </div>
                            </form>
                        </div>
                    </div>
                </div>

                <!-- Messages -->
                <?php if ($message): ?>
                    <div class="columns is-centered">
                        <div class="column is-8">
                            <div class="notification <?php echo $message_type; ?>">
                                <button class="delete" onclick="this.parentElement.style.display='none'"></button>
                                <?php echo htmlspecialchars($message); ?>
                            </div>
                        </div>
                    </div>
                <?php endif; ?>

                <!-- Student Status Results -->
                <?php if ($student_data): ?>
                    <div class="columns is-centered">
                        <div class="column is-10">
                            <!-- Student Information -->
                            <div class="box status-card">
                                <h2 class="title is-4">
                                    <i class="fas fa-user mr-2"></i>
                                    Registration Details
                                </h2>
                                <div class="columns">
                                    <div class="column is-6">
                                        <table class="table is-fullwidth">
                                            <tbody>
                                                <tr>
                                                    <td><strong>Invoice Number:</strong></td>
                                                    <td><?php echo htmlspecialchars($student_data['invoice_number']); ?></td>
                                                </tr>
                                                <tr>
                                                    <td><strong>Full Name:</strong></td>
                                                    <td><?php echo htmlspecialchars($student_data['full_name']); ?></td>
                                                </tr>
                                                <tr>
                                                    <td><strong>NIC Number:</strong></td>
                                                    <td><?php echo htmlspecialchars($student_data['nic_number']); ?></td>
                                                </tr>
                                                <tr>
                                                    <td><strong>Event:</strong></td>
                                                    <td><?php echo htmlspecialchars($student_data['event_name']); ?></td>
                                                </tr>
                                            </tbody>
                                        </table>
                                    </div>
                                    <div class="column is-6">
                                        <table class="table is-fullwidth">
                                            <tbody>
                                                <tr>
                                                    <td><strong>Session:</strong></td>
                                                    <td><?php echo htmlspecialchars($student_data['session'] ?? 'Not assigned'); ?></td>
                                                </tr>
                                                <tr>
                                                    <td><strong>Delivery Method:</strong></td>
                                                    <td><?php echo htmlspecialchars($student_data['delivery_method']); ?></td>
                                                </tr>
                                                <tr>
                                                    <td><strong>Total Amount:</strong></td>
                                                    <td>LKR <?php echo number_format($student_data['total_amount'], 2); ?></td>
                                                </tr>
                                                <tr>
                                                    <td><strong>Payment Status:</strong></td>
                                                    <td>
                                                        <?php
                                                        $payment_status = $student_data['payment_status'];
                                                        $status_class = 'is-warning';
                                                        $status_text = ucfirst(str_replace('_', ' ', $payment_status));

                                                        switch ($payment_status) {
                                                            case 'paid':
                                                                $status_class = 'is-success';
                                                                break;
                                                            case 'pending':
                                                                $status_class = 'is-warning';
                                                                $status_text = 'Payment Pending';
                                                                break;
                                                            case 'pay_at_event':
                                                                $status_class = 'is-info';
                                                                $status_text = 'Pay at Event';
                                                                break;
                                                            case 'pending_verification':
                                                                $status_class = 'is-warning';
                                                                $status_text = 'Pending Verification';
                                                                break;
                                                            default:
                                                                $status_class = 'is-light';
                                                        }
                                                        ?>
                                                        <span class="tag <?php echo $status_class; ?>">
                                                            <?php echo $status_text; ?>
                                                        </span>
                                                    </td>
                                                </tr>
                                            </tbody>
                                        </table>
                                    </div>

                                    <!-- Payment Action Button -->
                                    <?php if (in_array($student_data['payment_status'], ['pending', 'pay_at_event', 'pending_verification'])): ?>
                                        <div class="has-text-centered mt-4">
                                            <?php if ($student_data['payment_status'] === 'pending'): ?>
                                                <a href="payment.php?nic=<?php echo urlencode($student_data['nic_number']); ?>"
                                                   class="button is-primary is-large">
                                                    <i class="fas fa-credit-card mr-2"></i>
                                                    Proceed to Pay
                                                </a>
                                                <p class="help has-text-grey mt-2">
                                                    Complete your payment to confirm your booking
                                                </p>
                                            <?php elseif ($student_data['payment_status'] === 'pay_at_event'): ?>
                                                <div class="notification is-warning is-light">
                                                    <h4 class="title is-6">
                                                        <i class="fas fa-calendar-check mr-2"></i>
                                                        Pay at Event Selected
                                                    </h4>
                                                    <p>Remember to bring <strong>LKR <?php echo number_format($student_data['total_amount'], 2); ?></strong> in cash to the event.</p>
                                                    <div class="buttons is-centered mt-3">
                                                        <a href="payment.php?nic=<?php echo urlencode($student_data['nic_number']); ?>"
                                                           class="button is-info">
                                                            <i class="fas fa-edit mr-2"></i>
                                                            Change Payment Method
                                                        </a>
                                                    </div>
                                                </div>
                                            <?php elseif ($student_data['payment_status'] === 'pending_verification'): ?>
                                                <div class="notification is-info is-light">
                                                    <h4 class="title is-6">
                                                        <i class="fas fa-clock mr-2"></i>
                                                        Payment Under Verification
                                                    </h4>
                                                    <p>Your bank transfer is being verified. This usually takes 1-2 business days.</p>
                                                    <div class="buttons is-centered mt-3">
                                                        <a href="payment.php?nic=<?php echo urlencode($student_data['nic_number']); ?>"
                                                           class="button is-light">
                                                            <i class="fas fa-eye mr-2"></i>
                                                            View Payment Details
                                                        </a>
                                                    </div>
                                                </div>
                                            <?php endif; ?>
                                        </div>
                                    <?php endif; ?>
                                </div>
                            </div>

                            <!-- Current Status -->
                            <div class="box status-card">
                                <h2 class="title is-4">
                                    <i class="fas fa-info-circle mr-2"></i>
                                    Current Status
                                </h2>
                                <?php 
                                $current_status = $student_data['registration_status'];
                                $status = $status_info[$current_status] ?? $status_info['registered'];
                                ?>
                                <div class="notification <?php echo $status['color']; ?> is-light">
                                    <div class="level">
                                        <div class="level-left">
                                            <div class="level-item">
                                                <i class="<?php echo $status['icon']; ?> fa-2x mr-4"></i>
                                                <div>
                                                    <h3 class="title is-5 mb-1"><?php echo $status['label']; ?></h3>
                                                    <p><?php echo $status['description']; ?></p>
                                                    <?php if ($student_data['photo_completed_at']): ?>
                                                        <p class="is-size-7 has-text-grey">
                                                            Photo completed: <?php echo date('M j, Y H:i', strtotime($student_data['photo_completed_at'])); ?>
                                                        </p>
                                                    <?php endif; ?>
                                                </div>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </div>

                            <!-- Photo Orders -->
                            <?php if (!empty($photo_orders)): ?>
                                <div class="box status-card">
                                    <h2 class="title is-4">
                                        <i class="fas fa-images mr-2"></i>
                                        Photo Orders
                                    </h2>
                                    <div class="table-container">
                                        <table class="table is-fullwidth is-striped">
                                            <thead>
                                                <tr>
                                                    <th>Photo Type</th>
                                                    <th>Quantity</th>
                                                    <th>Unit Price</th>
                                                    <th>Total Price</th>
                                                </tr>
                                            </thead>
                                            <tbody>
                                                <?php foreach ($photo_orders as $order): ?>
                                                    <tr>
                                                        <td>
                                                            <strong><?php echo htmlspecialchars($order['type_name']); ?></strong>
                                                            <?php if ($order['description']): ?>
                                                                <br><small class="has-text-grey"><?php echo htmlspecialchars($order['description']); ?></small>
                                                            <?php endif; ?>
                                                        </td>
                                                        <td><?php echo $order['quantity']; ?></td>
                                                        <td>LKR <?php echo number_format($order['unit_price'], 2); ?></td>
                                                        <td><strong>LKR <?php echo number_format($order['total_price'], 2); ?></strong></td>
                                                    </tr>
                                                <?php endforeach; ?>
                                            </tbody>
                                        </table>
                                    </div>
                                </div>
                            <?php endif; ?>
                        </div>
                    </div>
                <?php endif; ?>

                <!-- Back Button -->
                <div class="has-text-centered mt-6">
                    <a href="index.php" class="button is-light is-large">
                        <i class="fas fa-arrow-left mr-2"></i>
                        Back to Home
                    </a>
                </div>
            </div>
        </div>
    </section>
</body>
</html>
