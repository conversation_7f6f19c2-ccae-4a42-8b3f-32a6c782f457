<?php
require_once 'config/config.php';

// Redirect if already logged in
if (is_logged_in()) {
    if (is_admin()) {
        redirect(BASE_URL . 'admin/dashboard.php');
    } else {
        redirect(BASE_URL . 'operator/dashboard.php');
    }
}

$error_message = '';
$success_message = '';

// Handle logout message
if (isset($_GET['logout'])) {
    $success_message = 'You have been logged out successfully.';
}

// Handle timeout message
if (isset($_GET['timeout'])) {
    $error_message = 'Your session has expired. Please log in again.';
}

// Handle access denied message
if (isset($_GET['error'])) {
    switch ($_GET['error']) {
        case 'access_denied':
            $error_message = 'You must be logged in to access that page.';
            break;
        case 'insufficient_privileges':
            $error_message = 'You do not have permission to access that page.';
            break;
        case 'session_expired':
            $error_message = 'Your session has expired. Please log in again.';
            break;
    }
}

// Handle login form submission
if ($_SERVER['REQUEST_METHOD'] === 'POST') {
    if (!verify_csrf_token($_POST['csrf_token'] ?? '')) {
        $error_message = 'Invalid security token. Please try again.';
    } else {
        $username = sanitize_input($_POST['username'] ?? '');
        $password = $_POST['password'] ?? '';
        
        if (empty($username) || empty($password)) {
            $error_message = 'Please enter both username and password.';
        } else {
            $auth = new Auth();
            $result = $auth->login($username, $password);
            
            if ($result['success']) {
                redirect(BASE_URL . $result['redirect']);
            } else {
                $error_message = $result['message'];
            }
        }
    }
}
?>
<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Login - <?php echo APP_NAME; ?></title>
    <link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/bulma@0.9.4/css/bulma.min.css">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css">
    <style>
        .login-container {
            min-height: 100vh;
            background: linear-gradient(135deg, #f8f9fa 0%, #e9ecef 100%);
            display: flex;
            align-items: center;
            justify-content: center;
            padding: 1rem;
        }
        .login-box {
            background: white;
            border-radius: 15px;
            box-shadow: 0 15px 35px rgba(0,0,0,0.1);
            border: 1px solid #dee2e6;
        }
        .logo-section {
            background: linear-gradient(135deg, #495057 0%, #6c757d 100%);
            color: white;
            border-radius: 15px 15px 0 0;
        }

    </style>
</head>
<body>
    <div class="login-container">
        <div class="container">
            <div class="columns is-centered is-vcentered" style="min-height: 100vh;">
                <div class="column is-4-desktop is-6-tablet is-11-mobile">
                    <div class="login-box">
                        <div class="logo-section has-text-centered py-6">
                            <img src="assets/logo.png" alt="Logo" style="width: 240px; margin-bottom: .7rem !important;">
                            <h1 class="title is-4 has-text-white mb-2"><?php echo APP_NAME; ?></h1>
                        </div>
                        
                        <div class="box">
                            <?php if ($error_message): ?>
                                <div class="notification is-danger is-light">
                                    <i class="fas fa-exclamation-triangle mr-2"></i>
                                    <?php echo htmlspecialchars($error_message); ?>
                                </div>
                            <?php endif; ?>
                            
                            <?php if ($success_message): ?>
                                <div class="notification is-success is-light">
                                    <i class="fas fa-check-circle mr-2"></i>
                                    <?php echo htmlspecialchars($success_message); ?>
                                </div>
                            <?php endif; ?>
                            
                            <form method="POST" action="">
                                <input type="hidden" name="csrf_token" value="<?php echo generate_csrf_token(); ?>">
                                
                                <div class="field">
                                    <label class="label">Username</label>
                                    <div class="control has-icons-left">
                                        <input class="input" type="text" name="username" placeholder="Enter your username" 
                                               value="<?php echo htmlspecialchars($_POST['username'] ?? ''); ?>" required>
                                        <span class="icon is-small is-left">
                                            <i class="fas fa-user"></i>
                                        </span>
                                    </div>
                                </div>
                                
                                <div class="field">
                                    <label class="label">Password</label>
                                    <div class="control has-icons-left">
                                        <input class="input" type="password" name="password" placeholder="Enter your password" required>
                                        <span class="icon is-small is-left">
                                            <i class="fas fa-lock"></i>
                                        </span>
                                    </div>
                                </div>
                                
                                <div class="field">
                                    <div class="control">
                                        <button class="button is-primary is-fullwidth" type="submit">
                                            <span class="icon">
                                                <i class="fas fa-sign-in-alt"></i>
                                            </span>
                                            <span>Login</span>
                                        </button>
                                    </div>
                                </div>
                            </form>
                            
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
    
    <script>
        // Auto-hide notifications after 5 seconds
        document.addEventListener('DOMContentLoaded', function() {
            const notifications = document.querySelectorAll('.notification');
            notifications.forEach(function(notification) {
                setTimeout(function() {
                    notification.style.display = 'none';
                }, 5000);
            });
        });
    </script>
</body>
</html>
