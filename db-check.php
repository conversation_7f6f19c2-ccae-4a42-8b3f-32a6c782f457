<?php
error_reporting(E_ALL);
ini_set('display_errors', 1);

echo "<h1>Database Check</h1>";

try {
    // Test MySQL connection
    $pdo = new PDO("mysql:host=localhost", "root", "");
    echo "<p>✅ MySQL connection successful</p>";
    
    // Check if databases exist
    $stmt = $pdo->query("SHOW DATABASES");
    $databases = $stmt->fetchAll(PDO::FETCH_COLUMN);
    
    $local_exists = in_array('photo_session_management', $databases);
    $external_exists = in_array('student_registration', $databases);
    
    echo "<h2>Database Status:</h2>";
    echo "<p>photo_session_management: " . ($local_exists ? "✅ EXISTS" : "❌ MISSING") . "</p>";
    echo "<p>student_registration: " . ($external_exists ? "✅ EXISTS" : "❌ MISSING") . "</p>";
    
    if ($local_exists) {
        // Check if admin user exists
        $local_pdo = new PDO("mysql:host=localhost;dbname=photo_session_management", "root", "");
        $stmt = $local_pdo->query("SELECT username, full_name, role FROM operators WHERE username = 'admin'");
        $admin = $stmt->fetch();
        
        if ($admin) {
            echo "<p>✅ Admin user exists: " . htmlspecialchars($admin['full_name']) . " (" . htmlspecialchars($admin['role']) . ")</p>";
            echo "<div style='background: #d4edda; padding: 15px; border: 1px solid #c3e6cb; border-radius: 5px; margin: 10px 0;'>";
            echo "<h3>✅ Ready to Login!</h3>";
            echo "<p>Try logging in with:</p>";
            echo "<ul>";
            echo "<li><strong>Username:</strong> admin</li>";
            echo "<li><strong>Password:</strong> admin123</li>";
            echo "</ul>";
            echo "<p><a href='login.php' class='button'>Go to Login Page</a></p>";
            echo "</div>";
        } else {
            echo "<p>❌ Admin user not found</p>";
            echo "<div style='background: #fff3cd; padding: 15px; border: 1px solid #ffeaa7; border-radius: 5px; margin: 10px 0;'>";
            echo "<h3>⚠️ Admin User Missing</h3>";
            echo "<p>The database exists but the admin user wasn't created. This means the SQL import was incomplete.</p>";
            echo "<p><strong>Solution:</strong> Re-run the database/local_database.sql file in phpMyAdmin</p>";
            echo "</div>";
        }
    }
    
    if (!$local_exists || !$external_exists) {
        echo "<div style='background: #f8d7da; padding: 15px; border: 1px solid #f5c6cb; border-radius: 5px; margin: 10px 0;'>";
        echo "<h3>❌ Databases Missing</h3>";
        echo "<p><strong>You need to create the databases:</strong></p>";
        echo "<ol>";
        echo "<li>Go to <a href='http://localhost/phpmyadmin' target='_blank'>phpMyAdmin</a></li>";
        echo "<li>Click 'SQL' tab</li>";
        echo "<li>Copy and paste ALL content from <code>database/local_database.sql</code></li>";
        echo "<li>Click 'Go'</li>";
        echo "<li>Repeat for <code>database/external_database.sql</code></li>";
        echo "</ol>";
        echo "</div>";
    }
    
} catch (Exception $e) {
    echo "<p>❌ Database error: " . htmlspecialchars($e->getMessage()) . "</p>";
    echo "<div style='background: #f8d7da; padding: 15px; border: 1px solid #f5c6cb; border-radius: 5px; margin: 10px 0;'>";
    echo "<h3>🔧 Fix MySQL Connection</h3>";
    echo "<ol>";
    echo "<li>Open XAMPP Control Panel</li>";
    echo "<li>Make sure MySQL service is running</li>";
    echo "<li>Refresh this page</li>";
    echo "</ol>";
    echo "</div>";
}
?>

<style>
body { font-family: Arial, sans-serif; margin: 20px; }
.button { 
    display: inline-block; 
    padding: 10px 20px; 
    background: #007bff; 
    color: white; 
    text-decoration: none; 
    border-radius: 5px; 
}
</style>
