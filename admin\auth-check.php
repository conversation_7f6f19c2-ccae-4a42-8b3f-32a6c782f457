<?php
/**
 * Authentication Check for Admin Directory
 * This file must be included at the top of every admin page
 * Ensures only logged-in admin users can access admin functionality
 */

// Start session if not already started
if (session_status() === PHP_SESSION_NONE) {
    session_start();
}

// Include config for authentication functions
require_once '../config/config.php';

// Check if user is logged in
if (!is_logged_in()) {
    // Log unauthorized access attempt
    $ip = $_SERVER['REMOTE_ADDR'] ?? 'unknown';
    $user_agent = $_SERVER['HTTP_USER_AGENT'] ?? 'unknown';
    $requested_page = $_SERVER['REQUEST_URI'] ?? 'unknown';
    
    error_log("Unauthorized admin access attempt - IP: {$ip}, Page: {$requested_page}, User-Agent: {$user_agent}");
    
    // Store the attempted URL for redirect after login
    $_SESSION['redirect_after_login'] = $_SERVER['REQUEST_URI'];
    
    // Redirect to login page
    header('Location: ../login.php?error=access_denied');
    exit;
}

// Check if user has admin role
if (!is_admin()) {
    // Log unauthorized admin access attempt
    $username = $_SESSION['username'] ?? 'unknown';
    $role = $_SESSION['role'] ?? 'unknown';
    $requested_page = $_SERVER['REQUEST_URI'] ?? 'unknown';
    
    error_log("Non-admin user attempted admin access - User: {$username}, Role: {$role}, Page: {$requested_page}");
    
    // Redirect to appropriate dashboard based on role
    if ($_SESSION['role'] === 'operator') {
        header('Location: ../operator/dashboard.php?error=insufficient_privileges');
    } else {
        header('Location: ../login.php?error=insufficient_privileges');
    }
    exit;
}

// Update last activity timestamp
$_SESSION['last_activity'] = time();

// Optional: Check for session timeout (30 minutes)
$session_timeout = 30 * 60; // 30 minutes in seconds
if (isset($_SESSION['last_activity']) && (time() - $_SESSION['last_activity'] > $session_timeout)) {
    // Session has expired
    session_unset();
    session_destroy();
    
    error_log("Session expired for user: " . ($_SESSION['username'] ?? 'unknown'));
    
    header('Location: ../login.php?error=session_expired');
    exit;
}

// Log successful access (optional - can be disabled for performance)
if (defined('LOG_ADMIN_ACCESS') && LOG_ADMIN_ACCESS === true) {
    $username = $_SESSION['username'] ?? 'unknown';
    $page = basename($_SERVER['PHP_SELF']);
    log_activity('ADMIN_ACCESS', "Admin {$username} accessed {$page}");
}
?>
