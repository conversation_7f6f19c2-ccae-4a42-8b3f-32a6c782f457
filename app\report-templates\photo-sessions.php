<?php if (!isset($reportData)) exit; ?>

<!-- Summary Statistics -->
<div class="columns">
    <div class="column">
        <div class="box stat-card">
            <div class="level">
                <div class="level-left">
                    <div>
                        <p class="heading">Total Sessions</p>
                        <p class="title is-3"><?php echo number_format($reportData['summary']['total_sessions']); ?></p>
                    </div>
                </div>
                <div class="level-right">
                    <i class="fas fa-camera fa-2x has-text-info"></i>
                </div>
            </div>
        </div>
    </div>
    <div class="column">
        <div class="box stat-card">
            <div class="level">
                <div class="level-left">
                    <div>
                        <p class="heading">Date Range</p>
                        <p class="title is-6"><?php echo htmlspecialchars($reportData['summary']['date_range']); ?></p>
                    </div>
                </div>
                <div class="level-right">
                    <i class="fas fa-calendar fa-2x has-text-success"></i>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Sessions Table -->
<div class="box">
    <h2 class="title is-5">
        <i class="fas fa-list mr-2"></i>
        Photo Sessions Details
    </h2>
    
    <?php if (empty($reportData['sessions'])): ?>
        <div class="has-text-centered py-6">
            <i class="fas fa-inbox fa-3x has-text-grey-light mb-4"></i>
            <p class="title is-5 has-text-grey">No sessions found</p>
            <p class="has-text-grey">No photo sessions were recorded in the selected date range</p>
        </div>
    <?php else: ?>
        <div class="table-container">
            <table class="table is-fullwidth is-striped">
                <thead>
                    <tr>
                        <th>Invoice</th>
                        <th>Student Name</th>
                        <th>NIC</th>
                        <th>Session</th>
                        <th>Sequence #</th>
                        <th>Operator</th>
                        <th>Completed At</th>
                    </tr>
                </thead>
                <tbody>
                    <?php foreach ($reportData['sessions'] as $session): ?>
                        <tr>
                            <td><strong><?php echo htmlspecialchars($session['invoice_number']); ?></strong></td>
                            <td><?php echo htmlspecialchars($session['student_name']); ?></td>
                            <td><?php echo htmlspecialchars($session['student_nic']); ?></td>
                            <td><?php echo htmlspecialchars($session['session']); ?></td>
                            <td>
                                <span class="tag is-primary">
                                    <?php echo htmlspecialchars($session['photo_sequence_number']); ?>
                                </span>
                            </td>
                            <td><?php echo htmlspecialchars($session['operator_name']); ?></td>
                            <td><?php echo date('M j, Y H:i', strtotime($session['completed_at'])); ?></td>
                        </tr>
                    <?php endforeach; ?>
                </tbody>
            </table>
        </div>
    <?php endif; ?>
</div>
