<?php
require_once '../config/config.php';

if (session_status() === PHP_SESSION_NONE) {
    session_start();
}

// Check if payment method is selected and student data exists
if (!isset($_SESSION['selected_payment_method']) || $_SESSION['selected_payment_method'] !== 'bank_transfer' || 
    !isset($_SESSION['payment_student_data'])) {
    redirect('payment.php');
}

$student_data = $_SESSION['payment_student_data'];
$message = '';
$message_type = '';

// Handle bank slip upload
if ($_SERVER['REQUEST_METHOD'] === 'POST' && isset($_POST['action']) && $_POST['action'] === 'upload_slip') {
    $transfer_reference = sanitize_input($_POST['transfer_reference'] ?? '');
    $transfer_date = sanitize_input($_POST['transfer_date'] ?? '');
    $transfer_amount = floatval($_POST['transfer_amount'] ?? 0);
    
    // Basic validation
    if (empty($transfer_reference) || empty($transfer_date) || $transfer_amount <= 0) {
        $message = 'Please fill in all transfer details.';
        $message_type = 'is-danger';
    } elseif ($transfer_amount != $student_data['total_amount']) {
        $message = 'Transfer amount must match the total amount: LKR ' . number_format($student_data['total_amount'], 2);
        $message_type = 'is-danger';
    } else {
        // Handle file upload
        $upload_success = false;
        $slip_filename = '';
        
        if (isset($_FILES['bank_slip']) && $_FILES['bank_slip']['error'] === UPLOAD_ERR_OK) {
            $upload_dir = '../uploads/bank_slips/';
            
            // Create directory if it doesn't exist
            if (!is_dir($upload_dir)) {
                mkdir($upload_dir, 0755, true);
            }
            
            $file_extension = strtolower(pathinfo($_FILES['bank_slip']['name'], PATHINFO_EXTENSION));
            $allowed_extensions = ['jpg', 'jpeg', 'png', 'pdf'];
            
            if (in_array($file_extension, $allowed_extensions)) {
                $slip_filename = $student_data['invoice_number'] . '_' . time() . '.' . $file_extension;
                $upload_path = $upload_dir . $slip_filename;
                
                if (move_uploaded_file($_FILES['bank_slip']['tmp_name'], $upload_path)) {
                    $upload_success = true;
                }
            } else {
                $message = 'Please upload a valid file (JPG, PNG, or PDF).';
                $message_type = 'is-danger';
            }
        } else {
            $message = 'Please upload your bank slip.';
            $message_type = 'is-danger';
        }
        
        if ($upload_success) {
            try {
                $pdo = get_pdo_connection();
                
                // Update payment status to pending verification
                $stmt = $pdo->prepare("
                    UPDATE students 
                    SET payment_status = 'pending_verification', 
                        payment_method = 'Bank Transfer',
                        payment_reference = ?,
                        payment_slip = ?,
                        payment_date = ?,
                        updated_at = NOW()
                    WHERE invoice_number = ?
                ");
                $stmt->execute([$transfer_reference, $slip_filename, $transfer_date, $student_data['invoice_number']]);
                
                // Log payment activity
                log_activity('BANK_TRANSFER_SUBMITTED', "Bank transfer slip uploaded for invoice: {$student_data['invoice_number']}");
                
                // Clear payment session data
                unset($_SESSION['selected_payment_method']);
                unset($_SESSION['payment_student_data']);
                unset($_SESSION['payment_invoice']);
                unset($_SESSION['payment_attempt_made']);
                
                // Store success data
                $_SESSION['payment_success'] = [
                    'invoice_number' => $student_data['invoice_number'],
                    'amount' => $student_data['total_amount'],
                    'method' => 'Bank Transfer',
                    'status' => 'pending_verification'
                ];

                redirect('payment-invoice.php');
                
            } catch (Exception $e) {
                $message = 'Error processing bank transfer. Please try again.';
                $message_type = 'is-danger';
                error_log("Bank transfer error: " . $e->getMessage());
            }
        }
    }
}
?>
<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Bank Transfer - <?php echo APP_NAME; ?></title>
    <link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/bulma@0.9.4/css/bulma.min.css">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css">
    <style>
        .hero-section {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
        }
        .payment-card {
            background: white;
            border-radius: 15px;
            box-shadow: 0 10px 30px rgba(0,0,0,0.1);
            overflow: hidden;
        }
        .bank-details {
            background: #f8f9fa;
            border: 2px solid #e9ecef;
            border-radius: 12px;
            padding: 1.5rem;
            margin-bottom: 2rem;
        }
        .bank-info {
            background: linear-gradient(135deg, #17a2b8 0%, #138496 100%);
            color: white;
            border-radius: 8px;
            padding: 1rem;
            margin-bottom: 1rem;
        }
        .upload-area {
            border: 2px dashed #dbdbdb;
            border-radius: 8px;
            padding: 2rem;
            text-align: center;
            transition: all 0.3s;
            cursor: pointer;
        }
        .upload-area:hover {
            border-color: #3273dc;
            background-color: #f8f9fa;
        }
        .upload-area.dragover {
            border-color: #48c78e;
            background-color: #f0fff4;
        }
        .input, .select select {
            font-size: 0.9rem;
        }

        /* Custom select dropdown arrow positioning */
        .select:not(.is-multiple):not(.is-loading)::after {
            margin-top: -0.9em !important;
            transform: none !important;
        }
        .step-indicator {
            display: flex;
            justify-content: space-between;
            margin-bottom: 2rem;
        }
        .step {
            flex: 1;
            text-align: center;
            padding: 1rem;
            border-radius: 8px;
            margin: 0 0.5rem;
        }
        .step.active {
            background: #3273dc;
            color: white;
        }
        .step.completed {
            background: #48c78e;
            color: white;
        }
        .step.pending {
            background: #f5f5f5;
            color: #7a7a7a;
        }
        .step-number {
            font-weight: bold;
            font-size: 0.9rem;
        }
        .step-label {
            font-size: 0.75rem;
            margin-top: 0.25rem;
        }
    </style>
</head>
<body>
    <section class="hero hero-section">
        <div class="hero-body">
            <div class="container">
                <!-- Header -->
                <div class="has-text-centered mb-6">
                    <h1 class="title is-2 has-text-white">
                        <i class="fas fa-university mr-3"></i>
                        Bank Transfer
                    </h1>
                    <p class="subtitle is-4 has-text-white">
                        Transfer to our bank account and upload your receipt
                    </p>
                </div>

                <!-- Messages -->
                <?php if ($message): ?>
                    <div class="columns is-centered">
                        <div class="column is-8">
                            <div class="notification <?php echo $message_type; ?>">
                                <button class="delete" onclick="this.parentElement.style.display='none'"></button>
                                <?php echo htmlspecialchars($message); ?>
                            </div>
                        </div>
                    </div>
                <?php endif; ?>

                <!-- Payment Process -->
                <div class="columns is-centered">
                    <div class="column is-10">
                        <div class="box payment-card">
                            
                            <!-- Main Step Indicator -->
                            <div class="step-indicator">
                                <div class="step completed">
                                    <div class="step-number">1</div>
                                    <div class="step-label">Verification</div>
                                </div>
                                <div class="step completed">
                                    <div class="step-number">2</div>
                                    <div class="step-label">Details</div>
                                </div>
                                <div class="step completed">
                                    <div class="step-number">3</div>
                                    <div class="step-label">Photos</div>
                                </div>
                                <div class="step completed">
                                    <div class="step-number">4</div>
                                    <div class="step-label">Review</div>
                                </div>
                                <div class="step active">
                                    <div class="step-number">5</div>
                                    <div class="step-label">Payment</div>
                                </div>
                                <div class="step pending">
                                    <div class="step-number">6</div>
                                    <div class="step-label">Invoice</div>
                                </div>
                            </div>

                            <!-- Bank Transfer Steps -->
                            <div class="step-indicator">
                                <div class="step completed">
                                    <div class="title is-6">1. Transfer Money</div>
                                    <p class="is-size-7">Make bank transfer</p>
                                </div>
                                <div class="step active">
                                    <div class="title is-6">2. Upload Receipt</div>
                                    <p class="is-size-7">Upload bank slip</p>
                                </div>
                                <div class="step pending">
                                    <div class="title is-6">3. Verification</div>
                                    <p class="is-size-7">We verify payment</p>
                                </div>
                            </div>
                            
                            <div class="columns">
                                <!-- Bank Details & Order Summary -->
                                <div class="column is-5">
                                    <h2 class="title is-5">
                                        <i class="fas fa-university mr-2"></i>
                                        Bank Account Details
                                    </h2>
                                    
                                    <div class="bank-details">
                                        <div class="bank-info">
                                            <h3 class="title is-6 has-text-white">
                                                <i class="fas fa-building mr-2"></i>
                                                Commercial Bank of Ceylon
                                            </h3>
                                            <div class="content has-text-white">
                                                <p><strong>Account Name:</strong> Photo Management System</p>
                                                <p><strong>Account Number:</strong> *************</p>
                                                <p><strong>Branch:</strong> Colombo Main Branch</p>
                                                <p><strong>Branch Code:</strong> 001</p>
                                            </div>
                                        </div>
                                        
                                        <div class="notification is-warning is-light">
                                            <h4 class="title is-6">
                                                <i class="fas fa-exclamation-triangle mr-2"></i>
                                                Important Instructions
                                            </h4>
                                            <ul class="is-size-7">
                                                <li>Transfer the <strong>exact amount</strong> shown below</li>
                                                <li>Use your <strong>invoice number</strong> as reference</li>
                                                <li>Keep your bank slip for upload</li>
                                                <li>Processing takes 1-2 business days</li>
                                            </ul>
                                        </div>
                                    </div>
                                    
                                    <h3 class="title is-5">
                                        <i class="fas fa-receipt mr-2"></i>
                                        Transfer Details
                                    </h3>
                                    <div class="content">
                                        <p><strong>Invoice:</strong> <?php echo htmlspecialchars($student_data['invoice_number']); ?></p>
                                        <p><strong>Name:</strong> <?php echo htmlspecialchars($student_data['full_name']); ?></p>
                                        <p><strong>Event:</strong> <?php echo htmlspecialchars($student_data['event_name']); ?></p>
                                        <hr>
                                        <p><strong>Amount to Transfer:</strong></p>
                                        <p class="title is-3 has-text-primary">LKR <?php echo number_format($student_data['total_amount'], 2); ?></p>
                                        <p><strong>Reference:</strong> <?php echo htmlspecialchars($student_data['invoice_number']); ?></p>
                                    </div>
                                </div>
                                
                                <!-- Upload Form -->
                                <div class="column is-7">
                                    <h2 class="title is-5">
                                        <i class="fas fa-upload mr-2"></i>
                                        Upload Bank Slip
                                    </h2>
                                    
                                    <form method="POST" action="" enctype="multipart/form-data">
                                        <input type="hidden" name="action" value="upload_slip">
                                        
                                        <div class="field">
                                            <label class="label">Transfer Reference Number</label>
                                            <div class="control has-icons-left">
                                                <input class="input" type="text" name="transfer_reference" 
                                                       placeholder="Enter reference number from your bank slip" required>
                                                <span class="icon is-left">
                                                    <i class="fas fa-hashtag"></i>
                                                </span>
                                            </div>
                                            <p class="help">This is usually found on your bank slip or online transfer receipt</p>
                                        </div>
                                        
                                        <div class="columns">
                                            <div class="column is-6">
                                                <div class="field">
                                                    <label class="label">Transfer Date</label>
                                                    <div class="control has-icons-left">
                                                        <input class="input" type="date" name="transfer_date" 
                                                               max="<?php echo date('Y-m-d'); ?>" required>
                                                        <span class="icon is-left">
                                                            <i class="fas fa-calendar"></i>
                                                        </span>
                                                    </div>
                                                </div>
                                            </div>
                                            <div class="column is-6">
                                                <div class="field">
                                                    <label class="label">Transfer Amount (LKR)</label>
                                                    <div class="control has-icons-left">
                                                        <input class="input" type="number" name="transfer_amount" 
                                                               value="<?php echo $student_data['total_amount']; ?>" 
                                                               step="0.01" min="0" required readonly>
                                                        <span class="icon is-left">
                                                            <i class="fas fa-dollar-sign"></i>
                                                        </span>
                                                    </div>
                                                </div>
                                            </div>
                                        </div>
                                        
                                        <div class="field">
                                            <label class="label">Bank Slip / Receipt</label>
                                            <div class="upload-area" onclick="document.getElementById('bankSlip').click()">
                                                <input type="file" id="bankSlip" name="bank_slip" 
                                                       accept=".jpg,.jpeg,.png,.pdf" required style="display: none;">
                                                <div class="upload-content">
                                                    <i class="fas fa-cloud-upload-alt fa-3x has-text-grey-light mb-3"></i>
                                                    <p class="title is-6">Click to upload bank slip</p>
                                                    <p class="subtitle is-7 has-text-grey">
                                                        Supported formats: JPG, PNG, PDF<br>
                                                        Maximum size: 5MB
                                                    </p>
                                                </div>
                                            </div>
                                            <div id="fileName" class="has-text-success mt-2" style="display: none;"></div>
                                        </div>
                                        
                                        <div class="field mt-5">
                                            <div class="control">
                                                <button class="button is-info is-large is-fullwidth" type="submit">
                                                    <i class="fas fa-upload mr-2"></i>
                                                    Submit Bank Transfer Details
                                                </button>
                                            </div>
                                        </div>
                                        
                                        <div class="field">
                                            <div class="control">
                                                <a href="payment.php" class="button is-light is-fullwidth">
                                                    <i class="fas fa-arrow-left mr-2"></i>
                                                    Back to Payment Options
                                                </a>
                                            </div>
                                        </div>
                                    </form>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </section>

    <script>
        // File upload handling
        document.getElementById('bankSlip').addEventListener('change', function(e) {
            const file = e.target.files[0];
            const fileName = document.getElementById('fileName');
            
            if (file) {
                fileName.innerHTML = `<i class="fas fa-file mr-2"></i>Selected: ${file.name}`;
                fileName.style.display = 'block';
            } else {
                fileName.style.display = 'none';
            }
        });
        
        // Drag and drop functionality
        const uploadArea = document.querySelector('.upload-area');
        
        uploadArea.addEventListener('dragover', function(e) {
            e.preventDefault();
            uploadArea.classList.add('dragover');
        });
        
        uploadArea.addEventListener('dragleave', function(e) {
            e.preventDefault();
            uploadArea.classList.remove('dragover');
        });
        
        uploadArea.addEventListener('drop', function(e) {
            e.preventDefault();
            uploadArea.classList.remove('dragover');
            
            const files = e.dataTransfer.files;
            if (files.length > 0) {
                document.getElementById('bankSlip').files = files;
                document.getElementById('bankSlip').dispatchEvent(new Event('change'));
            }
        });
        
        // Auto-hide notifications
        document.addEventListener('DOMContentLoaded', function() {
            const notifications = document.querySelectorAll('.notification');
            notifications.forEach(function(notification) {
                setTimeout(function() {
                    if (notification.parentElement) {
                        notification.style.display = 'none';
                    }
                }, 5000);
            });
        });
    </script>
</body>
</html>
