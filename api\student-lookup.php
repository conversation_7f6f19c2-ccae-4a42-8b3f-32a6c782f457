<?php
/**
 * API endpoint for student lookup by invoice number
 */

require_once '../config/config.php';

// Set JSON response headers
header('Content-Type: application/json');
header('Access-Control-Allow-Origin: *');
header('Access-Control-Allow-Methods: GET, POST, OPTIONS');
header('Access-Control-Allow-Headers: Content-Type');

// Handle preflight requests
if ($_SERVER['REQUEST_METHOD'] === 'OPTIONS') {
    http_response_code(200);
    exit;
}

// Require authentication
if (!is_logged_in()) {
    json_response(['success' => false, 'message' => 'Authentication required'], 401);
}

try {
    $invoice_number = '';
    
    if ($_SERVER['REQUEST_METHOD'] === 'GET') {
        $invoice_number = sanitize_input($_GET['invoice'] ?? '');
    } elseif ($_SERVER['REQUEST_METHOD'] === 'POST') {
        $input = json_decode(file_get_contents('php://input'), true);
        $invoice_number = sanitize_input($input['invoice'] ?? '');
    }
    
    if (empty($invoice_number)) {
        json_response(['success' => false, 'message' => 'Invoice number is required'], 400);
    }
    
    $studentService = new StudentService();
    $result = $studentService->getStudentByInvoice($invoice_number);
    
    // Log the lookup attempt
    log_activity('STUDENT_LOOKUP', "Looked up student with invoice: {$invoice_number}");
    
    json_response($result);
    
} catch (Exception $e) {
    error_log("Student lookup API error: " . $e->getMessage());
    json_response(['success' => false, 'message' => 'Internal server error'], 500);
}
?>
