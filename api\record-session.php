<?php
/**
 * API endpoint for recording photo sessions
 */

require_once '../config/config.php';

// Set JSON response headers
header('Content-Type: application/json');
header('Access-Control-Allow-Origin: *');
header('Access-Control-Allow-Methods: POST, OPTIONS');
header('Access-Control-Allow-Headers: Content-Type');

// Handle preflight requests
if ($_SERVER['REQUEST_METHOD'] === 'OPTIONS') {
    http_response_code(200);
    exit;
}

// Only allow POST requests
if ($_SERVER['REQUEST_METHOD'] !== 'POST') {
    json_response(['success' => false, 'message' => 'Method not allowed'], 405);
}

// Require authentication
if (!is_logged_in()) {
    json_response(['success' => false, 'message' => 'Authentication required'], 401);
}

try {
    $input = json_decode(file_get_contents('php://input'), true);
    
    if (!$input) {
        json_response(['success' => false, 'message' => 'Invalid JSON data'], 400);
    }
    
    // Validate CSRF token if provided
    if (isset($input['csrf_token']) && !verify_csrf_token($input['csrf_token'])) {
        json_response(['success' => false, 'message' => 'Invalid security token'], 403);
    }
    
    // Prepare session data
    $session_data = [
        'invoice_number' => sanitize_input($input['invoice_number'] ?? ''),
        'student_nic' => sanitize_input($input['student_nic'] ?? ''),
        'student_name' => sanitize_input($input['student_name'] ?? ''),
        'student_address' => sanitize_input($input['student_address'] ?? ''),
        'whatsapp_number' => sanitize_input($input['whatsapp_number'] ?? ''),
        'alternate_phone' => sanitize_input($input['alternate_phone'] ?? ''),
        'session' => sanitize_input($input['session'] ?? ''),
        'seat_number' => sanitize_input($input['seat_number'] ?? ''),
        'photo_types' => $input['photo_types'] ?? [],
        'photo_sequence_number' => sanitize_input($input['photo_sequence_number'] ?? ''),
        'operator_id' => $_SESSION['user_id'],
        'notes' => sanitize_input($input['notes'] ?? '')
    ];
    
    $photoSessionService = new PhotoSessionService();
    $result = $photoSessionService->recordPhotoSession($session_data);
    
    json_response($result);
    
} catch (Exception $e) {
    error_log("Record session API error: " . $e->getMessage());
    json_response(['success' => false, 'message' => 'Internal server error'], 500);
}
?>
