-- Unified Database Schema for Complete Photo Management System
-- This combines both operator management and student registration systems

CREATE DATABASE IF NOT EXISTS `photo_management_system` DEFAULT CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci;
USE `photo_management_system`;

-- =============================================
-- OPERATOR MANAGEMENT TABLES
-- =============================================

-- Operators table (Admin and Operator users)
CREATE TABLE `operators` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `username` varchar(50) NOT NULL UNIQUE,
  `password_hash` varchar(255) NOT NULL,
  `full_name` varchar(100) NOT NULL,
  `role` enum('admin','operator') NOT NULL DEFAULT 'operator',
  `is_active` tinyint(1) NOT NULL DEFAULT 1,
  `created_by` int(11) DEFAULT NULL,
  `created_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP,
  `updated_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  `last_login` timestamp NULL DEFAULT NULL,
  PRIMARY KEY (`id`),
  KEY `idx_username` (`username`),
  KEY `idx_role` (`role`),
  KEY `fk_created_by` (`created_by`),
  CONSTRAINT `fk_operators_created_by` FOREIGN KEY (`created_by`) REFERENCES `operators` (`id`) ON DELETE SET NULL
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

-- System settings
CREATE TABLE `system_settings` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `setting_key` varchar(100) NOT NULL UNIQUE,
  `setting_value` text,
  `description` varchar(255) DEFAULT NULL,
  `updated_by` int(11) DEFAULT NULL,
  `updated_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  PRIMARY KEY (`id`),
  KEY `idx_setting_key` (`setting_key`),
  KEY `fk_updated_by` (`updated_by`),
  CONSTRAINT `fk_settings_updated_by` FOREIGN KEY (`updated_by`) REFERENCES `operators` (`id`) ON DELETE SET NULL
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

-- Session logs for activity tracking
CREATE TABLE `session_logs` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `user_id` int(11) DEFAULT NULL,
  `username` varchar(50) DEFAULT NULL,
  `action` varchar(100) NOT NULL,
  `description` text,
  `ip_address` varchar(45) DEFAULT NULL,
  `user_agent` text,
  `created_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP,
  PRIMARY KEY (`id`),
  KEY `idx_user_id` (`user_id`),
  KEY `idx_action` (`action`),
  KEY `idx_created_at` (`created_at`),
  CONSTRAINT `fk_logs_user_id` FOREIGN KEY (`user_id`) REFERENCES `operators` (`id`) ON DELETE SET NULL
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

-- =============================================
-- STUDENT REGISTRATION TABLES
-- =============================================

-- Events (Convocation, Graduation, etc.)
CREATE TABLE `events` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `event_name` varchar(100) NOT NULL,
  `event_code` varchar(20) NOT NULL UNIQUE,
  `event_date` date DEFAULT NULL,
  `description` text,
  `is_active` tinyint(1) NOT NULL DEFAULT 1,
  `created_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP,
  `updated_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  PRIMARY KEY (`id`),
  KEY `idx_event_code` (`event_code`),
  KEY `idx_is_active` (`is_active`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

-- Photo types with pricing
CREATE TABLE `photo_types` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `type_name` varchar(100) NOT NULL,
  `type_code` varchar(20) NOT NULL,
  `description` text,
  `is_active` tinyint(1) NOT NULL DEFAULT 1,
  `created_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP,
  PRIMARY KEY (`id`),
  KEY `idx_type_code` (`type_code`),
  KEY `idx_is_active` (`is_active`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

-- Event-specific photo pricing
CREATE TABLE `event_photo_pricing` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `event_id` int(11) NOT NULL,
  `photo_type_id` int(11) NOT NULL,
  `price` decimal(10,2) NOT NULL DEFAULT 0.00,
  `currency` varchar(3) NOT NULL DEFAULT 'LKR',
  `is_active` tinyint(1) NOT NULL DEFAULT 1,
  `created_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP,
  `updated_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  PRIMARY KEY (`id`),
  UNIQUE KEY `unique_event_photo_type` (`event_id`, `photo_type_id`),
  KEY `fk_event_pricing` (`event_id`),
  KEY `fk_photo_type_pricing` (`photo_type_id`),
  CONSTRAINT `fk_event_pricing` FOREIGN KEY (`event_id`) REFERENCES `events` (`id`) ON DELETE CASCADE,
  CONSTRAINT `fk_photo_type_pricing` FOREIGN KEY (`photo_type_id`) REFERENCES `photo_types` (`id`) ON DELETE CASCADE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

-- Delivery methods
CREATE TABLE `delivery_methods` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `method_name` varchar(100) NOT NULL,
  `method_code` varchar(20) NOT NULL UNIQUE,
  `description` text,
  `additional_cost` decimal(10,2) NOT NULL DEFAULT 0.00,
  `is_active` tinyint(1) NOT NULL DEFAULT 1,
  `created_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP,
  PRIMARY KEY (`id`),
  KEY `idx_method_code` (`method_code`),
  KEY `idx_is_active` (`is_active`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

-- Students registration
CREATE TABLE `students` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `invoice_number` varchar(50) NOT NULL UNIQUE,
  `nic_number` varchar(20) NOT NULL,
  `full_name` varchar(150) NOT NULL,
  `email` varchar(100) DEFAULT NULL,
  `phone_number` varchar(20) DEFAULT NULL,
  `whatsapp_number` varchar(20) DEFAULT NULL,
  `address` text,
  `event_id` int(11) NOT NULL,
  `session` varchar(100) DEFAULT NULL,
  `seat_number` varchar(20) DEFAULT NULL,
  `delivery_method_id` int(11) NOT NULL,
  `delivery_address` text,
  `total_amount` decimal(10,2) NOT NULL DEFAULT 0.00,
  `payment_status` enum('pending','paid','failed','refunded') NOT NULL DEFAULT 'pending',
  `registration_status` enum('registered','photo_taken','in_development','dispatched','ready_for_pickup','completed') NOT NULL DEFAULT 'registered',
  `notes` text,
  `created_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP,
  `updated_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  PRIMARY KEY (`id`),
  UNIQUE KEY `unique_nic_event` (`nic_number`, `event_id`),
  KEY `idx_invoice_number` (`invoice_number`),
  KEY `idx_nic_number` (`nic_number`),
  KEY `idx_registration_status` (`registration_status`),
  KEY `idx_payment_status` (`payment_status`),
  KEY `fk_student_event` (`event_id`),
  KEY `fk_student_delivery` (`delivery_method_id`),
  CONSTRAINT `fk_student_event` FOREIGN KEY (`event_id`) REFERENCES `events` (`id`) ON DELETE RESTRICT,
  CONSTRAINT `fk_student_delivery` FOREIGN KEY (`delivery_method_id`) REFERENCES `delivery_methods` (`id`) ON DELETE RESTRICT
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

-- Student photo orders (many-to-many relationship)
CREATE TABLE `student_photo_orders` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `student_id` int(11) NOT NULL,
  `photo_type_id` int(11) NOT NULL,
  `quantity` int(11) NOT NULL DEFAULT 1,
  `unit_price` decimal(10,2) NOT NULL,
  `total_price` decimal(10,2) NOT NULL,
  `created_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP,
  PRIMARY KEY (`id`),
  KEY `fk_order_student` (`student_id`),
  KEY `fk_order_photo_type` (`photo_type_id`),
  CONSTRAINT `fk_order_student` FOREIGN KEY (`student_id`) REFERENCES `students` (`id`) ON DELETE CASCADE,
  CONSTRAINT `fk_order_photo_type` FOREIGN KEY (`photo_type_id`) REFERENCES `photo_types` (`id`) ON DELETE CASCADE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

-- Photo sessions (completed photos)
CREATE TABLE `photo_sessions` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `student_id` int(11) NOT NULL,
  `operator_id` int(11) NOT NULL,
  `photo_sequence_number` varchar(50) NOT NULL,
  `session_notes` text,
  `completed_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP,
  PRIMARY KEY (`id`),
  KEY `idx_sequence_number` (`photo_sequence_number`),
  KEY `idx_completed_at` (`completed_at`),
  KEY `fk_session_student` (`student_id`),
  KEY `fk_session_operator` (`operator_id`),
  CONSTRAINT `fk_session_student` FOREIGN KEY (`student_id`) REFERENCES `students` (`id`) ON DELETE CASCADE,
  CONSTRAINT `fk_session_operator` FOREIGN KEY (`operator_id`) REFERENCES `operators` (`id`) ON DELETE RESTRICT
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

-- =============================================
-- INSERT DEFAULT DATA
-- =============================================

-- Insert default admin user
INSERT INTO `operators` (`username`, `password_hash`, `full_name`, `role`, `is_active`) VALUES
('admin', '$2y$10$92IXUNpkjO0rOQ5byMi.Ye4oKoEa3Ro9llC/.og/at2.uheWG/igi', 'System Administrator', 'admin', 1);

-- Insert default system settings
INSERT INTO `system_settings` (`setting_key`, `setting_value`, `description`) VALUES
('app_name', 'Photo Management System', 'Application name'),
('session_timeout', '3600', 'Session timeout in seconds'),
('max_login_attempts', '5', 'Maximum login attempts before lockout'),
('payment_gateway_enabled', '0', 'Enable payment gateway integration'),
('default_currency', 'LKR', 'Default currency for pricing');

-- Insert default events
INSERT INTO `events` (`event_name`, `event_code`, `event_date`, `description`) VALUES
('University Convocation 2024', 'CONV2024', '2024-12-15', 'Annual university convocation ceremony'),
('Graduation Ceremony 2024', 'GRAD2024', '2024-11-30', 'Faculty graduation ceremony');

-- Insert photo types
INSERT INTO `photo_types` (`type_name`, `type_code`, `description`) VALUES
('Single Full Photo', 'SINGLE_FULL', 'Individual full body photograph'),
('Single Bust Photo', 'SINGLE_BUST', 'Individual upper body photograph'),
('Stage Photo', 'STAGE', 'Photograph taken on stage during ceremony'),
('Family Photo', 'FAMILY', 'Family group photograph'),
('Couple Photo', 'COUPLE', 'Photograph with partner/spouse'),
('Group Photo', 'GROUP', 'Group photograph with friends/colleagues');

-- Insert delivery methods
INSERT INTO `delivery_methods` (`method_name`, `method_code`, `description`, `additional_cost`) VALUES
('Courier Service', 'COURIER', 'Home delivery via courier service', 500.00),
('Collection Center Pickup', 'PICKUP', 'Pickup from designated collection center', 0.00);

-- Insert default pricing for events
INSERT INTO `event_photo_pricing` (`event_id`, `photo_type_id`, `price`) VALUES
-- Convocation 2024 pricing
(1, 1, 1500.00), -- Single Full Photo
(1, 2, 1200.00), -- Single Bust Photo
(1, 3, 2000.00), -- Stage Photo
(1, 4, 3000.00), -- Family Photo
(1, 5, 2500.00), -- Couple Photo
(1, 6, 3500.00), -- Group Photo
-- Graduation 2024 pricing
(2, 1, 1400.00), -- Single Full Photo
(2, 2, 1100.00), -- Single Bust Photo
(2, 3, 1800.00), -- Stage Photo
(2, 4, 2800.00), -- Family Photo
(2, 5, 2300.00), -- Couple Photo
(2, 6, 3200.00); -- Group Photo
