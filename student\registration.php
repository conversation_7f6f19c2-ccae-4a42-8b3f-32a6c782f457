<?php
require_once '../config/config.php';

if (session_status() === PHP_SESSION_NONE) {
    session_start();
}

$message = '';
$message_type = '';
$step = 1;
$existing_student = null;
$events = [];
$photo_types = [];
$delivery_methods = [];

// Get available events, photo types, and delivery methods
try {
    $pdo = get_pdo_connection();
    
    // Get active events
    $stmt = $pdo->query("SELECT * FROM events WHERE is_active = 1 ORDER BY event_date DESC");
    $events = $stmt->fetchAll(PDO::FETCH_ASSOC);
    
    // Get active photo types
    $stmt = $pdo->query("SELECT * FROM photo_types WHERE is_active = 1 ORDER BY type_name");
    $photo_types = $stmt->fetchAll(PDO::FETCH_ASSOC);
    
    // Get active delivery methods
    $stmt = $pdo->query("SELECT * FROM delivery_methods WHERE is_active = 1 ORDER BY additional_cost");
    $delivery_methods = $stmt->fetchAll(PDO::FETCH_ASSOC);

    // Get sessions for the selected event (if in step 2)
    $sessions = [];
    if (isset($_SESSION['registration_event_id'])) {
        $stmt = $pdo->prepare("
            SELECT * FROM event_sessions
            WHERE event_id = ? AND is_active = 1
            ORDER BY session_date, session_time
        ");
        $stmt->execute([$_SESSION['registration_event_id']]);
        $sessions = $stmt->fetchAll(PDO::FETCH_ASSOC);
    }

} catch (Exception $e) {
    $message = 'Error loading registration data. Please try again.';
    $message_type = 'is-danger';
    error_log("Registration data error: " . $e->getMessage());
}

// Handle form submissions
if ($_SERVER['REQUEST_METHOD'] === 'POST') {
    $action = $_POST['action'] ?? '';
    
    if ($action === 'check_nic') {
        $nic_number = sanitize_input($_POST['nic_number'] ?? '');
        $event_id = (int)($_POST['event_id'] ?? 0);
        
        if (empty($nic_number) || empty($event_id)) {
            $message = 'Please enter your NIC number and select an event';
            $message_type = 'is-warning';
        } else {
            try {
                // Check if student already registered for this event
                $stmt = $pdo->prepare("
                    SELECT s.*, e.event_name, es.session_name, es.session_date, es.session_time, es.venue
                    FROM students s
                    JOIN events e ON s.event_id = e.id
                    LEFT JOIN event_sessions es ON s.session_id = es.id
                    WHERE s.nic_number = ? AND s.event_id = ?
                ");
                $stmt->execute([$nic_number, $event_id]);
                $existing_student = $stmt->fetch(PDO::FETCH_ASSOC);
                
                if ($existing_student) {
                    $message = 'You are already registered for this event. Registration details are shown below.';
                    $message_type = 'is-info';
                    $step = 'existing';
                } else {
                    // Store NIC and event in session for next step
                    $_SESSION['registration_nic'] = $nic_number;
                    $_SESSION['registration_event_id'] = $event_id;
                    $step = 2;
                }
            } catch (Exception $e) {
                $message = 'Error checking registration. Please try again.';
                $message_type = 'is-danger';
                error_log("NIC check error: " . $e->getMessage());
            }
        }
    }
}
?>
<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Student Registration - <?php echo APP_NAME; ?></title>
    <link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/bulma@0.9.4/css/bulma.min.css">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css">
    <style>
        .hero-section {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
        }
        .registration-card {
            border-radius: 15px;
            box-shadow: 0 10px 30px rgba(0,0,0,0.1);
        }
        .step-indicator {
            display: flex;
            justify-content: center;
            margin-bottom: 2rem;
        }
        .step {
            display: flex;
            align-items: center;
            margin: 0 1rem;
        }
        .step-number {
            width: 40px;
            height: 40px;
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
            font-weight: bold;
            margin-right: 0.5rem;
        }
        .step.active .step-number {
            background-color: #3273dc;
            color: white;
        }
        .step.completed .step-number {
            background-color: #48c78e;
            color: white;
        }
        .step.pending .step-number {
            background-color: #dbdbdb;
            color: #7a7a7a;
        }
        .photo-selection {
            border: 2px solid #dbdbdb;
            border-radius: 8px;
            padding: 1rem;
            margin-bottom: 1rem;
            transition: border-color 0.3s;
        }
        .photo-selection:hover {
            border-color: #3273dc;
        }
        .photo-selection.selected {
            border-color: #48c78e;
            background-color: #f0fff4;
        }

        .input, .select select {
            font-size: 0.9rem;
        }

        .nic-error {
            color: #ff3860;
            font-size: 0.8rem;
            margin-top: 0.25rem;
        }

        .nic-valid {
            color: #48c78e;
            font-size: 0.8rem;
            margin-top: 0.25rem;
        }

        /* Mobile responsiveness improvements */
        @media screen and (max-width: 768px) {
            .container {
                padding: 0 1rem;
            }

            .hero-body {
                padding: 2rem 1rem;
            }

            .registration-card {
                margin: 0.5rem 0;
            }

            .title.is-2 {
                font-size: 2rem;
            }

            .subtitle.is-4 {
                font-size: 1.25rem;
            }

            .columns .column {
                padding: 0.5rem;
            }

            /* Fix step indicator for mobile */
            .step-indicator {
                flex-wrap: wrap;
                justify-content: center;
                gap: 0.5rem;
            }

            .step {
                margin: 0.25rem;
                flex-direction: column;
                text-align: center;
            }

            .step span {
                font-size: 0.75rem;
                margin-top: 0.25rem;
            }

            .step-number {
                margin-right: 0;
                margin-bottom: 0.25rem;
            }
        }

        @media screen and (max-width: 480px) {
            .hero-body {
                padding: 1.5rem 0.5rem;
            }

            .title.is-2 {
                font-size: 1.75rem;
            }

            .subtitle.is-4 {
                font-size: 1.125rem;
            }

            .button.is-large {
                font-size: 1rem;
                padding: 0.75rem 1rem;
            }

            .registration-card {
                padding: 1rem;
            }

            /* More compact step indicator for small mobile */
            .step {
                margin: 0.125rem;
            }

            .step-number {
                width: 30px;
                height: 30px;
                font-size: 0.8rem;
            }

            .step span {
                font-size: 0.7rem;
            }

            /* Fix dropdown arrow alignment on small mobile */
            .select.is-large:not(.is-multiple):not(.is-loading)::after {
                border-color: #3273dc;
                right: 1rem;
                top: 50%;
            }
        }

        /* Fix dropdown arrow alignment for all screen sizes */
        .select:not(.is-multiple):not(.is-loading)::after {
            border-color: #3273dc;
            right: 1rem;
            top: 50%;
        }

        /* Custom select dropdown arrow positioning */
        .select:not(.is-multiple):not(.is-loading)::after {
            margin-top: -0.9em !important;
        }
    </style>
</head>
<body>
    <section class="hero hero-section">
        <div class="hero-body">
            <div class="container">
                <!-- Header -->
                <div class="has-text-centered mb-6">
                    <h1 class="title is-2 has-text-white">
                        <i class="fas fa-user-plus mr-3"></i>
                        Student Registration
                    </h1>
                    <p class="subtitle is-4 has-text-white">
                        Register for photography services
                    </p>
                </div>

                <!-- Messages -->
                <?php if ($message): ?>
                    <div class="columns is-centered">
                        <div class="column is-8">
                            <div class="notification <?php echo $message_type; ?>">
                                <button class="delete" onclick="this.parentElement.style.display='none'"></button>
                                <?php echo htmlspecialchars($message); ?>
                            </div>
                        </div>
                    </div>
                <?php endif; ?>

                <!-- Registration Form -->
                <div class="columns is-centered">
                    <div class="column is-8">
                        <div class="box registration-card">
                            
                            <?php if ($step === 1): ?>
                                <!-- Step 1: NIC and Event Selection -->
                                <div class="step-indicator">
                                    <div class="step active">
                                        <div class="step-number">1</div>
                                        <span>Verification</span>
                                    </div>
                                    <div class="step pending">
                                        <div class="step-number">2</div>
                                        <span>Details</span>
                                    </div>
                                    <div class="step pending">
                                        <div class="step-number">3</div>
                                        <span>Photos</span>
                                    </div>
                                    <div class="step pending">
                                        <div class="step-number">4</div>
                                        <span>Payment</span>
                                    </div>
                                </div>

                                <h2 class="title is-4 has-text-centered">Step 1: Verification</h2>
                                <p class="has-text-centered mb-5">Enter your NIC number and select the event you want to register for</p>

                                <form method="POST" action="">
                                    <input type="hidden" name="action" value="check_nic">
                                    
                                    <div class="field">
                                        <label class="label">NIC Number</label>
                                        <div class="control has-icons-left">
                                            <input class="input" type="text" name="nic_number" id="nic_number"
                                                   placeholder="Enter your NIC number (e.g., 123456789V or 200012345678)"
                                                   value="<?php echo htmlspecialchars($_POST['nic_number'] ?? ''); ?>" required>
                                            <span class="icon is-left">
                                                <i class="fas fa-id-card"></i>
                                            </span>
                                        </div>
                                        <div id="nic-validation-message"></div>
                                        <p class="help">
                                            <strong>Old NIC:</strong> 10 digits ending with V/X (e.g., 123456789V)<br>
                                            <strong>New NIC:</strong> 12 digits (e.g., 200012345678)
                                        </p>
                                    </div>

                                    <div class="field">
                                        <label class="label">Select Event</label>
                                        <div class="control">
                                            <div class="select is-large is-fullwidth">
                                                <select name="event_id" required>
                                                    <option value="">Choose an event</option>
                                                    <?php foreach ($events as $event): ?>
                                                        <option value="<?php echo $event['id']; ?>" 
                                                                <?php echo (($_POST['event_id'] ?? '') == $event['id']) ? 'selected' : ''; ?>>
                                                            <?php echo htmlspecialchars($event['event_name']); ?>
                                                            <?php if ($event['event_date']): ?>
                                                                - <?php echo date('M j, Y', strtotime($event['event_date'])); ?>
                                                            <?php endif; ?>
                                                        </option>
                                                    <?php endforeach; ?>
                                                </select>
                                            </div>
                                        </div>
                                    </div>

                                    <div class="field">
                                        <div class="control">
                                            <button class="button is-primary is-large is-fullwidth" type="submit">
                                                <i class="fas fa-search mr-2"></i>
                                                Check & Continue
                                            </button>
                                        </div>
                                    </div>
                                </form>

                            <?php elseif ($step === 'existing'): ?>
                                <!-- Existing Registration Display -->
                                <h2 class="title is-4 has-text-centered has-text-info">Existing Registration Found</h2>
                                
                                <div class="notification is-info is-light">
                                    <h3 class="title is-5">Registration Details</h3>
                                    <div class="columns">
                                        <div class="column">
                                            <p><strong>Invoice Number:</strong> <?php echo htmlspecialchars($existing_student['invoice_number']); ?></p>
                                            <p><strong>Full Name:</strong> <?php echo htmlspecialchars($existing_student['full_name']); ?></p>
                                            <p><strong>NIC Number:</strong> <?php echo htmlspecialchars($existing_student['nic_number']); ?></p>
                                        </div>
                                        <div class="column">
                                            <p><strong>Event:</strong> <?php echo htmlspecialchars($existing_student['event_name']); ?></p>
                                            <?php if ($existing_student['session_name']): ?>
                                                <p><strong>Session:</strong> <?php echo htmlspecialchars($existing_student['session_name']); ?>
                                                    <?php if ($existing_student['session_date']): ?>
                                                        <br><small class="has-text-grey">
                                                            <i class="fas fa-calendar mr-1"></i>
                                                            <?php echo date('M j, Y', strtotime($existing_student['session_date'])); ?>
                                                            <?php if ($existing_student['session_time']): ?>
                                                                at <?php echo date('g:i A', strtotime($existing_student['session_time'])); ?>
                                                            <?php endif; ?>
                                                            <?php if ($existing_student['venue']): ?>
                                                                <br><i class="fas fa-map-marker-alt mr-1"></i>
                                                                <?php echo htmlspecialchars($existing_student['venue']); ?>
                                                            <?php endif; ?>
                                                        </small>
                                                    <?php endif; ?>
                                                </p>
                                            <?php elseif ($existing_student['session']): ?>
                                                <p><strong>Session:</strong> <?php echo htmlspecialchars($existing_student['session']); ?></p>
                                            <?php endif; ?>
                                            <p><strong>Status:</strong>
                                                <span class="tag is-info"><?php echo ucfirst(str_replace('_', ' ', $existing_student['registration_status'])); ?></span>
                                            </p>
                                            <p><strong>Total Amount:</strong> LKR <?php echo number_format($existing_student['total_amount'], 2); ?></p>
                                        </div>
                                    </div>
                                </div>

                                <div class="buttons is-centered">
                                    <a href="status-check.php" class="button is-info">
                                        <i class="fas fa-eye mr-2"></i>
                                        View Full Status
                                    </a>
                                    <a href="index.php" class="button is-light is-small">
                                        <i class="fas fa-home mr-2"></i>
                                        Back to Home
                                    </a>
                                </div>

                            <?php elseif ($step === 2): ?>
                                <!-- Step 2: Personal Details -->
                                <div class="step-indicator">
                                    <div class="step completed">
                                        <div class="step-number"><i class="fas fa-check"></i></div>
                                        <span>Verification</span>
                                    </div>
                                    <div class="step active">
                                        <div class="step-number">2</div>
                                        <span>Details</span>
                                    </div>
                                    <div class="step pending">
                                        <div class="step-number">3</div>
                                        <span>Photos</span>
                                    </div>
                                    <div class="step pending">
                                        <div class="step-number">4</div>
                                        <span>Payment</span>
                                    </div>
                                </div>

                                <h2 class="title is-4 has-text-centered">Step 2: Personal Details</h2>
                                <p class="has-text-centered mb-5">Please provide your personal information</p>

                                <form method="POST" action="registration-step2.php">
                                    <div class="columns">
                                        <div class="column is-6">
                                            <div class="field">
                                                <label class="label">Full Name *</label>
                                                <div class="control">
                                                    <input class="input" type="text" name="full_name" required>
                                                </div>
                                            </div>

                                            <div class="field">
                                                <label class="label">Email Address</label>
                                                <div class="control">
                                                    <input class="input" type="email" name="email">
                                                </div>
                                            </div>

                                            <div class="field">
                                                <label class="label">Phone Number</label>
                                                <div class="control">
                                                    <input class="input" type="tel" name="phone_number">
                                                </div>
                                            </div>
                                        </div>
                                        <div class="column is-6">
                                            <div class="field">
                                                <label class="label">WhatsApp Number</label>
                                                <div class="control">
                                                    <input class="input" type="tel" name="whatsapp_number">
                                                </div>
                                            </div>

                                            <div class="field">
                                                <label class="label">Session/Class *</label>
                                                <div class="control has-icons-left">
                                                    <div class="select is-fullwidth">
                                                        <select name="session" required>
                                                            <option value="">Select a session</option>
                                                            <?php foreach ($sessions as $session): ?>
                                                                <option value="<?php echo htmlspecialchars($session['id']); ?>">
                                                                    <?php echo htmlspecialchars($session['session_name']); ?>
                                                                    <?php if ($session['session_date']): ?>
                                                                        - <?php echo date('M j, Y', strtotime($session['session_date'])); ?>
                                                                    <?php endif; ?>
                                                                    <?php if ($session['session_time']): ?>
                                                                        at <?php echo date('g:i A', strtotime($session['session_time'])); ?>
                                                                    <?php endif; ?>
                                                                    <?php if ($session['venue']): ?>
                                                                        (<?php echo htmlspecialchars($session['venue']); ?>)
                                                                    <?php endif; ?>
                                                                </option>
                                                            <?php endforeach; ?>
                                                        </select>
                                                    </div>
                                                    <span class="icon is-left">
                                                        <i class="fas fa-calendar-alt"></i>
                                                    </span>
                                                </div>
                                                <?php if (empty($sessions)): ?>
                                                    <p class="help is-warning">
                                                        <i class="fas fa-exclamation-triangle mr-1"></i>
                                                        No sessions available for this event. Please contact support.
                                                    </p>
                                                <?php else: ?>
                                                    <p class="help">
                                                        Select the session you will attend for this event
                                                    </p>
                                                <?php endif; ?>
                                            </div>

                                            <div class="field">
                                                <label class="label">Seat Number</label>
                                                <div class="control">
                                                    <input class="input" type="text" name="seat_number">
                                                </div>
                                            </div>
                                        </div>
                                    </div>

                                    <div class="field">
                                        <label class="label">Address</label>
                                        <div class="control">
                                            <textarea class="textarea" name="address" rows="3"></textarea>
                                        </div>
                                    </div>

                                    <div class="buttons is-centered">
                                        <a href="registration.php" class="button is-light">
                                            <i class="fas fa-arrow-left mr-2"></i>
                                            Back
                                        </a>
                                        <button class="button is-primary" type="submit">
                                            <i class="fas fa-arrow-right mr-2"></i>
                                            Continue to Photo Selection
                                        </button>
                                    </div>
                                </form>
                            <?php endif; ?>
                        </div>
                    </div>
                </div>

                <!-- Back to Home -->
                <div class="has-text-centered mt-6">
                    <a href="index.php" class="button is-light">
                        <i class="fas fa-home mr-2"></i>
                        Back to Home
                    </a>
                </div>
            </div>
        </div>
    </section>

    <script>
        // NIC Validation
        function validateNIC(nic) {
            // Remove spaces and convert to uppercase
            nic = nic.replace(/\s/g, '').toUpperCase();

            // Old NIC format: 10 digits ending with V or X
            const oldNICPattern = /^[1-9]\d{8}[VX]$/;

            // New NIC format: 12 digits, cannot start with 0
            const newNICPattern = /^[1-9]\d{11}$/;

            return oldNICPattern.test(nic) || newNICPattern.test(nic);
        }

        function showNICValidation(isValid, message) {
            const messageDiv = document.getElementById('nic-validation-message');
            const input = document.getElementById('nic_number');

            if (isValid) {
                messageDiv.innerHTML = '<div class="nic-valid"><i class="fas fa-check-circle mr-1"></i>' + message + '</div>';
                input.classList.remove('is-danger');
                input.classList.add('is-success');
            } else {
                messageDiv.innerHTML = '<div class="nic-error"><i class="fas fa-exclamation-circle mr-1"></i>' + message + '</div>';
                input.classList.remove('is-success');
                input.classList.add('is-danger');
            }
        }

        // Real-time NIC validation
        document.addEventListener('DOMContentLoaded', function() {
            const nicInput = document.getElementById('nic_number');

            if (nicInput) {
                nicInput.addEventListener('input', function() {
                    const nic = this.value.trim();

                    if (nic === '') {
                        document.getElementById('nic-validation-message').innerHTML = '';
                        this.classList.remove('is-success', 'is-danger');
                        return;
                    }

                    if (validateNIC(nic)) {
                        showNICValidation(true, 'Valid NIC number');
                    } else {
                        if (nic.length < 10) {
                            showNICValidation(false, 'NIC number is too short');
                        } else if (nic.length === 10) {
                            if (!/^[1-9]/.test(nic)) {
                                showNICValidation(false, 'NIC cannot start with 0');
                            } else if (!/[VX]$/i.test(nic)) {
                                showNICValidation(false, '10-digit NIC must end with V or X');
                            } else if (!/^\d{9}[VX]$/i.test(nic)) {
                                showNICValidation(false, 'Invalid 10-digit NIC format');
                            }
                        } else if (nic.length === 12) {
                            if (!/^[1-9]/.test(nic)) {
                                showNICValidation(false, 'NIC cannot start with 0');
                            } else if (!/^\d{12}$/.test(nic)) {
                                showNICValidation(false, '12-digit NIC must contain only numbers');
                            }
                        } else {
                            showNICValidation(false, 'NIC must be 10 or 12 characters long');
                        }
                    }
                });

                // Validate on form submit
                const form = nicInput.closest('form');
                if (form) {
                    form.addEventListener('submit', function(e) {
                        const nic = nicInput.value.trim();
                        if (nic && !validateNIC(nic)) {
                            e.preventDefault();
                            showNICValidation(false, 'Please enter a valid NIC number');
                            nicInput.focus();
                        }
                    });
                }
            }
        });
    </script>
</body>
</html>
