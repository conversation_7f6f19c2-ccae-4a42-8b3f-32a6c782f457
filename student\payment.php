<?php
require_once '../config/config.php';

if (session_status() === PHP_SESSION_NONE) {
    session_start();
}

$message = '';
$message_type = '';
$student_data = null;
$invoice_number = '';

// Check if coming from registration, NIC lookup, or status check
$from_registration = false;
if (isset($_SESSION['payment_invoice'])) {
    $invoice_number = $_SESSION['payment_invoice'];
    // Check if this is a fresh registration (no previous payment attempts)
    if (!isset($_SESSION['payment_attempt_made'])) {
        $from_registration = true;
        $_SESSION['payment_attempt_made'] = true;
    }
} elseif (isset($_GET['nic']) && !empty($_GET['nic'])) {
    // Handle NIC from status check page
    $nic_number = sanitize_input($_GET['nic']);

    try {
        $pdo = get_pdo_connection();
        $stmt = $pdo->prepare("
            SELECT s.*, e.event_name, dm.method_name as delivery_method
            FROM students s
            LEFT JOIN events e ON s.event_id = e.id
            LEFT JOIN delivery_methods dm ON s.delivery_method_id = dm.id
            WHERE s.nic_number = ? AND s.payment_status IN ('pending', 'pay_at_event', 'pending_verification')
            ORDER BY s.created_at DESC
            LIMIT 1
        ");
        $stmt->execute([$nic_number]);
        $student_data = $stmt->fetch(PDO::FETCH_ASSOC);

        if ($student_data) {
            $invoice_number = $student_data['invoice_number'];
            $_SESSION['payment_invoice'] = $invoice_number;
        } else {
            $message = 'No pending payment found for this NIC number.';
            $message_type = 'is-warning';
        }
    } catch (Exception $e) {
        $message = 'Error looking up booking. Please try again.';
        $message_type = 'is-danger';
        error_log("Payment lookup error: " . $e->getMessage());
    }
} elseif (isset($_POST['nic_number']) && !empty($_POST['nic_number'])) {
    // Handle NIC lookup for existing bookings
    $nic_number = sanitize_input($_POST['nic_number']);

    try {
        $pdo = get_pdo_connection();
        $stmt = $pdo->prepare("
            SELECT s.*, e.event_name, dm.method_name as delivery_method
            FROM students s
            LEFT JOIN events e ON s.event_id = e.id
            LEFT JOIN delivery_methods dm ON s.delivery_method_id = dm.id
            WHERE s.nic_number = ? AND s.payment_status IN ('pending', 'pay_at_event', 'pending_verification')
            ORDER BY s.created_at DESC
            LIMIT 1
        ");
        $stmt->execute([$nic_number]);
        $student_data = $stmt->fetch(PDO::FETCH_ASSOC);

        if ($student_data) {
            $invoice_number = $student_data['invoice_number'];
            $_SESSION['payment_invoice'] = $invoice_number;
        } else {
            $message = 'No pending payment found for this NIC number.';
            $message_type = 'is-warning';
        }
    } catch (Exception $e) {
        $message = 'Error looking up booking. Please try again.';
        $message_type = 'is-danger';
        error_log("Payment lookup error: " . $e->getMessage());
    }
}

// Load student data if we have an invoice number
if ($invoice_number && !$student_data) {
    try {
        $pdo = get_pdo_connection();
        $stmt = $pdo->prepare("
            SELECT s.*, e.event_name, dm.method_name as delivery_method
            FROM students s
            LEFT JOIN events e ON s.event_id = e.id
            LEFT JOIN delivery_methods dm ON s.delivery_method_id = dm.id
            WHERE s.invoice_number = ? AND s.payment_status != 'paid'
        ");
        $stmt->execute([$invoice_number]);
        $student_data = $stmt->fetch(PDO::FETCH_ASSOC);
        
        if (!$student_data) {
            $message = 'Booking not found.';
            $message_type = 'is-danger';
        }
    } catch (Exception $e) {
        $message = 'Error loading booking details.';
        $message_type = 'is-danger';
        error_log("Payment data loading error: " . $e->getMessage());
    }
}

// Handle payment method selection
if ($_SERVER['REQUEST_METHOD'] === 'POST' && isset($_POST['action']) && $_POST['action'] === 'select_payment') {
    $payment_method = sanitize_input($_POST['payment_method'] ?? '');
    
    if ($payment_method && $student_data) {
        $_SESSION['selected_payment_method'] = $payment_method;
        $_SESSION['payment_student_data'] = $student_data;
        
        // Redirect based on payment method
        switch ($payment_method) {
            case 'card':
                redirect('payment-card.php');
                break;
            case 'bank_transfer':
                redirect('payment-bank.php');
                break;
            case 'pay_at_event':
                redirect('payment-event.php');
                break;
            default:
                $message = 'Please select a valid payment method.';
                $message_type = 'is-warning';
        }
    } else {
        $message = 'Please select a payment method.';
        $message_type = 'is-warning';
    }
}
?>
<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Payment - <?php echo APP_NAME; ?></title>
    <link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/bulma@0.9.4/css/bulma.min.css">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css">
    <style>
        .hero-section {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
        }
        .payment-card {
            background: white;
            border-radius: 15px;
            box-shadow: 0 10px 30px rgba(0,0,0,0.1);
            overflow: hidden;
        }
        .payment-option {
            border: 2px solid #dbdbdb;
            border-radius: 12px;
            padding: 1.5rem;
            margin-bottom: 1rem;
            transition: all 0.3s;
            cursor: pointer;
            position: relative;
        }
        .payment-option:hover {
            border-color: #3273dc;
            transform: translateY(-2px);
            box-shadow: 0 8px 25px rgba(0,0,0,0.1);
        }
        .payment-option.selected {
            border-color: #48c78e;
            background-color: #f0fff4;
        }
        .payment-option input[type="radio"] {
            position: absolute;
            opacity: 0;
        }
        .payment-badge {
            position: absolute;
            top: 1rem;
            right: 1rem;
            font-size: 0.75rem;
            padding: 0.25rem 0.5rem;
        }
        .payment-icon {
            font-size: 2rem;
            margin-bottom: 1rem;
        }
        .input, .select select, .textarea {
            font-size: 0.9rem;
        }
    </style>
</head>
<body>
    <section class="hero hero-section">
        <div class="hero-body">
            <div class="container">
                <!-- Header -->
                <div class="has-text-centered mb-6">
                    <h1 class="title is-2 has-text-white">
                        <i class="fas fa-credit-card mr-3"></i>
                        Payment
                    </h1>
                    <p class="subtitle is-4 has-text-white">
                        Complete your payment to confirm your booking
                    </p>
                </div>

                <!-- Messages -->
                <?php if ($from_registration): ?>
                    <div class="columns is-centered">
                        <div class="column is-8">
                            <div class="notification is-success">
                                <button class="delete" onclick="this.parentElement.style.display='none'"></button>
                                <h4 class="title is-5 has-text-success">
                                    <i class="fas fa-check-circle mr-2"></i>
                                    Registration Completed Successfully!
                                </h4>
                                <p>Your registration has been saved. Please complete your payment to confirm your booking.</p>
                            </div>
                        </div>
                    </div>
                <?php endif; ?>

                <?php if ($message): ?>
                    <div class="columns is-centered">
                        <div class="column is-8">
                            <div class="notification <?php echo $message_type; ?>">
                                <button class="delete" onclick="this.parentElement.style.display='none'"></button>
                                <?php echo htmlspecialchars($message); ?>
                            </div>
                        </div>
                    </div>
                <?php endif; ?>

                <!-- NIC Lookup Form (if no invoice) -->
                <?php if (!$invoice_number): ?>
                    <div class="columns is-centered">
                        <div class="column is-6">
                            <div class="box payment-card">
                                <h2 class="title is-4 has-text-centered">
                                    <i class="fas fa-search mr-2"></i>
                                    Find Your Booking
                                </h2>
                                
                                <form method="POST" action="">
                                    <div class="field">
                                        <label class="label">NIC Number</label>
                                        <div class="control has-icons-left">
                                            <input class="input" type="text" name="nic_number" 
                                                   placeholder="Enter your NIC number" required>
                                            <span class="icon is-left">
                                                <i class="fas fa-id-card"></i>
                                            </span>
                                        </div>
                                        <p class="help">Enter your NIC number to find your pending payment</p>
                                    </div>
                                    
                                    <div class="field">
                                        <div class="control">
                                            <button class="button is-primary is-fullwidth" type="submit">
                                                <i class="fas fa-search mr-2"></i>
                                                Find Booking
                                            </button>
                                        </div>
                                    </div>
                                </form>
                            </div>
                        </div>
                    </div>
                <?php endif; ?>

                <!-- Payment Options (if student data loaded) -->
                <?php if ($student_data): ?>
                    <div class="columns is-centered">
                        <div class="column is-10">
                            <div class="box payment-card">
                                
                                <!-- Booking Summary -->
                                <div class="columns">
                                    <div class="column is-4">
                                        <h2 class="title is-5">
                                            <i class="fas fa-receipt mr-2"></i>
                                            Booking Summary
                                        </h2>
                                        <div class="content">
                                            <p><strong>Invoice:</strong> <?php echo htmlspecialchars($student_data['invoice_number']); ?></p>
                                            <p><strong>Name:</strong> <?php echo htmlspecialchars($student_data['full_name']); ?></p>
                                            <p><strong>Event:</strong> <?php echo htmlspecialchars($student_data['event_name']); ?></p>
                                            <p><strong>Amount:</strong> <span class="title is-4 has-text-primary">LKR <?php echo number_format($student_data['total_amount'], 2); ?></span></p>
                                            <p><strong>Current Status:</strong>
                                                <?php if ($student_data['payment_status'] === 'pending'): ?>
                                                    <span class="tag is-warning">Payment Pending</span>
                                                <?php elseif ($student_data['payment_status'] === 'pay_at_event'): ?>
                                                    <span class="tag is-info">Pay at Event</span>
                                                <?php elseif ($student_data['payment_status'] === 'pending_verification'): ?>
                                                    <span class="tag is-warning">Pending Verification</span>
                                                <?php endif; ?>
                                            </p>
                                        </div>

                                        <!-- Status-specific messages -->
                                        <?php if ($student_data['payment_status'] === 'pay_at_event'): ?>
                                            <div class="notification is-info is-light">
                                                <h4 class="title is-6">
                                                    <i class="fas fa-info-circle mr-2"></i>
                                                    Pay at Event Selected
                                                </h4>
                                                <p class="is-size-7">
                                                    You have chosen to pay at the event. You can change your payment method below if needed.
                                                </p>
                                            </div>
                                        <?php elseif ($student_data['payment_status'] === 'pending_verification'): ?>
                                            <div class="notification is-warning is-light">
                                                <h4 class="title is-6">
                                                    <i class="fas fa-clock mr-2"></i>
                                                    Bank Transfer Under Review
                                                </h4>
                                                <p class="is-size-7">
                                                    Your bank transfer is being verified. You can submit a new payment method if needed.
                                                </p>
                                            </div>
                                        <?php endif; ?>
                                    </div>
                                    
                                    <div class="column is-8">
                                        <h2 class="title is-5">
                                            <i class="fas fa-credit-card mr-2"></i>
                                            Select Payment Method
                                        </h2>
                                        
                                        <form method="POST" action="">
                                            <input type="hidden" name="action" value="select_payment">
                                            
                                            <!-- Credit/Debit Card -->
                                            <div class="payment-option" onclick="selectPayment('card', this)">
                                                <input type="radio" name="payment_method" value="card" id="payment_card">
                                                <span class="payment-badge tag is-success">Fastest & Recommended</span>
                                                <div class="has-text-centered">
                                                    <div class="payment-icon has-text-primary">
                                                        <i class="fas fa-credit-card"></i>
                                                    </div>
                                                    <h3 class="title is-5">Credit/Debit Card</h3>
                                                    <p class="subtitle is-6 has-text-grey">
                                                        Pay instantly with your credit or debit card<br>
                                                        <strong>Secure & Fast Processing</strong>
                                                    </p>
                                                </div>
                                            </div>
                                            
                                            <!-- Bank Transfer -->
                                            <div class="payment-option" onclick="selectPayment('bank_transfer', this)">
                                                <input type="radio" name="payment_method" value="bank_transfer" id="payment_bank">
                                                <div class="has-text-centered">
                                                    <div class="payment-icon has-text-info">
                                                        <i class="fas fa-university"></i>
                                                    </div>
                                                    <h3 class="title is-5">Bank Transfer</h3>
                                                    <p class="subtitle is-6 has-text-grey">
                                                        Transfer to our bank account and upload receipt<br>
                                                        <strong>Processing: 1-2 business days</strong>
                                                    </p>
                                                </div>
                                            </div>
                                            
                                            <!-- Pay at Event -->
                                            <div class="payment-option" onclick="selectPayment('pay_at_event', this)">
                                                <input type="radio" name="payment_method" value="pay_at_event" id="payment_event">
                                                <span class="payment-badge tag is-warning">Slowest</span>
                                                <div class="has-text-centered">
                                                    <div class="payment-icon has-text-warning">
                                                        <i class="fas fa-calendar-check"></i>
                                                    </div>
                                                    <h3 class="title is-5">Pay at Event</h3>
                                                    <p class="subtitle is-6 has-text-grey">
                                                        Pay cash at the event venue<br>
                                                        <strong>Subject to availability</strong>
                                                    </p>
                                                </div>
                                            </div>
                                            
                                            <div class="field mt-5">
                                                <div class="control">
                                                    <button class="button is-primary is-large is-fullwidth" type="submit">
                                                        <i class="fas fa-arrow-right mr-2"></i>
                                                        Continue with Selected Method
                                                    </button>
                                                </div>
                                            </div>
                                        </form>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                <?php endif; ?>
            </div>
        </div>
    </section>

    <script>
        function selectPayment(method, element) {
            // Remove selected class from all options
            document.querySelectorAll('.payment-option').forEach(option => {
                option.classList.remove('selected');
            });
            
            // Add selected class to clicked option
            element.classList.add('selected');
            
            // Check the radio button
            document.getElementById('payment_' + method).checked = true;
        }
        
        // Auto-hide notifications
        document.addEventListener('DOMContentLoaded', function() {
            const notifications = document.querySelectorAll('.notification');
            notifications.forEach(function(notification) {
                setTimeout(function() {
                    if (notification.parentElement) {
                        notification.style.display = 'none';
                    }
                }, 5000);
            });
        });
    </script>
</body>
</html>
