<?php
require_once 'config/simple-config.php';

if (!is_logged_in()) {
    redirect(BASE_URL . 'simple-login.php');
}
?>
<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Test Dashboard - <?php echo APP_NAME; ?></title>
    <link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/bulma@0.9.4/css/bulma.min.css">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css">
    <style>
        .dashboard-header {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
        }
    </style>
</head>
<body>
    <!-- Navigation -->
    <nav class="navbar dashboard-header" role="navigation">
        <div class="navbar-brand">
            <div class="navbar-item">
                <i class="fas fa-camera mr-2"></i>
                <strong>Test Dashboard</strong>
            </div>
        </div>
        
        <div class="navbar-menu">
            <div class="navbar-end">
                <div class="navbar-item">
                    <span class="has-text-white">
                        <i class="fas fa-user mr-2"></i>
                        <?php echo htmlspecialchars($_SESSION['full_name']); ?>
                    </span>
                </div>
                <div class="navbar-item">
                    <a href="logout.php" class="button is-light is-small">
                        <i class="fas fa-sign-out-alt mr-2"></i>
                        Logout
                    </a>
                </div>
            </div>
        </div>
    </nav>

    <div class="container mt-4">
        <div class="box">
            <h1 class="title is-4">
                <i class="fas fa-check-circle mr-2 has-text-success"></i>
                Login Test Successful!
            </h1>
            <p class="subtitle is-6">The basic authentication system is working</p>
            
            <div class="notification is-success is-light">
                <h2 class="title is-5">✅ What's Working:</h2>
                <ul>
                    <li>✅ PHP is running correctly</li>
                    <li>✅ Sessions are working</li>
                    <li>✅ Basic authentication works</li>
                    <li>✅ Bulma CSS is loading</li>
                    <li>✅ Font Awesome icons are loading</li>
                </ul>
            </div>
            
            <div class="notification is-warning is-light">
                <h2 class="title is-5">⚠️ Next Steps:</h2>
                <ol>
                    <li><strong>Create Databases:</strong> Go to <a href="http://localhost/phpmyadmin" target="_blank">phpMyAdmin</a> and run the SQL files</li>
                    <li><strong>Test Database Connection:</strong> Go back to <a href="minimal-test.php">minimal-test.php</a></li>
                    <li><strong>Once databases are ready:</strong> Try the full <a href="login.php">login.php</a></li>
                </ol>
            </div>
            
            <div class="buttons">
                <a href="minimal-test.php" class="button is-info">
                    <i class="fas fa-flask mr-2"></i>
                    System Test
                </a>
                <a href="http://localhost/phpmyadmin" target="_blank" class="button is-primary">
                    <i class="fas fa-database mr-2"></i>
                    phpMyAdmin
                </a>
                <a href="logout.php" class="button is-light">
                    <i class="fas fa-sign-out-alt mr-2"></i>
                    Logout
                </a>
            </div>
        </div>
    </div>
</body>
</html>
