<?php
require_once '../config/config.php';

if (session_status() === PHP_SESSION_NONE) {
    session_start();
}

// Check if payment method is selected and student data exists
if (!isset($_SESSION['selected_payment_method']) || $_SESSION['selected_payment_method'] !== 'card' || 
    !isset($_SESSION['payment_student_data'])) {
    redirect('payment.php');
}

$student_data = $_SESSION['payment_student_data'];
$message = '';
$message_type = '';

// Handle payment processing
if ($_SERVER['REQUEST_METHOD'] === 'POST' && isset($_POST['action']) && $_POST['action'] === 'process_payment') {
    // In a real implementation, you would integrate with a payment gateway like Stripe, PayPal, etc.
    // For demo purposes, we'll simulate successful payment
    
    $card_number = sanitize_input($_POST['card_number'] ?? '');
    $expiry_month = sanitize_input($_POST['expiry_month'] ?? '');
    $expiry_year = sanitize_input($_POST['expiry_year'] ?? '');
    $cvv = sanitize_input($_POST['cvv'] ?? '');
    $cardholder_name = sanitize_input($_POST['cardholder_name'] ?? '');
    
    // Basic validation
    if (empty($card_number) || empty($expiry_month) || empty($expiry_year) || empty($cvv) || empty($cardholder_name)) {
        $message = 'Please fill in all card details.';
        $message_type = 'is-danger';
    } else {
        try {
            $pdo = get_pdo_connection();
            
            // Update payment status
            $stmt = $pdo->prepare("
                UPDATE students 
                SET payment_status = 'paid', 
                    payment_method = 'Credit/Debit Card',
                    payment_date = NOW(),
                    updated_at = NOW()
                WHERE invoice_number = ?
            ");
            $stmt->execute([$student_data['invoice_number']]);
            
            // Log payment activity
            log_activity('PAYMENT_COMPLETED', "Card payment completed for invoice: {$student_data['invoice_number']}");
            
            // Clear payment session data
            unset($_SESSION['selected_payment_method']);
            unset($_SESSION['payment_student_data']);
            unset($_SESSION['payment_invoice']);
            unset($_SESSION['payment_attempt_made']);
            
            // Store success data
            $_SESSION['payment_success'] = [
                'invoice_number' => $student_data['invoice_number'],
                'amount' => $student_data['total_amount'],
                'method' => 'Credit/Debit Card'
            ];
            
            redirect('payment-invoice.php');
            
        } catch (Exception $e) {
            $message = 'Payment processing failed. Please try again.';
            $message_type = 'is-danger';
            error_log("Card payment error: " . $e->getMessage());
        }
    }
}
?>
<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Card Payment - <?php echo APP_NAME; ?></title>
    <link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/bulma@0.9.4/css/bulma.min.css">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css">
    <style>
        .hero-section {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
        }
        .payment-card {
            background: white;
            border-radius: 15px;
            box-shadow: 0 10px 30px rgba(0,0,0,0.1);
            overflow: hidden;
        }
        .card-form {
            background: #f8f9fa;
            padding: 2rem;
            border-radius: 12px;
            border: 1px solid #e9ecef;
        }
        .card-preview {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            border-radius: 12px;
            padding: 1.5rem;
            color: white;
            margin-bottom: 2rem;
            position: relative;
            min-height: 200px;
        }
        .card-number {
            font-family: 'Courier New', monospace;
            font-size: 1.25rem;
            letter-spacing: 2px;
            margin: 2rem 0 1rem 0;
        }
        .card-details {
            display: flex;
            justify-content: space-between;
            align-items: flex-end;
        }
        .card-logo {
            position: absolute;
            top: 1rem;
            right: 1rem;
            font-size: 2rem;
        }
        .input, .select select {
            font-size: 0.9rem;
        }

        /* Custom select dropdown arrow positioning */
        .select:not(.is-multiple):not(.is-loading)::after {
            margin-top: -0.9em !important;
            transform: none !important;
        }
        .step-indicator {
            display: flex;
            justify-content: space-between;
            margin-bottom: 2rem;
            padding: 0 1rem;
        }
        .step {
            flex: 1;
            text-align: center;
            padding: 0.75rem 0.5rem;
            border-radius: 8px;
            margin: 0 0.25rem;
        }
        .step.completed {
            background: #48c78e;
            color: white;
        }
        .step.active {
            background: #3273dc;
            color: white;
        }
        .step.pending {
            background: #f5f5f5;
            color: #7a7a7a;
        }
        .step-number {
            font-weight: bold;
            font-size: 0.9rem;
        }
        .step-label {
            font-size: 0.75rem;
            margin-top: 0.25rem;
        }
        .security-info {
            background: #e8f4fd;
            border: 1px solid #bee5eb;
            border-radius: 8px;
            padding: 1rem;
            margin-top: 1rem;
        }
    </style>
</head>
<body>
    <section class="hero hero-section">
        <div class="hero-body">
            <div class="container">
                <!-- Header -->
                <div class="has-text-centered mb-6">
                    <h1 class="title is-2 has-text-white">
                        <i class="fas fa-credit-card mr-3"></i>
                        Card Payment
                    </h1>
                    <p class="subtitle is-4 has-text-white">
                        Secure payment with your credit or debit card
                    </p>
                </div>

                <!-- Messages -->
                <?php if ($message): ?>
                    <div class="columns is-centered">
                        <div class="column is-8">
                            <div class="notification <?php echo $message_type; ?>">
                                <button class="delete" onclick="this.parentElement.style.display='none'"></button>
                                <?php echo htmlspecialchars($message); ?>
                            </div>
                        </div>
                    </div>
                <?php endif; ?>

                <!-- Payment Form -->
                <div class="columns is-centered">
                    <div class="column is-10">
                        <div class="box payment-card">

                            <!-- Step Indicator -->
                            <div class="step-indicator">
                                <div class="step completed">
                                    <div class="step-number">1</div>
                                    <div class="step-label">Verification</div>
                                </div>
                                <div class="step completed">
                                    <div class="step-number">2</div>
                                    <div class="step-label">Details</div>
                                </div>
                                <div class="step completed">
                                    <div class="step-number">3</div>
                                    <div class="step-label">Photos</div>
                                </div>
                                <div class="step completed">
                                    <div class="step-number">4</div>
                                    <div class="step-label">Review</div>
                                </div>
                                <div class="step active">
                                    <div class="step-number">5</div>
                                    <div class="step-label">Payment</div>
                                </div>
                                <div class="step pending">
                                    <div class="step-number">6</div>
                                    <div class="step-label">Invoice</div>
                                </div>
                            </div>

                            <div class="columns">
                                <!-- Order Summary -->
                                <div class="column is-4">
                                    <h2 class="title is-5">
                                        <i class="fas fa-receipt mr-2"></i>
                                        Order Summary
                                    </h2>
                                    <div class="content">
                                        <p><strong>Invoice:</strong> <?php echo htmlspecialchars($student_data['invoice_number']); ?></p>
                                        <p><strong>Name:</strong> <?php echo htmlspecialchars($student_data['full_name']); ?></p>
                                        <p><strong>Event:</strong> <?php echo htmlspecialchars($student_data['event_name']); ?></p>
                                        <hr>
                                        <p><strong>Total Amount:</strong></p>
                                        <p class="title is-3 has-text-primary">LKR <?php echo number_format($student_data['total_amount'], 2); ?></p>
                                    </div>
                                    
                                    <div class="security-info">
                                        <h4 class="title is-6 has-text-info">
                                            <i class="fas fa-shield-alt mr-2"></i>
                                            Secure Payment
                                        </h4>
                                        <p class="is-size-7">
                                            Your payment information is encrypted and secure. We never store your card details.
                                        </p>
                                    </div>
                                </div>
                                
                                <!-- Card Form -->
                                <div class="column is-8">
                                    <div class="card-form">
                                        <!-- Card Preview -->
                                        <div class="card-preview">
                                            <div class="card-logo">
                                                <i class="fab fa-cc-visa"></i>
                                            </div>
                                            <div class="card-number" id="cardPreview">
                                                •••• •••• •••• ••••
                                            </div>
                                            <div class="card-details">
                                                <div>
                                                    <p class="is-size-7 has-text-grey-light">CARDHOLDER NAME</p>
                                                    <p id="namePreview">YOUR NAME</p>
                                                </div>
                                                <div>
                                                    <p class="is-size-7 has-text-grey-light">EXPIRES</p>
                                                    <p id="expiryPreview">MM/YY</p>
                                                </div>
                                            </div>
                                        </div>
                                        
                                        <!-- Payment Form -->
                                        <form method="POST" action="">
                                            <input type="hidden" name="action" value="process_payment">
                                            
                                            <div class="field">
                                                <label class="label">Card Number</label>
                                                <div class="control has-icons-left">
                                                    <input class="input" type="text" name="card_number" id="cardNumber"
                                                           placeholder="1234 5678 9012 3456" maxlength="19" required>
                                                    <span class="icon is-left">
                                                        <i class="fas fa-credit-card"></i>
                                                    </span>
                                                </div>
                                            </div>
                                            
                                            <div class="field">
                                                <label class="label">Cardholder Name</label>
                                                <div class="control has-icons-left">
                                                    <input class="input" type="text" name="cardholder_name" id="cardholderName"
                                                           placeholder="John Doe" required>
                                                    <span class="icon is-left">
                                                        <i class="fas fa-user"></i>
                                                    </span>
                                                </div>
                                            </div>
                                            
                                            <div class="columns">
                                                <div class="column is-8">
                                                    <label class="label">Expiry Date</label>
                                                    <div class="field has-addons">
                                                        <div class="control">
                                                            <div class="select">
                                                                <select name="expiry_month" id="expiryMonth" required>
                                                                    <option value="">Month</option>
                                                                    <?php for ($i = 1; $i <= 12; $i++): ?>
                                                                        <option value="<?php echo sprintf('%02d', $i); ?>">
                                                                            <?php echo sprintf('%02d', $i); ?>
                                                                        </option>
                                                                    <?php endfor; ?>
                                                                </select>
                                                            </div>
                                                        </div>
                                                        <div class="control">
                                                            <div class="select">
                                                                <select name="expiry_year" id="expiryYear" required>
                                                                    <option value="">Year</option>
                                                                    <?php for ($i = date('Y'); $i <= date('Y') + 10; $i++): ?>
                                                                        <option value="<?php echo substr($i, -2); ?>">
                                                                            <?php echo $i; ?>
                                                                        </option>
                                                                    <?php endfor; ?>
                                                                </select>
                                                            </div>
                                                        </div>
                                                    </div>
                                                </div>
                                                <div class="column is-4">
                                                    <div class="field">
                                                        <label class="label">CVV</label>
                                                        <div class="control has-icons-left">
                                                            <input class="input" type="text" name="cvv" 
                                                                   placeholder="123" maxlength="4" required>
                                                            <span class="icon is-left">
                                                                <i class="fas fa-lock"></i>
                                                            </span>
                                                        </div>
                                                    </div>
                                                </div>
                                            </div>
                                            
                                            <div class="field mt-5">
                                                <div class="control">
                                                    <button class="button is-success is-large is-fullwidth" type="submit">
                                                        <i class="fas fa-lock mr-2"></i>
                                                        Pay LKR <?php echo number_format($student_data['total_amount'], 2); ?>
                                                    </button>
                                                </div>
                                            </div>
                                            
                                            <div class="field">
                                                <div class="control">
                                                    <a href="payment.php" class="button is-light is-fullwidth">
                                                        <i class="fas fa-arrow-left mr-2"></i>
                                                        Back to Payment Options
                                                    </a>
                                                </div>
                                            </div>
                                        </form>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </section>

    <script>
        // Card number formatting and preview
        document.getElementById('cardNumber').addEventListener('input', function(e) {
            let value = e.target.value.replace(/\s/g, '').replace(/[^0-9]/gi, '');
            let formattedValue = value.match(/.{1,4}/g)?.join(' ') || value;
            
            if (formattedValue.length > 19) {
                formattedValue = formattedValue.substr(0, 19);
            }
            
            e.target.value = formattedValue;
            
            // Update preview
            const preview = document.getElementById('cardPreview');
            if (value.length > 0) {
                let maskedValue = value.replace(/./g, '•');
                let displayValue = maskedValue.match(/.{1,4}/g)?.join(' ') || maskedValue;
                preview.textContent = displayValue;
            } else {
                preview.textContent = '•••• •••• •••• ••••';
            }
        });
        
        // Cardholder name preview
        document.getElementById('cardholderName').addEventListener('input', function(e) {
            const preview = document.getElementById('namePreview');
            preview.textContent = e.target.value.toUpperCase() || 'YOUR NAME';
        });
        
        // Expiry date preview
        function updateExpiryPreview() {
            const month = document.getElementById('expiryMonth').value;
            const year = document.getElementById('expiryYear').value;
            const preview = document.getElementById('expiryPreview');
            
            if (month && year) {
                preview.textContent = month + '/' + year;
            } else {
                preview.textContent = 'MM/YY';
            }
        }
        
        document.getElementById('expiryMonth').addEventListener('change', updateExpiryPreview);
        document.getElementById('expiryYear').addEventListener('change', updateExpiryPreview);
        
        // Auto-hide notifications
        document.addEventListener('DOMContentLoaded', function() {
            const notifications = document.querySelectorAll('.notification');
            notifications.forEach(function(notification) {
                setTimeout(function() {
                    if (notification.parentElement) {
                        notification.style.display = 'none';
                    }
                }, 5000);
            });
        });
    </script>
</body>
</html>
