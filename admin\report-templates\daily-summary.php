<?php if (!isset($reportData)) exit; ?>

<!-- Summary Statistics -->
<div class="columns">
    <div class="column">
        <div class="box stat-card">
            <div class="level">
                <div class="level-left">
                    <div>
                        <p class="heading">Total Sessions</p>
                        <p class="title is-3"><?php echo number_format($reportData['summary']['total_sessions']); ?></p>
                    </div>
                </div>
                <div class="level-right">
                    <i class="fas fa-camera fa-2x has-text-info"></i>
                </div>
            </div>
        </div>
    </div>
    <div class="column">
        <div class="box stat-card">
            <div class="level">
                <div class="level-left">
                    <div>
                        <p class="heading">Active Operators</p>
                        <p class="title is-3"><?php echo number_format($reportData['summary']['operators_active']); ?></p>
                    </div>
                </div>
                <div class="level-right">
                    <i class="fas fa-users fa-2x has-text-success"></i>
                </div>
            </div>
        </div>
    </div>
    <div class="column">
        <div class="box stat-card">
            <div class="level">
                <div class="level-left">
                    <div>
                        <p class="heading">Report Date</p>
                        <p class="title is-6"><?php echo date('F j, Y', strtotime($reportData['summary']['date'])); ?></p>
                    </div>
                </div>
                <div class="level-right">
                    <i class="fas fa-calendar fa-2x has-text-primary"></i>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Hourly Distribution Chart -->
<?php if (!empty($reportData['summary']['sessions_by_hour'])): ?>
<div class="box">
    <h2 class="title is-5">
        <i class="fas fa-chart-bar mr-2"></i>
        Hourly Distribution
    </h2>
    <div class="chart-container">
        <canvas id="hourlyChart"></canvas>
    </div>
</div>

<script>
document.addEventListener('DOMContentLoaded', function() {
    const hourlyData = <?php echo json_encode($reportData['summary']['sessions_by_hour']); ?>;
    
    // Create 24-hour array
    const hours = Array.from({length: 24}, (_, i) => i);
    const sessionCounts = new Array(24).fill(0);
    
    // Fill in actual data
    Object.keys(hourlyData).forEach(hour => {
        sessionCounts[parseInt(hour)] = hourlyData[hour];
    });
    
    const ctx = document.getElementById('hourlyChart').getContext('2d');
    new Chart(ctx, {
        type: 'bar',
        data: {
            labels: hours.map(h => h + ':00'),
            datasets: [{
                label: 'Sessions',
                data: sessionCounts,
                backgroundColor: '#3273dc',
                borderColor: '#3273dc',
                borderWidth: 1
            }]
        },
        options: {
            responsive: true,
            maintainAspectRatio: false,
            scales: {
                y: {
                    beginAtZero: true,
                    ticks: {
                        stepSize: 1
                    }
                }
            },
            plugins: {
                legend: {
                    display: false
                }
            }
        }
    });
});
</script>
<?php endif; ?>

<!-- Sessions Details -->
<div class="box">
    <h2 class="title is-5">
        <i class="fas fa-list mr-2"></i>
        Session Details
    </h2>
    
    <?php if (empty($reportData['sessions'])): ?>
        <div class="has-text-centered py-6">
            <i class="fas fa-inbox fa-3x has-text-grey-light mb-4"></i>
            <p class="title is-5 has-text-grey">No sessions recorded</p>
            <p class="has-text-grey">No photo sessions were completed on this date</p>
        </div>
    <?php else: ?>
        <div class="table-container">
            <table class="table is-fullwidth is-striped">
                <thead>
                    <tr>
                        <th>Time</th>
                        <th>Invoice</th>
                        <th>Student Name</th>
                        <th>Session</th>
                        <th>Sequence #</th>
                        <th>Operator</th>
                    </tr>
                </thead>
                <tbody>
                    <?php foreach ($reportData['sessions'] as $session): ?>
                        <tr>
                            <td><strong><?php echo date('H:i', strtotime($session['completed_at'])); ?></strong></td>
                            <td><?php echo htmlspecialchars($session['invoice_number']); ?></td>
                            <td><?php echo htmlspecialchars($session['student_name']); ?></td>
                            <td><?php echo htmlspecialchars($session['session']); ?></td>
                            <td>
                                <span class="tag is-primary">
                                    <?php echo htmlspecialchars($session['photo_sequence_number']); ?>
                                </span>
                            </td>
                            <td><?php echo htmlspecialchars($session['operator_name']); ?></td>
                        </tr>
                    <?php endforeach; ?>
                </tbody>
            </table>
        </div>
    <?php endif; ?>
</div>
