<?php
require_once '../config/config.php';

if (session_status() === PHP_SESSION_NONE) {
    session_start();
}

// Check if user came from step 1
if (!isset($_SESSION['registration_nic']) || !isset($_SESSION['registration_event_id'])) {
    redirect('registration.php');
}

$message = '';
$message_type = '';

// Handle form submission
if ($_SERVER['REQUEST_METHOD'] === 'POST') {
    // Store personal details in session
    $_SESSION['registration_data'] = [
        'full_name' => sanitize_input($_POST['full_name'] ?? ''),
        'email' => sanitize_input($_POST['email'] ?? ''),
        'phone_number' => sanitize_input($_POST['phone_number'] ?? ''),
        'whatsapp_number' => sanitize_input($_POST['whatsapp_number'] ?? ''),
        'session' => sanitize_input($_POST['session'] ?? ''),
        'seat_number' => sanitize_input($_POST['seat_number'] ?? ''),
        'address' => sanitize_input($_POST['address'] ?? '')
    ];
    
    // Validate required fields
    if (empty($_SESSION['registration_data']['full_name'])) {
        $message = 'Full name is required';
        $message_type = 'is-danger';
    } else {
        redirect('registration-step3.php');
    }
}

// Get event details and sessions
try {
    $pdo = get_pdo_connection();
    $stmt = $pdo->prepare("SELECT * FROM events WHERE id = ?");
    $stmt->execute([$_SESSION['registration_event_id']]);
    $event = $stmt->fetch(PDO::FETCH_ASSOC);

    // Get event sessions
    $stmt = $pdo->prepare("
        SELECT * FROM event_sessions
        WHERE event_id = ? AND is_active = 1
        ORDER BY session_date, session_time
    ");
    $stmt->execute([$_SESSION['registration_event_id']]);
    $sessions = $stmt->fetchAll(PDO::FETCH_ASSOC);

} catch (Exception $e) {
    $message = 'Error loading event details';
    $message_type = 'is-danger';
    $event = null;
    $sessions = [];
}
?>
<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Registration Step 2 - <?php echo APP_NAME; ?></title>
    <link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/bulma@0.9.4/css/bulma.min.css">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css">
    <style>
        .hero-section {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
        }
        .registration-card {
            border-radius: 15px;
            box-shadow: 0 10px 30px rgba(0,0,0,0.1);
        }
        .step-indicator {
            display: flex;
            justify-content: center;
            margin-bottom: 2rem;
        }
        .step {
            display: flex;
            align-items: center;
            margin: 0 1rem;
        }
        .step-number {
            width: 40px;
            height: 40px;
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
            font-weight: bold;
            margin-right: 0.5rem;
        }
        .step.active .step-number {
            background-color: #3273dc;
            color: white;
        }
        .step.completed .step-number {
            background-color: #48c78e;
            color: white;
        }
        .step.pending .step-number {
            background-color: #dbdbdb;
            color: #7a7a7a;
        }

        .input, .select select, .textarea {
            font-size: 0.9rem;
        }

        /* Custom select dropdown arrow positioning */
        .select:not(.is-multiple):not(.is-loading)::after {
            margin-top: -0.9em !important;
            transform: none !important;
        }
    </style>
</head>
<body>
    <section class="hero hero-section">
        <div class="hero-body">
            <div class="container">
                <!-- Header -->
                <div class="has-text-centered mb-6">
                    <h1 class="title is-2 has-text-white">
                        <i class="fas fa-user-plus mr-3"></i>
                        Student Registration
                    </h1>
                    <p class="subtitle is-4 has-text-white">
                        Step 2: Personal Details
                    </p>
                </div>

                <!-- Messages -->
                <?php if ($message): ?>
                    <div class="columns is-centered">
                        <div class="column is-8">
                            <div class="notification <?php echo $message_type; ?>">
                                <button class="delete" onclick="this.parentElement.style.display='none'"></button>
                                <?php echo htmlspecialchars($message); ?>
                            </div>
                        </div>
                    </div>
                <?php endif; ?>

                <!-- Registration Form -->
                <div class="columns is-centered">
                    <div class="column is-8">
                        <div class="box registration-card">
                            
                            <!-- Step Indicator -->
                            <div class="step-indicator">
                                <div class="step completed">
                                    <div class="step-number"><i class="fas fa-check"></i></div>
                                    <span>Verification</span>
                                </div>
                                <div class="step active">
                                    <div class="step-number">2</div>
                                    <span>Details</span>
                                </div>
                                <div class="step pending">
                                    <div class="step-number">3</div>
                                    <span>Photos</span>
                                </div>
                                <div class="step pending">
                                    <div class="step-number">4</div>
                                    <span>Payment</span>
                                </div>
                            </div>

                            <!-- Registration Info -->
                            <div class="notification is-info is-light mb-5">
                                <div class="level">
                                    <div class="level-left">
                                        <div>
                                            <p><strong>NIC:</strong> <?php echo htmlspecialchars($_SESSION['registration_nic']); ?></p>
                                            <p><strong>Event:</strong> <?php echo htmlspecialchars($event['event_name'] ?? 'Unknown'); ?></p>
                                        </div>
                                    </div>
                                </div>
                            </div>

                            <h2 class="title is-4 has-text-centered">Personal Information</h2>
                            <p class="has-text-centered mb-5">Please provide your personal details for registration</p>

                            <form method="POST" action="">
                                <div class="columns">
                                    <div class="column is-6">
                                        <div class="field">
                                            <label class="label">Full Name *</label>
                                            <div class="control has-icons-left">
                                                <input class="input" type="text" name="full_name" 
                                                       value="<?php echo htmlspecialchars($_SESSION['registration_data']['full_name'] ?? ''); ?>" 
                                                       required>
                                                <span class="icon is-left">
                                                    <i class="fas fa-user"></i>
                                                </span>
                                            </div>
                                        </div>

                                        <div class="field">
                                            <label class="label">Email Address</label>
                                            <div class="control has-icons-left">
                                                <input class="input" type="email" name="email" 
                                                       value="<?php echo htmlspecialchars($_SESSION['registration_data']['email'] ?? ''); ?>">
                                                <span class="icon is-left">
                                                    <i class="fas fa-envelope"></i>
                                                </span>
                                            </div>
                                        </div>

                                        <div class="field">
                                            <label class="label">Phone Number</label>
                                            <div class="control has-icons-left">
                                                <input class="input" type="tel" name="phone_number" 
                                                       value="<?php echo htmlspecialchars($_SESSION['registration_data']['phone_number'] ?? ''); ?>">
                                                <span class="icon is-left">
                                                    <i class="fas fa-phone"></i>
                                                </span>
                                            </div>
                                        </div>
                                    </div>
                                    <div class="column is-6">
                                        <div class="field">
                                            <label class="label">WhatsApp Number</label>
                                            <div class="control has-icons-left">
                                                <input class="input" type="tel" name="whatsapp_number" 
                                                       value="<?php echo htmlspecialchars($_SESSION['registration_data']['whatsapp_number'] ?? ''); ?>">
                                                <span class="icon is-left">
                                                    <i class="fab fa-whatsapp"></i>
                                                </span>
                                            </div>
                                        </div>

                                        <div class="field">
                                            <label class="label">Session/Class</label>
                                            <div class="control has-icons-left">
                                                <div class="select is-fullwidth">
                                                    <select name="session" required>
                                                        <option value="">Select a session</option>
                                                        <?php foreach ($sessions as $session): ?>
                                                            <option value="<?php echo htmlspecialchars($session['id']); ?>"
                                                                    <?php echo (isset($_SESSION['registration_data']['session']) && $_SESSION['registration_data']['session'] == $session['id']) ? 'selected' : ''; ?>>
                                                                <?php echo htmlspecialchars($session['session_name']); ?>
                                                                <?php if ($session['session_date']): ?>
                                                                    - <?php echo date('M j, Y', strtotime($session['session_date'])); ?>
                                                                <?php endif; ?>
                                                                <?php if ($session['session_time']): ?>
                                                                    at <?php echo date('g:i A', strtotime($session['session_time'])); ?>
                                                                <?php endif; ?>
                                                            </option>
                                                        <?php endforeach; ?>
                                                    </select>
                                                </div>
                                                <span class="icon is-left">
                                                    <i class="fas fa-graduation-cap"></i>
                                                </span>
                                            </div>
                                            <?php if (empty($sessions)): ?>
                                                <p class="help is-warning">
                                                    <i class="fas fa-exclamation-triangle mr-1"></i>
                                                    No sessions available for this event. Please contact support.
                                                </p>
                                            <?php endif; ?>
                                        </div>

                                        <div class="field">
                                            <label class="label">Seat Number</label>
                                            <div class="control has-icons-left">
                                                <input class="input" type="text" name="seat_number" 
                                                       value="<?php echo htmlspecialchars($_SESSION['registration_data']['seat_number'] ?? ''); ?>">
                                                <span class="icon is-left">
                                                    <i class="fas fa-chair"></i>
                                                </span>
                                            </div>
                                        </div>
                                    </div>
                                </div>

                                <div class="field">
                                    <label class="label">Address</label>
                                    <div class="control">
                                        <textarea class="textarea" name="address" rows="3" 
                                                  placeholder="Enter your full address"><?php echo htmlspecialchars($_SESSION['registration_data']['address'] ?? ''); ?></textarea>
                                    </div>
                                </div>

                                <div class="buttons is-centered">
                                    <a href="registration.php" class="button is-light is-large">
                                        <i class="fas fa-arrow-left mr-2"></i>
                                        Back
                                    </a>
                                    <button class="button is-primary is-large" type="submit">
                                        Continue to Photo Selection
                                        <i class="fas fa-arrow-right ml-2"></i>
                                    </button>
                                </div>
                            </form>
                        </div>
                    </div>
                </div>

                <!-- Back to Home -->
                <div class="has-text-centered mt-6">
                    <a href="index.php" class="button is-light is-large">
                        <i class="fas fa-home mr-2"></i>
                        Back to Home
                    </a>
                </div>
            </div>
        </div>
    </section>
</body>
</html>
