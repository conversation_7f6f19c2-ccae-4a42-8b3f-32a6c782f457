<?php
// Simple test file to check what's causing the error
error_reporting(E_ALL);
ini_set('display_errors', 1);

echo "<h1>PHP Test</h1>";
echo "<p>PHP Version: " . phpversion() . "</p>";

// Test basic database connection
try {
    echo "<h2>Testing Database Connection</h2>";
    
    // Test MySQL connection
    $pdo = new PDO("mysql:host=localhost", "root", "");
    echo "<p>✅ MySQL connection successful</p>";
    
    // Check if databases exist
    $stmt = $pdo->query("SHOW DATABASES");
    $databases = $stmt->fetchAll(PDO::FETCH_COLUMN);
    
    echo "<h3>Available Databases:</h3><ul>";
    foreach ($databases as $db) {
        echo "<li>" . htmlspecialchars($db) . "</li>";
    }
    echo "</ul>";
    
    // Check if our databases exist
    $local_exists = in_array('photo_session_management', $databases);
    $external_exists = in_array('student_registration', $databases);
    
    echo "<h3>Required Databases:</h3>";
    echo "<p>photo_session_management: " . ($local_exists ? "✅ EXISTS" : "❌ MISSING") . "</p>";
    echo "<p>student_registration: " . ($external_exists ? "✅ EXISTS" : "❌ MISSING") . "</p>";
    
    if (!$local_exists || !$external_exists) {
        echo "<div style='background: #ffebee; padding: 10px; border-left: 4px solid #f44336; margin: 10px 0;'>";
        echo "<strong>Action Required:</strong> You need to create the databases first!<br>";
        echo "1. Go to <a href='http://localhost/phpmyadmin' target='_blank'>phpMyAdmin</a><br>";
        echo "2. Click 'SQL' tab<br>";
        echo "3. Copy and paste the contents of database/local_database.sql<br>";
        echo "4. Click 'Go'<br>";
        echo "5. Repeat for database/external_database.sql";
        echo "</div>";
    }
    
} catch (Exception $e) {
    echo "<p>❌ Database connection failed: " . htmlspecialchars($e->getMessage()) . "</p>";
    echo "<div style='background: #ffebee; padding: 10px; border-left: 4px solid #f44336; margin: 10px 0;'>";
    echo "<strong>Possible Solutions:</strong><br>";
    echo "1. Make sure XAMPP MySQL service is running<br>";
    echo "2. Check if MySQL is running on port 3306<br>";
    echo "3. Verify MySQL root user has no password (default XAMPP setup)";
    echo "</div>";
}

// Test file permissions
echo "<h2>Testing File Permissions</h2>";
$dirs_to_check = ['logs', 'uploads'];
foreach ($dirs_to_check as $dir) {
    if (is_dir($dir)) {
        if (is_writable($dir)) {
            echo "<p>✅ {$dir}/ directory is writable</p>";
        } else {
            echo "<p>❌ {$dir}/ directory is not writable</p>";
        }
    } else {
        echo "<p>❌ {$dir}/ directory does not exist</p>";
    }
}

echo "<h2>Next Steps</h2>";
echo "<ol>";
echo "<li>Fix any database issues shown above</li>";
echo "<li>Once databases are created, try accessing <a href='index.php'>index.php</a></li>";
echo "<li>If still having issues, check XAMPP error logs in: C:\\xampp\\apache\\logs\\error.log</li>";
echo "</ol>";
?>
