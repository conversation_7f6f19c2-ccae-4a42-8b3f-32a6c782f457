<?php
require_once '../config/config.php';
require_admin();

$operatorService = new OperatorService();
$message = '';
$message_type = '';

// Handle form submissions
if ($_SERVER['REQUEST_METHOD'] === 'POST') {
    if (!verify_csrf_token($_POST['csrf_token'] ?? '')) {
        $message = 'Invalid security token';
        $message_type = 'is-danger';
    } else {
        $action = $_POST['action'] ?? '';
        
        switch ($action) {
            case 'create':
                $result = $operatorService->createOperator([
                    'username' => sanitize_input($_POST['username'] ?? ''),
                    'password' => $_POST['password'] ?? '',
                    'full_name' => sanitize_input($_POST['full_name'] ?? ''),
                    'role' => sanitize_input($_POST['role'] ?? ''),
                    'is_active' => isset($_POST['is_active'])
                ]);
                $message = $result['message'];
                $message_type = $result['success'] ? 'is-success' : 'is-danger';
                break;
                
            case 'update':
                $operator_id = intval($_POST['operator_id'] ?? 0);
                $update_data = [
                    'full_name' => sanitize_input($_POST['full_name'] ?? ''),
                    'role' => sanitize_input($_POST['role'] ?? ''),
                    'is_active' => isset($_POST['is_active'])
                ];
                
                if (!empty($_POST['password'])) {
                    $update_data['password'] = $_POST['password'];
                }
                
                $result = $operatorService->updateOperator($operator_id, $update_data);
                $message = $result['message'];
                $message_type = $result['success'] ? 'is-success' : 'is-danger';
                break;
                
            case 'delete':
                $operator_id = intval($_POST['operator_id'] ?? 0);
                $result = $operatorService->deleteOperator($operator_id);
                $message = $result['message'];
                $message_type = $result['success'] ? 'is-success' : 'is-danger';
                break;
        }
    }
}

$operators = $operatorService->getAllOperators(true);
$stats = $operatorService->getOperatorStatistics();
?>
<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Manage Operators - <?php echo APP_NAME; ?></title>
    <link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/bulma@0.9.4/css/bulma.min.css">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css">
    <style>
        .dashboard-header {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
        }
        .navbar-item.has-text-white:hover {
            background-color: rgba(255,255,255,0.1);
            color: white !important;
        }
        .navbar-item.is-active {
            background-color: rgba(255,255,255,0.2);
            font-weight: bold;
        }
        .navbar-dropdown {
            background-color: white;
            border: 1px solid #dbdbdb;
            box-shadow: 0 8px 16px rgba(10,10,10,.1);
        }
        .navbar-dropdown .navbar-item {
            color: #363636 !important;
            padding: 0.75rem 1rem;
        }
        .navbar-dropdown .navbar-item:hover {
            background-color: #f5f5f5;
            color: #363636 !important;
        }
        .navbar-dropdown .navbar-divider {
            background-color: #dbdbdb;
        }
        .stat-card .heading {
            color: #7a7a7a;
            font-weight: 600;
            text-transform: uppercase;
            font-size: 0.75rem;
        }
        .stat-card .title {
            color: #363636;
            font-weight: 700;
        }
        .navbar-brand .navbar-item {
            color: white !important;
            font-weight: bold;
        }
        .navbar-item.has-dropdown .navbar-link {
            color: white !important;
        }
        .navbar-item.has-dropdown .navbar-link:hover {
            background-color: rgba(255,255,255,0.05) !important;
            color: white !important;
        }
        .navbar-item.has-dropdown.is-active .navbar-link,
        .navbar-item.has-dropdown:hover .navbar-link {
            background-color: rgba(255,255,255,0.05) !important;
            color: white !important;
        }
        .navbar-link::after {
            border-color: white !important;
        }
        .stat-card {
            border-left: 4px solid #667eea;
        }
        .operator-row.inactive {
            opacity: 0.6;
        }
    </style>
</head>
<body>
    <!-- Navigation -->
    <nav class="navbar dashboard-header" role="navigation">
        <div class="navbar-brand">
            <div class="navbar-item" style="border-right: 1px solid #ccc; padding-right: 1rem; margin-right: 1rem;">
                <i class="fas fa-shield-alt mr-2" style="color: #ffffffff;"></i>
                <span style="font-weight: bold; color: #ffffffff;">Admin Dashboard</span>
            </div>
        </div>
        
        <div class="navbar-menu">
            <div class="navbar-start">
                <a class="navbar-item has-text-white" href="dashboard.php">
                    <i class="fas fa-tachometer-alt mr-2"></i>
                    Dashboard
                </a>
                <a class="navbar-item has-text-white is-active" href="operators.php">
                    <i class="fas fa-users mr-2"></i>
                    Operators
                </a>
                <a class="navbar-item has-text-white" href="sessions.php">
                    <i class="fas fa-camera mr-2"></i>
                    Photo Sessions
                </a>
                <a class="navbar-item has-text-white" href="photo-pricing.php">
                    <i class="fas fa-tags mr-2"></i>
                    Photo Pricing
                </a>
                <a class="navbar-item has-text-white" href="reports.php">
                    <i class="fas fa-chart-bar mr-2"></i>
                    Reports
                </a>
            </div>
            
            <div class="navbar-end">
                <div class="navbar-item has-dropdown is-hoverable">
                    <a class="navbar-link has-text-white">
                        <i class="fas fa-user-shield mr-2"></i>
                        <?php echo htmlspecialchars($_SESSION['full_name']); ?>
                    </a>
                    <div class="navbar-dropdown">
                        <a class="navbar-item" href="profile.php">
                            <i class="fas fa-user-cog mr-2"></i>
                            Profile
                        </a>
                        <a class="navbar-item" href="settings.php">
                            <i class="fas fa-cog mr-2"></i>
                            Settings
                        </a>
                        <hr class="navbar-divider">
                        <a class="navbar-item" href="../logout.php">
                            <i class="fas fa-sign-out-alt mr-2"></i>
                            Logout
                        </a>
                    </div>
                </div>
            </div>
        </div>
    </nav>

    <div class="container mt-4">
        <!-- Header -->
        <div class="box">
            <div class="level">
                <div class="level-left">
                    <div>
                        <h1 class="title is-4">
                            <i class="fas fa-users mr-2"></i>
                            Manage Operators
                        </h1>
                        <p class="subtitle is-6">Add, edit, and manage system operators</p>
                    </div>
                </div>
                <div class="level-right">
                    <button class="button is-primary" onclick="openCreateModal()">
                        <i class="fas fa-user-plus mr-2"></i>
                        Add New Operator
                    </button>
                </div>
            </div>
        </div>

        <!-- Messages -->
        <?php if ($message): ?>
            <div class="notification <?php echo $message_type; ?>">
                <button class="delete" onclick="this.parentElement.style.display='none'"></button>
                <?php echo htmlspecialchars($message); ?>
            </div>
        <?php endif; ?>

        <!-- Statistics -->
        <div class="columns">
            <div class="column">
                <div class="box stat-card">
                    <div class="level">
                        <div class="level-left">
                            <div>
                                <p class="heading">Total Operators</p>
                                <p class="title is-4"><?php echo $stats['total_operators'] ?? 0; ?></p>
                            </div>
                        </div>
                        <div class="level-right">
                            <i class="fas fa-users fa-2x has-text-info"></i>
                        </div>
                    </div>
                </div>
            </div>
            <div class="column">
                <div class="box stat-card">
                    <div class="level">
                        <div class="level-left">
                            <div>
                                <p class="heading">Admins</p>
                                <p class="title is-4"><?php echo $stats['admins'] ?? 0; ?></p>
                            </div>
                        </div>
                        <div class="level-right">
                            <i class="fas fa-user-shield fa-2x has-text-success"></i>
                        </div>
                    </div>
                </div>
            </div>
            <div class="column">
                <div class="box stat-card">
                    <div class="level">
                        <div class="level-left">
                            <div>
                                <p class="heading">Operators</p>
                                <p class="title is-4"><?php echo $stats['operators'] ?? 0; ?></p>
                            </div>
                        </div>
                        <div class="level-right">
                            <i class="fas fa-user fa-2x has-text-primary"></i>
                        </div>
                    </div>
                </div>
            </div>
            <div class="column">
                <div class="box stat-card">
                    <div class="level">
                        <div class="level-left">
                            <div>
                                <p class="heading">Active (24h)</p>
                                <p class="title is-4"><?php echo $stats['active_operators'] ?? 0; ?></p>
                            </div>
                        </div>
                        <div class="level-right">
                            <i class="fas fa-user-check fa-2x has-text-warning"></i>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- Operators Table -->
        <div class="box">
            <div class="table-container">
                <table class="table is-fullwidth is-hoverable">
                    <thead>
                        <tr>
                            <th>Username</th>
                            <th>Full Name</th>
                            <th>Role</th>
                            <th>Status</th>
                            <th>Sessions</th>
                            <th>Last Login</th>
                            <th>Created</th>
                            <th>Actions</th>
                        </tr>
                    </thead>
                    <tbody>
                        <?php foreach ($operators as $operator): ?>
                            <tr class="operator-row <?php echo $operator['is_active'] ? '' : 'inactive'; ?>">
                                <td>
                                    <strong><?php echo htmlspecialchars($operator['username']); ?></strong>
                                    <?php if ($operator['id'] == $_SESSION['user_id']): ?>
                                        <span class="tag is-small is-info ml-2">You</span>
                                    <?php endif; ?>
                                </td>
                                <td><?php echo htmlspecialchars($operator['full_name']); ?></td>
                                <td>
                                    <span class="tag <?php echo $operator['role'] === 'admin' ? 'is-success' : 'is-info'; ?>">
                                        <i class="fas <?php echo $operator['role'] === 'admin' ? 'fa-user-shield' : 'fa-user'; ?> mr-1"></i>
                                        <?php echo ucfirst($operator['role']); ?>
                                    </span>
                                </td>
                                <td>
                                    <span class="tag <?php echo $operator['is_active'] ? 'is-success' : 'is-danger'; ?>">
                                        <?php echo $operator['is_active'] ? 'Active' : 'Inactive'; ?>
                                    </span>
                                </td>
                                <td>
                                    <span class="has-text-weight-bold"><?php echo $operator['total_sessions']; ?></span>
                                    <?php if ($operator['today_sessions'] > 0): ?>
                                        <span class="tag is-small is-primary ml-1">
                                            <?php echo $operator['today_sessions']; ?> today
                                        </span>
                                    <?php endif; ?>
                                </td>
                                <td>
                                    <?php if ($operator['last_login']): ?>
                                        <span title="<?php echo $operator['last_login']; ?>">
                                            <?php echo date('M j, Y H:i', strtotime($operator['last_login'])); ?>
                                        </span>
                                    <?php else: ?>
                                        <span class="has-text-grey">Never</span>
                                    <?php endif; ?>
                                </td>
                                <td>
                                    <span title="<?php echo $operator['created_at']; ?>">
                                        <?php echo date('M j, Y', strtotime($operator['created_at'])); ?>
                                    </span>
                                    <?php if ($operator['created_by_name']): ?>
                                        <br><small class="has-text-grey">by <?php echo htmlspecialchars($operator['created_by_name']); ?></small>
                                    <?php endif; ?>
                                </td>
                                <td>
                                    <div class="buttons are-small">
                                        <button class="button is-info" onclick="openEditModal(<?php echo htmlspecialchars(json_encode($operator)); ?>)">
                                            <i class="fas fa-edit"></i>
                                        </button>
                                        <?php if ($operator['id'] != $_SESSION['user_id']): ?>
                                            <button class="button is-danger" onclick="confirmDelete(<?php echo $operator['id']; ?>, '<?php echo htmlspecialchars($operator['username']); ?>')">
                                                <i class="fas fa-trash"></i>
                                            </button>
                                        <?php endif; ?>
                                    </div>
                                </td>
                            </tr>
                        <?php endforeach; ?>
                    </tbody>
                </table>
            </div>
        </div>
    </div>

    <!-- Create Operator Modal -->
    <div class="modal" id="createModal">
        <div class="modal-background" onclick="closeModal('createModal')"></div>
        <div class="modal-card">
            <header class="modal-card-head">
                <p class="modal-card-title">
                    <i class="fas fa-user-plus mr-2"></i>
                    Add New Operator
                </p>
                <button class="delete" onclick="closeModal('createModal')"></button>
            </header>
            <form method="POST" action="">
                <section class="modal-card-body">
                    <input type="hidden" name="csrf_token" value="<?php echo generate_csrf_token(); ?>">
                    <input type="hidden" name="action" value="create">
                    
                    <div class="field">
                        <label class="label">Username *</label>
                        <div class="control">
                            <input class="input" type="text" name="username" required>
                        </div>
                    </div>
                    
                    <div class="field">
                        <label class="label">Full Name *</label>
                        <div class="control">
                            <input class="input" type="text" name="full_name" required>
                        </div>
                    </div>
                    
                    <div class="field">
                        <label class="label">Password *</label>
                        <div class="control">
                            <input class="input" type="password" name="password" required>
                        </div>
                        <p class="help">Minimum 6 characters</p>
                    </div>
                    
                    <div class="field">
                        <label class="label">Role *</label>
                        <div class="control">
                            <div class="select is-fullwidth">
                                <select name="role" required>
                                    <option value="">Select Role</option>
                                    <option value="operator">Operator</option>
                                    <option value="admin">Admin</option>
                                </select>
                            </div>
                        </div>
                    </div>
                    
                    <div class="field">
                        <div class="control">
                            <label class="checkbox">
                                <input type="checkbox" name="is_active" checked>
                                Active
                            </label>
                        </div>
                    </div>
                </section>
                <footer class="modal-card-foot">
                    <button class="button is-primary" type="submit">
                        <i class="fas fa-save mr-2"></i>
                        Create Operator
                    </button>
                    <button class="button" type="button" onclick="closeModal('createModal')">Cancel</button>
                </footer>
            </form>
        </div>
    </div>

    <!-- Edit Operator Modal -->
    <div class="modal" id="editModal">
        <div class="modal-background" onclick="closeModal('editModal')"></div>
        <div class="modal-card">
            <header class="modal-card-head">
                <p class="modal-card-title">
                    <i class="fas fa-user-edit mr-2"></i>
                    Edit Operator
                </p>
                <button class="delete" onclick="closeModal('editModal')"></button>
            </header>
            <form method="POST" action="">
                <section class="modal-card-body">
                    <input type="hidden" name="csrf_token" value="<?php echo generate_csrf_token(); ?>">
                    <input type="hidden" name="action" value="update">
                    <input type="hidden" name="operator_id" id="edit_operator_id">
                    
                    <div class="field">
                        <label class="label">Username</label>
                        <div class="control">
                            <input class="input" type="text" id="edit_username" readonly>
                        </div>
                        <p class="help">Username cannot be changed</p>
                    </div>
                    
                    <div class="field">
                        <label class="label">Full Name *</label>
                        <div class="control">
                            <input class="input" type="text" name="full_name" id="edit_full_name" required>
                        </div>
                    </div>
                    
                    <div class="field">
                        <label class="label">New Password</label>
                        <div class="control">
                            <input class="input" type="password" name="password" id="edit_password">
                        </div>
                        <p class="help">Leave blank to keep current password</p>
                    </div>
                    
                    <div class="field">
                        <label class="label">Role *</label>
                        <div class="control">
                            <div class="select is-fullwidth">
                                <select name="role" id="edit_role" required>
                                    <option value="operator">Operator</option>
                                    <option value="admin">Admin</option>
                                </select>
                            </div>
                        </div>
                    </div>
                    
                    <div class="field">
                        <div class="control">
                            <label class="checkbox">
                                <input type="checkbox" name="is_active" id="edit_is_active">
                                Active
                            </label>
                        </div>
                    </div>
                </section>
                <footer class="modal-card-foot">
                    <button class="button is-primary" type="submit">
                        <i class="fas fa-save mr-2"></i>
                        Update Operator
                    </button>
                    <button class="button" type="button" onclick="closeModal('editModal')">Cancel</button>
                </footer>
            </form>
        </div>
    </div>

    <!-- Delete Confirmation Modal -->
    <div class="modal" id="deleteModal">
        <div class="modal-background" onclick="closeModal('deleteModal')"></div>
        <div class="modal-card">
            <header class="modal-card-head">
                <p class="modal-card-title">
                    <i class="fas fa-exclamation-triangle mr-2"></i>
                    Confirm Deletion
                </p>
                <button class="delete" onclick="closeModal('deleteModal')"></button>
            </header>
            <section class="modal-card-body">
                <p>Are you sure you want to deactivate operator <strong id="delete_username"></strong>?</p>
                <p class="has-text-grey mt-2">This action will deactivate the operator account but preserve all associated data.</p>
            </section>
            <footer class="modal-card-foot">
                <form method="POST" action="">
                    <input type="hidden" name="csrf_token" value="<?php echo generate_csrf_token(); ?>">
                    <input type="hidden" name="action" value="delete">
                    <input type="hidden" name="operator_id" id="delete_operator_id">
                    <button class="button is-danger" type="submit">
                        <i class="fas fa-trash mr-2"></i>
                        Deactivate
                    </button>
                    <button class="button" type="button" onclick="closeModal('deleteModal')">Cancel</button>
                </form>
            </footer>
        </div>
    </div>

    <script>
        function openCreateModal() {
            document.getElementById('createModal').classList.add('is-active');
        }
        
        function openEditModal(operator) {
            document.getElementById('edit_operator_id').value = operator.id;
            document.getElementById('edit_username').value = operator.username;
            document.getElementById('edit_full_name').value = operator.full_name;
            document.getElementById('edit_role').value = operator.role;
            document.getElementById('edit_is_active').checked = operator.is_active == 1;
            document.getElementById('edit_password').value = '';
            
            document.getElementById('editModal').classList.add('is-active');
        }
        
        function confirmDelete(operatorId, username) {
            document.getElementById('delete_operator_id').value = operatorId;
            document.getElementById('delete_username').textContent = username;
            document.getElementById('deleteModal').classList.add('is-active');
        }
        
        function closeModal(modalId) {
            document.getElementById(modalId).classList.remove('is-active');
        }
        
        // Close modals with Escape key
        document.addEventListener('keydown', function(event) {
            if (event.key === 'Escape') {
                const activeModal = document.querySelector('.modal.is-active');
                if (activeModal) {
                    activeModal.classList.remove('is-active');
                }
            }
        });
    </script>
</body>
</html>
