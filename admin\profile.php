<?php
require_once '../config/config.php';
require_admin();

$message = '';
$message_type = '';

// Handle password change
if ($_SERVER['REQUEST_METHOD'] === 'POST') {
    if (!verify_csrf_token($_POST['csrf_token'] ?? '')) {
        $message = 'Invalid security token';
        $message_type = 'is-danger';
    } else {
        $action = $_POST['action'] ?? '';
        
        if ($action === 'change_password') {
            $current_password = $_POST['current_password'] ?? '';
            $new_password = $_POST['new_password'] ?? '';
            $confirm_password = $_POST['confirm_password'] ?? '';
            
            if (empty($current_password) || empty($new_password) || empty($confirm_password)) {
                $message = 'All password fields are required';
                $message_type = 'is-danger';
            } elseif ($new_password !== $confirm_password) {
                $message = 'New passwords do not match';
                $message_type = 'is-danger';
            } else {
                $auth = new Auth();
                $result = $auth->changePassword($_SESSION['user_id'], $current_password, $new_password);
                $message = $result['message'];
                $message_type = $result['success'] ? 'is-success' : 'is-danger';
            }
        }
    }
}

// Get user info and statistics
$operatorService = new OperatorService();
$user = $operatorService->getOperatorById($_SESSION['user_id']);
$operatorStats = $operatorService->getOperatorStatistics();

$photoSessionService = new PhotoSessionService();
$sessionStats = $photoSessionService->getSessionStatistics();

$studentService = new StudentService();
$totalRegistered = $studentService->getTotalRegisteredCount();
?>
<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Admin Profile - <?php echo APP_NAME; ?></title>
    <link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/bulma@0.9.4/css/bulma.min.css">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css">
    <style>
        .dashboard-header {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
        }
        .navbar-item.has-text-white:hover {
            background-color: rgba(255,255,255,0.1);
            color: white !important;
        }
        .navbar-item.is-active {
            background-color: rgba(255,255,255,0.2);
            font-weight: bold;
        }
        .navbar-dropdown {
            background-color: white;
            border: 1px solid #dbdbdb;
            box-shadow: 0 8px 16px rgba(10,10,10,.1);
        }
        .navbar-dropdown .navbar-item {
            color: #363636 !important;
            padding: 0.75rem 1rem;
        }
        .navbar-dropdown .navbar-item:hover {
            background-color: #f5f5f5;
            color: #363636 !important;
        }
        .navbar-dropdown .navbar-divider {
            background-color: #dbdbdb;
        }
        .stat-card .heading {
            color: #7a7a7a;
            font-weight: 600;
            text-transform: uppercase;
            font-size: 0.75rem;
        }
        .stat-card .title {
            color: #363636;
            font-weight: 700;
        }
        .navbar-brand .navbar-item {
            color: white !important;
            font-weight: bold;
        }
        .navbar-item.has-dropdown .navbar-link {
            color: white !important;
        }
        .navbar-item.has-dropdown .navbar-link:hover {
            background-color: rgba(255,255,255,0.05) !important;
            color: white !important;
        }
        .navbar-item.has-dropdown.is-active .navbar-link,
        .navbar-item.has-dropdown:hover .navbar-link {
            background-color: rgba(255,255,255,0.05) !important;
            color: white !important;
        }
        .navbar-link::after {
            border-color: white !important;
        }
        .profile-card {
            border-left: 4px solid #667eea;
        }
        .stat-card {
            border-left: 4px solid #48c78e;
            transition: transform 0.2s;
        }
        .stat-card:hover {
            transform: translateY(-2px);
        }
    </style>
</head>
<body>
    <!-- Navigation -->
    <nav class="navbar dashboard-header" role="navigation">
        <div class="navbar-brand">
            <div class="navbar-item" style="border-right: 1px solid #ccc; padding-right: 1rem; margin-right: 1rem;">
                <i class="fas fa-shield-alt mr-2" style="color: #ffffffff;"></i>
                <span style="font-weight: bold; color: #ffffffff;">Admin Dashboard</span>
            </div>
        </div>
        
        <div class="navbar-menu">
            <div class="navbar-start">
                <a class="navbar-item has-text-white" href="dashboard.php">
                    <i class="fas fa-tachometer-alt mr-2"></i>
                    Dashboard
                </a>
                <a class="navbar-item has-text-white" href="operators.php">
                    <i class="fas fa-users mr-2"></i>
                    Operators
                </a>
                <a class="navbar-item has-text-white" href="sessions.php">
                    <i class="fas fa-camera mr-2"></i>
                    Photo Sessions
                </a>
                <a class="navbar-item has-text-white" href="reports.php">
                    <i class="fas fa-chart-bar mr-2"></i>
                    Reports
                </a>
            </div>
            
            <div class="navbar-end">
                <div class="navbar-item has-dropdown is-hoverable">
                    <a class="navbar-link has-text-white">
                        <i class="fas fa-user-shield mr-2"></i>
                        <?php echo htmlspecialchars($_SESSION['full_name']); ?>
                    </a>
                    <div class="navbar-dropdown">
                        <a class="navbar-item is-active" href="profile.php">
                            <i class="fas fa-user-cog mr-2"></i>
                            Profile
                        </a>
                        <a class="navbar-item" href="settings.php">
                            <i class="fas fa-cog mr-2"></i>
                            Settings
                        </a>
                        <hr class="navbar-divider">
                        <a class="navbar-item" href="../logout.php">
                            <i class="fas fa-sign-out-alt mr-2"></i>
                            Logout
                        </a>
                    </div>
                </div>
            </div>
        </div>
    </nav>

    <div class="container mt-4">
        <!-- Header -->
        <div class="box">
            <h1 class="title is-4">
                <i class="fas fa-user-shield mr-2"></i>
                Administrator Profile
            </h1>
            <p class="subtitle is-6">Manage your account settings and view system overview</p>
        </div>

        <!-- Messages -->
        <?php if ($message): ?>
            <div class="notification <?php echo $message_type; ?>">
                <button class="delete" onclick="this.parentElement.style.display='none'"></button>
                <?php echo htmlspecialchars($message); ?>
            </div>
        <?php endif; ?>

        <div class="columns">
            <!-- Profile Information -->
            <div class="column is-8">
                <div class="box profile-card">
                    <h2 class="title is-5">
                        <i class="fas fa-user mr-2"></i>
                        Account Information
                    </h2>
                    
                    <div class="columns">
                        <div class="column is-6">
                            <div class="field">
                                <label class="label">Username</label>
                                <div class="control">
                                    <input class="input" type="text" value="<?php echo htmlspecialchars($user['username']); ?>" readonly>
                                </div>
                                <p class="help">Username cannot be changed</p>
                            </div>
                            
                            <div class="field">
                                <label class="label">Full Name</label>
                                <div class="control">
                                    <input class="input" type="text" value="<?php echo htmlspecialchars($user['full_name']); ?>" readonly>
                                </div>
                                <p class="help">Contact system developer to change your name</p>
                            </div>
                        </div>
                        
                        <div class="column is-6">
                            <div class="field">
                                <label class="label">Role</label>
                                <div class="control">
                                    <span class="tag is-success is-large">
                                        <i class="fas fa-user-shield mr-2"></i>
                                        System Administrator
                                    </span>
                                </div>
                            </div>
                            
                            <div class="field">
                                <label class="label">Account Status</label>
                                <div class="control">
                                    <span class="tag is-success is-large">
                                        <i class="fas fa-check-circle mr-2"></i>
                                        Active
                                    </span>
                                </div>
                            </div>
                        </div>
                    </div>
                    
                    <div class="columns">
                        <div class="column is-6">
                            <div class="field">
                                <label class="label">Administrator Since</label>
                                <div class="control">
                                    <input class="input" type="text" 
                                           value="<?php echo date('F j, Y', strtotime($user['created_at'])); ?>" readonly>
                                </div>
                            </div>
                        </div>
                        
                        <div class="column is-6">
                            <div class="field">
                                <label class="label">Last Login</label>
                                <div class="control">
                                    <input class="input" type="text" 
                                           value="<?php echo $user['last_login'] ? date('F j, Y H:i', strtotime($user['last_login'])) : 'Never'; ?>" readonly>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- Change Password -->
                <div class="box">
                    <h2 class="title is-5">
                        <i class="fas fa-lock mr-2"></i>
                        Change Password
                    </h2>
                    
                    <form method="POST" action="">
                        <input type="hidden" name="csrf_token" value="<?php echo generate_csrf_token(); ?>">
                        <input type="hidden" name="action" value="change_password">
                        
                        <div class="field">
                            <label class="label">Current Password</label>
                            <div class="control">
                                <input class="input" type="password" name="current_password" required>
                            </div>
                        </div>
                        
                        <div class="field">
                            <label class="label">New Password</label>
                            <div class="control">
                                <input class="input" type="password" name="new_password" required>
                            </div>
                            <p class="help">Minimum 6 characters</p>
                        </div>
                        
                        <div class="field">
                            <label class="label">Confirm New Password</label>
                            <div class="control">
                                <input class="input" type="password" name="confirm_password" required>
                            </div>
                        </div>
                        
                        <div class="field">
                            <div class="control">
                                <button class="button is-primary" type="submit">
                                    <i class="fas fa-save mr-2"></i>
                                    Change Password
                                </button>
                            </div>
                        </div>
                    </form>
                </div>
            </div>
            
            <!-- System Overview -->
            <div class="column is-4">
                <div class="box stat-card">
                    <h2 class="title is-5">
                        <i class="fas fa-chart-bar mr-2"></i>
                        System Overview
                    </h2>
                    
                    <div class="level mb-4">
                        <div class="level-left">
                            <div>
                                <p class="heading">Total Operators</p>
                                <p class="title is-4"><?php echo $operatorStats['total_operators'] ?? 0; ?></p>
                            </div>
                        </div>
                        <div class="level-right">
                            <i class="fas fa-users fa-2x has-text-info"></i>
                        </div>
                    </div>
                    
                    <div class="level mb-4">
                        <div class="level-left">
                            <div>
                                <p class="heading">Active Operators</p>
                                <p class="title is-4"><?php echo $operatorStats['active_operators'] ?? 0; ?></p>
                            </div>
                        </div>
                        <div class="level-right">
                            <i class="fas fa-user-check fa-2x has-text-success"></i>
                        </div>
                    </div>
                    
                    <div class="level mb-4">
                        <div class="level-left">
                            <div>
                                <p class="heading">Total Sessions</p>
                                <p class="title is-4"><?php echo $sessionStats['total_sessions'] ?? 0; ?></p>
                            </div>
                        </div>
                        <div class="level-right">
                            <i class="fas fa-camera fa-2x has-text-primary"></i>
                        </div>
                    </div>
                    
                    <div class="level">
                        <div class="level-left">
                            <div>
                                <p class="heading">Today's Sessions</p>
                                <p class="title is-4"><?php echo $sessionStats['today_sessions'] ?? 0; ?></p>
                            </div>
                        </div>
                        <div class="level-right">
                            <i class="fas fa-calendar-day fa-2x has-text-warning"></i>
                        </div>
                    </div>
                </div>
                
                <!-- Quick Actions -->
                <div class="box">
                    <h2 class="title is-6">
                        <i class="fas fa-bolt mr-2"></i>
                        Quick Actions
                    </h2>
                    <div class="buttons">
                        <a href="operators.php" class="button is-info is-fullwidth">
                            <i class="fas fa-users mr-2"></i>
                            Manage Operators
                        </a>
                        <a href="sessions.php" class="button is-primary is-fullwidth">
                            <i class="fas fa-camera mr-2"></i>
                            View Sessions
                        </a>
                        <a href="settings.php" class="button is-success is-fullwidth">
                            <i class="fas fa-cog mr-2"></i>
                            System Settings
                        </a>
                        <a href="dashboard.php" class="button is-light is-fullwidth">
                            <i class="fas fa-tachometer-alt mr-2"></i>
                            Dashboard
                        </a>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <script>
        // Auto-hide notifications after 5 seconds
        document.addEventListener('DOMContentLoaded', function() {
            const notifications = document.querySelectorAll('.notification');
            notifications.forEach(function(notification) {
                setTimeout(function() {
                    notification.style.display = 'none';
                }, 5000);
            });
        });
    </script>
</body>
</html>
