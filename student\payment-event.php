<?php
require_once '../config/config.php';

if (session_status() === PHP_SESSION_NONE) {
    session_start();
}

// Check if payment method is selected and student data exists
if (!isset($_SESSION['selected_payment_method']) || $_SESSION['selected_payment_method'] !== 'pay_at_event' || 
    !isset($_SESSION['payment_student_data'])) {
    redirect('payment.php');
}

$student_data = $_SESSION['payment_student_data'];
$message = '';
$message_type = '';

// Handle confirmation
if ($_SERVER['REQUEST_METHOD'] === 'POST' && isset($_POST['action']) && $_POST['action'] === 'confirm_pay_at_event') {
    try {
        $pdo = get_pdo_connection();
        
        // Update payment status to pay at event
        $stmt = $pdo->prepare("
            UPDATE students 
            SET payment_status = 'pay_at_event', 
                payment_method = 'Pay at Event',
                updated_at = NOW()
            WHERE invoice_number = ?
        ");
        $stmt->execute([$student_data['invoice_number']]);
        
        // Log payment activity
        log_activity('PAY_AT_EVENT_SELECTED', "Pay at event selected for invoice: {$student_data['invoice_number']}");
        
        // Clear payment session data
        unset($_SESSION['selected_payment_method']);
        unset($_SESSION['payment_student_data']);
        unset($_SESSION['payment_invoice']);
        unset($_SESSION['payment_attempt_made']);
        
        // Store success data
        $_SESSION['payment_success'] = [
            'invoice_number' => $student_data['invoice_number'],
            'amount' => $student_data['total_amount'],
            'method' => 'Pay at Event',
            'status' => 'pay_at_event'
        ];
        
        redirect('payment-success.php');
        
    } catch (Exception $e) {
        $message = 'Error processing request. Please try again.';
        $message_type = 'is-danger';
        error_log("Pay at event error: " . $e->getMessage());
    }
}

// Get event details
$event_details = null;
try {
    $pdo = get_pdo_connection();
    $stmt = $pdo->prepare("SELECT * FROM events WHERE id = ?");
    $stmt->execute([$student_data['event_id']]);
    $event_details = $stmt->fetch(PDO::FETCH_ASSOC);
} catch (Exception $e) {
    error_log("Event details loading error: " . $e->getMessage());
}
?>
<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Pay at Event - <?php echo APP_NAME; ?></title>
    <link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/bulma@0.9.4/css/bulma.min.css">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css">
    <style>
        .hero-section {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
        }
        .payment-card {
            background: white;
            border-radius: 15px;
            box-shadow: 0 10px 30px rgba(0,0,0,0.1);
            overflow: hidden;
        }
        .warning-box {
            background: #fff3cd;
            border: 2px solid #ffeaa7;
            border-radius: 12px;
            padding: 1.5rem;
            margin-bottom: 2rem;
        }
        .event-info {
            background: #f8f9fa;
            border: 1px solid #e9ecef;
            border-radius: 8px;
            padding: 1.5rem;
            margin-bottom: 2rem;
        }
        .terms-box {
            background: #e8f4fd;
            border: 1px solid #bee5eb;
            border-radius: 8px;
            padding: 1rem;
            margin: 1rem 0;
        }
        .input, .select select {
            font-size: 0.9rem;
        }
    </style>
</head>
<body>
    <section class="hero hero-section">
        <div class="hero-body">
            <div class="container">
                <!-- Header -->
                <div class="has-text-centered mb-6">
                    <h1 class="title is-2 has-text-white">
                        <i class="fas fa-calendar-check mr-3"></i>
                        Pay at Event
                    </h1>
                    <p class="subtitle is-4 has-text-white">
                        Pay cash at the event venue
                    </p>
                </div>

                <!-- Messages -->
                <?php if ($message): ?>
                    <div class="columns is-centered">
                        <div class="column is-8">
                            <div class="notification <?php echo $message_type; ?>">
                                <button class="delete" onclick="this.parentElement.style.display='none'"></button>
                                <?php echo htmlspecialchars($message); ?>
                            </div>
                        </div>
                    </div>
                <?php endif; ?>

                <!-- Payment Information -->
                <div class="columns is-centered">
                    <div class="column is-8">
                        <div class="box payment-card">
                            
                            <!-- Warning Notice -->
                            <div class="warning-box">
                                <h2 class="title is-5 has-text-warning">
                                    <i class="fas fa-exclamation-triangle mr-2"></i>
                                    Important Notice
                                </h2>
                                <div class="content">
                                    <ul>
                                        <li><strong>Subject to availability:</strong> Photos may not be available if payment is not made in advance</li>
                                        <li><strong>Higher risk:</strong> Limited stock may result in unavailability of your selected photos</li>
                                        <li><strong>Longer wait times:</strong> Payment processing at the venue may cause delays</li>
                                        <li><strong>Cash only:</strong> Only cash payments accepted at the event</li>
                                    </ul>
                                </div>
                            </div>
                            
                            <div class="columns">
                                <!-- Order Summary -->
                                <div class="column is-6">
                                    <h2 class="title is-5">
                                        <i class="fas fa-receipt mr-2"></i>
                                        Order Summary
                                    </h2>
                                    <div class="content">
                                        <p><strong>Invoice:</strong> <?php echo htmlspecialchars($student_data['invoice_number']); ?></p>
                                        <p><strong>Name:</strong> <?php echo htmlspecialchars($student_data['full_name']); ?></p>
                                        <p><strong>Event:</strong> <?php echo htmlspecialchars($student_data['event_name']); ?></p>
                                        <hr>
                                        <p><strong>Amount to Pay at Event:</strong></p>
                                        <p class="title is-3 has-text-warning">LKR <?php echo number_format($student_data['total_amount'], 2); ?></p>
                                    </div>
                                    
                                    <?php if ($event_details): ?>
                                        <div class="event-info">
                                            <h3 class="title is-6">
                                                <i class="fas fa-map-marker-alt mr-2"></i>
                                                Event Information
                                            </h3>
                                            <div class="content is-small">
                                                <p><strong>Date:</strong> <?php echo date('F j, Y', strtotime($event_details['event_date'])); ?></p>
                                                <p><strong>Event:</strong> <?php echo htmlspecialchars($event_details['event_name']); ?></p>
                                                <?php if (!empty($event_details['description'])): ?>
                                                    <p><strong>Details:</strong> <?php echo htmlspecialchars($event_details['description']); ?></p>
                                                <?php endif; ?>
                                            </div>
                                        </div>
                                    <?php endif; ?>
                                </div>
                                
                                <!-- Payment Instructions -->
                                <div class="column is-6">
                                    <h2 class="title is-5">
                                        <i class="fas fa-info-circle mr-2"></i>
                                        Payment Instructions
                                    </h2>
                                    
                                    <div class="content">
                                        <h4 class="title is-6">What to bring:</h4>
                                        <ul>
                                            <li><strong>This invoice number:</strong> <?php echo htmlspecialchars($student_data['invoice_number']); ?></li>
                                            <li><strong>Exact cash amount:</strong> LKR <?php echo number_format($student_data['total_amount'], 2); ?></li>
                                            <li><strong>Valid ID:</strong> NIC or Passport</li>
                                            <li><strong>Print this page</strong> or save screenshot</li>
                                        </ul>
                                        
                                        <h4 class="title is-6">At the event:</h4>
                                        <ol>
                                            <li>Look for the <strong>"Photo Payment Counter"</strong></li>
                                            <li>Show your invoice number and ID</li>
                                            <li>Pay the exact amount in cash</li>
                                            <li>Collect your payment receipt</li>
                                            <li>Proceed to photo session area</li>
                                        </ol>
                                        
                                        <div class="terms-box">
                                            <h4 class="title is-6 has-text-info">
                                                <i class="fas fa-clock mr-2"></i>
                                                Payment Deadline
                                            </h4>
                                            <p class="is-size-7">
                                                Payment must be made <strong>before your scheduled photo session</strong>. 
                                                Late payments may result in session cancellation.
                                            </p>
                                        </div>
                                    </div>
                                    
                                    <form method="POST" action="">
                                        <input type="hidden" name="action" value="confirm_pay_at_event">
                                        
                                        <div class="field">
                                            <label class="checkbox">
                                                <input type="checkbox" required>
                                                I understand that this payment method is subject to availability and 
                                                I accept the risks associated with paying at the event.
                                            </label>
                                        </div>
                                        
                                        <div class="field">
                                            <label class="checkbox">
                                                <input type="checkbox" required>
                                                I will bring the exact cash amount and required documents to the event.
                                            </label>
                                        </div>
                                        
                                        <div class="field mt-5">
                                            <div class="control">
                                                <button class="button is-warning is-large is-fullwidth" type="submit">
                                                    <i class="fas fa-check mr-2"></i>
                                                    Confirm Pay at Event
                                                </button>
                                            </div>
                                        </div>
                                        
                                        <div class="field">
                                            <div class="control">
                                                <a href="payment.php" class="button is-light is-fullwidth">
                                                    <i class="fas fa-arrow-left mr-2"></i>
                                                    Back to Payment Options
                                                </a>
                                            </div>
                                        </div>
                                    </form>
                                </div>
                            </div>
                            
                            <!-- Recommendation -->
                            <div class="notification is-info is-light">
                                <h3 class="title is-6">
                                    <i class="fas fa-lightbulb mr-2"></i>
                                    Recommendation
                                </h3>
                                <p>
                                    For a smoother experience and guaranteed photo availability, we recommend paying online with 
                                    <a href="payment.php" class="has-text-link"><strong>Credit/Debit Card</strong></a> or 
                                    <a href="payment.php" class="has-text-link"><strong>Bank Transfer</strong></a>.
                                </p>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </section>

    <script>
        // Auto-hide notifications
        document.addEventListener('DOMContentLoaded', function() {
            const notifications = document.querySelectorAll('.notification');
            notifications.forEach(function(notification) {
                setTimeout(function() {
                    if (notification.parentElement) {
                        notification.style.display = 'none';
                    }
                }, 5000);
            });
        });
    </script>
</body>
</html>
