-- Add sample_image column to photo_types table
-- This allows admins to upload sample photos for each photo type

ALTER TABLE `photo_types` 
ADD COLUMN `sample_image` VARCHAR(255) NULL AFTER `description`,
ADD COLUMN `updated_at` TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP AFTER `created_at`;

-- Update existing records to have proper timestamps
UPDATE `photo_types` SET `updated_at` = `created_at` WHERE `updated_at` IS NULL;

-- Add index for better performance
ALTER TABLE `photo_types` ADD INDEX `idx_sample_image` (`sample_image`);

-- Show the updated table structure
DESCRIBE `photo_types`;
