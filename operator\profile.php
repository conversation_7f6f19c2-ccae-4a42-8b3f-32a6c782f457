<?php
require_once '../config/config.php';
require_login();

$message = '';
$message_type = '';

// Handle password change
if ($_SERVER['REQUEST_METHOD'] === 'POST') {
    if (!verify_csrf_token($_POST['csrf_token'] ?? '')) {
        $message = 'Invalid security token';
        $message_type = 'is-danger';
    } else {
        $current_password = $_POST['current_password'] ?? '';
        $new_password = $_POST['new_password'] ?? '';
        $confirm_password = $_POST['confirm_password'] ?? '';
        
        if (empty($current_password) || empty($new_password) || empty($confirm_password)) {
            $message = 'All password fields are required';
            $message_type = 'is-danger';
        } elseif ($new_password !== $confirm_password) {
            $message = 'New passwords do not match';
            $message_type = 'is-danger';
        } else {
            $auth = new Auth();
            $result = $auth->changePassword($_SESSION['user_id'], $current_password, $new_password);
            $message = $result['message'];
            $message_type = $result['success'] ? 'is-success' : 'is-danger';
        }
    }
}

// Get user info
$operatorService = new OperatorService();
$user = $operatorService->getOperatorById($_SESSION['user_id']);
$photoSessionService = new PhotoSessionService();
$userStats = [
    'total_sessions' => count($photoSessionService->getSessionsByOperator($_SESSION['user_id'])),
    'today_sessions' => count($photoSessionService->getSessionsByOperator($_SESSION['user_id'], date('Y-m-d')))
];
?>
<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Profile - <?php echo APP_NAME; ?></title>
    <link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/bulma@0.9.4/css/bulma.min.css">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css">
    <style>
        .dashboard-header {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
        }
        .profile-card {
            border-left: 4px solid #667eea;
        }
        .stat-card {
            border-left: 4px solid #48c78e;
        }
        .back-button {
            position: fixed;
            top: 20px;
            left: 20px;
            z-index: 1000;
        }
    </style>
</head>
<body>
    <!-- Back Button -->
    <a href="dashboard.php" class="button back-button">
        <i class="fas fa-arrow-left mr-2"></i>
        Back to Dashboard
    </a>

    <div class="container mt-6">
        <!-- Header -->
        <div class="box">
            <h1 class="title is-4">
                <i class="fas fa-user-cog mr-2"></i>
                Profile Settings
            </h1>
            <p class="subtitle is-6">Manage your account information and settings</p>
        </div>

        <!-- Messages -->
        <?php if ($message): ?>
            <div class="notification <?php echo $message_type; ?>">
                <button class="delete" onclick="this.parentElement.style.display='none'"></button>
                <?php echo htmlspecialchars($message); ?>
            </div>
        <?php endif; ?>

        <div class="columns">
            <!-- Profile Information -->
            <div class="column is-8">
                <div class="box profile-card">
                    <h2 class="title is-5">
                        <i class="fas fa-user mr-2"></i>
                        Account Information
                    </h2>
                    
                    <div class="columns">
                        <div class="column is-6">
                            <div class="field">
                                <label class="label">Username</label>
                                <div class="control">
                                    <input class="input" type="text" value="<?php echo htmlspecialchars($user['username']); ?>" readonly>
                                </div>
                                <p class="help">Username cannot be changed</p>
                            </div>
                            
                            <div class="field">
                                <label class="label">Full Name</label>
                                <div class="control">
                                    <input class="input" type="text" value="<?php echo htmlspecialchars($user['full_name']); ?>" readonly>
                                </div>
                                <p class="help">Contact admin to change your name</p>
                            </div>
                        </div>
                        
                        <div class="column is-6">
                            <div class="field">
                                <label class="label">Role</label>
                                <div class="control">
                                    <span class="tag <?php echo $user['role'] === 'admin' ? 'is-success' : 'is-info'; ?> is-large">
                                        <i class="fas <?php echo $user['role'] === 'admin' ? 'fa-user-shield' : 'fa-user'; ?> mr-2"></i>
                                        <?php echo ucfirst($user['role']); ?>
                                    </span>
                                </div>
                            </div>
                            
                            <div class="field">
                                <label class="label">Account Status</label>
                                <div class="control">
                                    <span class="tag <?php echo $user['is_active'] ? 'is-success' : 'is-danger'; ?> is-large">
                                        <i class="fas <?php echo $user['is_active'] ? 'fa-check-circle' : 'fa-times-circle'; ?> mr-2"></i>
                                        <?php echo $user['is_active'] ? 'Active' : 'Inactive'; ?>
                                    </span>
                                </div>
                            </div>
                        </div>
                    </div>
                    
                    <div class="columns">
                        <div class="column is-6">
                            <div class="field">
                                <label class="label">Member Since</label>
                                <div class="control">
                                    <input class="input" type="text" 
                                           value="<?php echo date('F j, Y', strtotime($user['created_at'])); ?>" readonly>
                                </div>
                            </div>
                        </div>
                        
                        <div class="column is-6">
                            <div class="field">
                                <label class="label">Last Login</label>
                                <div class="control">
                                    <input class="input" type="text" 
                                           value="<?php echo $user['last_login'] ? date('F j, Y H:i', strtotime($user['last_login'])) : 'Never'; ?>" readonly>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- Change Password -->
                <div class="box">
                    <h2 class="title is-5">
                        <i class="fas fa-lock mr-2"></i>
                        Change Password
                    </h2>
                    
                    <form method="POST" action="">
                        <input type="hidden" name="csrf_token" value="<?php echo generate_csrf_token(); ?>">
                        
                        <div class="field">
                            <label class="label">Current Password</label>
                            <div class="control">
                                <input class="input" type="password" name="current_password" required>
                            </div>
                        </div>
                        
                        <div class="field">
                            <label class="label">New Password</label>
                            <div class="control">
                                <input class="input" type="password" name="new_password" required>
                            </div>
                            <p class="help">Minimum 6 characters</p>
                        </div>
                        
                        <div class="field">
                            <label class="label">Confirm New Password</label>
                            <div class="control">
                                <input class="input" type="password" name="confirm_password" required>
                            </div>
                        </div>
                        
                        <div class="field">
                            <div class="control">
                                <button class="button is-primary" type="submit">
                                    <i class="fas fa-save mr-2"></i>
                                    Change Password
                                </button>
                            </div>
                        </div>
                    </form>
                </div>
            </div>
            
            <!-- Statistics -->
            <div class="column is-4">
                <div class="box stat-card">
                    <h2 class="title is-5">
                        <i class="fas fa-chart-bar mr-2"></i>
                        Your Statistics
                    </h2>
                    
                    <div class="level">
                        <div class="level-left">
                            <div>
                                <p class="heading">Total Sessions</p>
                                <p class="title is-3"><?php echo $userStats['total_sessions']; ?></p>
                            </div>
                        </div>
                        <div class="level-right">
                            <i class="fas fa-camera fa-2x has-text-info"></i>
                        </div>
                    </div>
                    
                    <div class="level">
                        <div class="level-left">
                            <div>
                                <p class="heading">Today's Sessions</p>
                                <p class="title is-3"><?php echo $userStats['today_sessions']; ?></p>
                            </div>
                        </div>
                        <div class="level-right">
                            <i class="fas fa-calendar-day fa-2x has-text-success"></i>
                        </div>
                    </div>
                </div>
                
                <!-- Quick Actions -->
                <div class="box">
                    <h2 class="title is-6">
                        <i class="fas fa-bolt mr-2"></i>
                        Quick Actions
                    </h2>
                    <div class="buttons">
                        <a href="sessions.php" class="button is-info is-fullwidth">
                            <i class="fas fa-list mr-2"></i>
                            View My Sessions
                        </a>
                        <a href="qr-scanner.php" class="button is-primary is-fullwidth">
                            <i class="fas fa-qrcode mr-2"></i>
                            Scan QR Code
                        </a>
                        <a href="dashboard.php" class="button is-light is-fullwidth">
                            <i class="fas fa-tachometer-alt mr-2"></i>
                            Dashboard
                        </a>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <script>
        // Auto-hide notifications after 5 seconds
        document.addEventListener('DOMContentLoaded', function() {
            const notifications = document.querySelectorAll('.notification');
            notifications.forEach(function(notification) {
                setTimeout(function() {
                    notification.style.display = 'none';
                }, 5000);
            });
        });
    </script>
</body>
</html>
