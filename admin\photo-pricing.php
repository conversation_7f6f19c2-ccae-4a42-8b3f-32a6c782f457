<?php
require_once '../config/config.php';
require_admin();

$message = '';
$message_type = '';

// Handle form submissions
if ($_SERVER['REQUEST_METHOD'] === 'POST') {
    if (!verify_csrf_token($_POST['csrf_token'] ?? '')) {
        $message = 'Invalid security token';
        $message_type = 'is-danger';
    } else {
        $action = $_POST['action'] ?? '';
        
        try {
            $pdo = get_pdo_connection();
            
            if ($action === 'update_pricing') {
                $event_id = (int)($_POST['event_id'] ?? 0);
                $pricing_data = $_POST['pricing'] ?? [];
                
                if ($event_id && !empty($pricing_data)) {
                    $pdo->beginTransaction();
                    
                    foreach ($pricing_data as $photo_type_id => $price) {
                        $price = (float)$price;
                        
                        // Update or insert pricing
                        $stmt = $pdo->prepare("
                            INSERT INTO event_photo_pricing (event_id, photo_type_id, price, updated_at)
                            VALUES (?, ?, ?, NOW())
                            ON DUPLICATE KEY UPDATE price = VALUES(price), updated_at = NOW()
                        ");
                        $stmt->execute([$event_id, $photo_type_id, $price]);
                    }
                    
                    $pdo->commit();
                    $message = 'Photo pricing updated successfully';
                    $message_type = 'is-success';
                } else {
                    $message = 'Please select an event and provide pricing information';
                    $message_type = 'is-warning';
                }
            } elseif ($action === 'add_event') {
                $event_name = sanitize_input($_POST['event_name'] ?? '');
                $event_code = sanitize_input($_POST['event_code'] ?? '');
                $event_date = sanitize_input($_POST['event_date'] ?? '');
                $description = sanitize_input($_POST['description'] ?? '');
                
                if ($event_name && $event_code) {
                    $stmt = $pdo->prepare("
                        INSERT INTO events (event_name, event_code, event_date, description)
                        VALUES (?, ?, ?, ?)
                    ");
                    $stmt->execute([$event_name, $event_code, $event_date ?: null, $description]);
                    
                    $message = 'Event added successfully';
                    $message_type = 'is-success';
                } else {
                    $message = 'Event name and code are required';
                    $message_type = 'is-warning';
                }
            } elseif ($action === 'add_photo_type') {
                $type_name = sanitize_input($_POST['type_name'] ?? '');
                $type_code = sanitize_input($_POST['type_code'] ?? '');
                $description = sanitize_input($_POST['description'] ?? '');
                
                if ($type_name && $type_code) {
                    $stmt = $pdo->prepare("
                        INSERT INTO photo_types (type_name, type_code, description)
                        VALUES (?, ?, ?)
                    ");
                    $stmt->execute([$type_name, $type_code, $description]);
                    
                    $message = 'Photo type added successfully';
                    $message_type = 'is-success';
                } else {
                    $message = 'Photo type name and code are required';
                    $message_type = 'is-warning';
                }
            }
        } catch (Exception $e) {
            if (isset($pdo)) {
                $pdo->rollBack();
            }
            $message = 'Error processing request: ' . $e->getMessage();
            $message_type = 'is-danger';
            error_log("Photo pricing error: " . $e->getMessage());
        }
    }
}

// Get data for display
try {
    $pdo = get_pdo_connection();
    
    // Get events
    $stmt = $pdo->query("SELECT * FROM events ORDER BY event_date DESC, event_name");
    $events = $stmt->fetchAll(PDO::FETCH_ASSOC);
    
    // Get photo types
    $stmt = $pdo->query("SELECT * FROM photo_types WHERE is_active = 1 ORDER BY type_name");
    $photo_types = $stmt->fetchAll(PDO::FETCH_ASSOC);
    
    // Get current pricing for selected event
    $selected_event_id = $_GET['event_id'] ?? ($events[0]['id'] ?? 0);
    $current_pricing = [];
    
    if ($selected_event_id) {
        $stmt = $pdo->prepare("
            SELECT pt.*, epp.price, epp.currency
            FROM photo_types pt
            LEFT JOIN event_photo_pricing epp ON pt.id = epp.photo_type_id AND epp.event_id = ?
            WHERE pt.is_active = 1
            ORDER BY pt.type_name
        ");
        $stmt->execute([$selected_event_id]);
        $current_pricing = $stmt->fetchAll(PDO::FETCH_ASSOC);
    }
    
} catch (Exception $e) {
    $message = 'Error loading data';
    $message_type = 'is-danger';
    $events = [];
    $photo_types = [];
    $current_pricing = [];
}
?>
<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Photo Pricing Management - <?php echo APP_NAME; ?></title>
    <link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/bulma@0.9.4/css/bulma.min.css">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css">
    <style>
        .dashboard-header {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
        }
        .navbar-item.has-text-white:hover {
            background-color: rgba(255,255,255,0.1);
            color: white !important;
        }
        .navbar-item.is-active {
            background-color: rgba(255,255,255,0.2);
            font-weight: bold;
        }
        .navbar-dropdown {
            background-color: white;
            border: 1px solid #dbdbdb;
            box-shadow: 0 8px 16px rgba(10,10,10,.1);
        }
        .navbar-dropdown .navbar-item {
            color: #363636 !important;
            padding: 0.75rem 1rem;
        }
        .navbar-dropdown .navbar-item:hover {
            background-color: #f5f5f5;
            color: #363636 !important;
        }
        .navbar-dropdown .navbar-divider {
            background-color: #dbdbdb;
        }
        .navbar-brand .navbar-item {
            color: white !important;
            font-weight: bold;
        }
        .navbar-item.has-dropdown .navbar-link {
            color: white !important;
        }
        .navbar-item.has-dropdown .navbar-link:hover {
            background-color: rgba(255,255,255,0.05) !important;
            color: white !important;
        }
        .navbar-item.has-dropdown.is-active .navbar-link,
        .navbar-item.has-dropdown:hover .navbar-link {
            background-color: rgba(255,255,255,0.05) !important;
            color: white !important;
        }
        .navbar-link::after {
            border-color: white !important;
        }
        .pricing-card {
            border-left: 4px solid #667eea;
            transition: transform 0.2s;
        }
        .pricing-card:hover {
            transform: translateY(-2px);
        }

        /* Mobile responsiveness for admin pages */
        @media screen and (max-width: 1024px) {
            .navbar-menu {
                background-color: #667eea;
            }

            .container {
                padding: 0 1rem;
            }
        }

        @media screen and (max-width: 768px) {
            .title.is-4 {
                font-size: 1.5rem;
            }

            .title.is-5 {
                font-size: 1.25rem;
            }

            .subtitle.is-6 {
                font-size: 1rem;
            }

            .box {
                padding: 1rem;
            }

            .pricing-card {
                margin-bottom: 1rem;
            }

            .columns .column {
                padding: 0.5rem;
            }

            .table-container {
                overflow-x: auto;
            }

            .table {
                font-size: 0.875rem;
            }

            .modal-card {
                margin: 1rem;
                max-height: calc(100vh - 2rem);
            }

            .modal-card-body {
                padding: 1rem;
            }

            .buttons .button {
                margin-bottom: 0.5rem;
            }
        }

        @media screen and (max-width: 480px) {
            .container {
                padding: 0 0.5rem;
            }

            .title.is-4 {
                font-size: 1.25rem;
            }

            .box {
                padding: 0.75rem;
            }

            .pricing-card .title.is-6 {
                font-size: 1rem;
            }

            .buttons {
                flex-direction: column;
            }

            .buttons .button {
                width: 100%;
                justify-content: flex-start;
            }

            .field.has-addons {
                flex-direction: column;
            }

            .field.has-addons .control {
                width: 100%;
            }

            .field.has-addons .control:not(:last-child) {
                margin-bottom: 0.5rem;
            }
        }

        /* Custom select dropdown arrow positioning */
        .select:not(.is-multiple):not(.is-loading)::after {
            margin-top: -0.9em !important;
            transform: none !important;
        }
    </style>
</head>
<body>
    <!-- Navigation -->
    <nav class="navbar dashboard-header" role="navigation">
        <div class="navbar-brand">
            <div class="navbar-item" style="border-right: 1px solid #ccc; padding-right: 1rem; margin-right: 1rem;">
                <i class="fas fa-shield-alt mr-2" style="color: #ffffffff;"></i>
                <span style="font-weight: bold; color: #ffffffff;">Admin Dashboard</span>
            </div>

            <a role="button" class="navbar-burger has-text-white" data-target="navbarMenu">
                <span></span>
                <span></span>
                <span></span>
            </a>
        </div>

        <div class="navbar-menu" id="navbarMenu">
            <div class="navbar-start">
                <a class="navbar-item has-text-white" href="dashboard.php">
                    <i class="fas fa-tachometer-alt mr-2"></i>
                    Dashboard
                </a>
                <a class="navbar-item has-text-white" href="operators.php">
                    <i class="fas fa-users mr-2"></i>
                    Operators
                </a>
                <a class="navbar-item has-text-white" href="sessions.php">
                    <i class="fas fa-camera mr-2"></i>
                    Photo Sessions
                </a>
                <a class="navbar-item has-text-white" href="event-sessions.php">
                    <i class="fas fa-calendar-alt mr-2"></i>
                    Event Sessions
                </a>
                <a class="navbar-item has-text-white" href="students.php">
                    <i class="fas fa-graduation-cap mr-2"></i>
                    Students
                </a>
                <a class="navbar-item has-text-white is-active" href="photo-pricing.php">
                    <i class="fas fa-tags mr-2"></i>
                    Photo Pricing
                </a>
                <a class="navbar-item has-text-white" href="reports.php">
                    <i class="fas fa-chart-bar mr-2"></i>
                    Reports
                </a>
            </div>
            
            <div class="navbar-end">
                <div class="navbar-item has-dropdown is-hoverable">
                    <a class="navbar-link has-text-white">
                        <i class="fas fa-user-shield mr-2"></i>
                        <?php echo htmlspecialchars($_SESSION['full_name']); ?>
                    </a>
                    <div class="navbar-dropdown">
                        <a class="navbar-item" href="profile.php">
                            <i class="fas fa-user-cog mr-2"></i>
                            Profile
                        </a>
                        <a class="navbar-item" href="settings.php">
                            <i class="fas fa-cog mr-2"></i>
                            Settings
                        </a>
                        <hr class="navbar-divider">
                        <a class="navbar-item" href="../logout.php">
                            <i class="fas fa-sign-out-alt mr-2"></i>
                            Logout
                        </a>
                    </div>
                </div>
            </div>
        </div>
    </nav>

    <div class="container mt-4">
        <!-- Header -->
        <div class="box">
            <h1 class="title is-4">
                <i class="fas fa-tags mr-2"></i>
                Photo Pricing Management
            </h1>
            <p class="subtitle is-6">Manage photo types, events, and pricing</p>
        </div>

        <!-- Messages -->
        <?php if ($message): ?>
            <div class="notification <?php echo $message_type; ?>">
                <button class="delete" onclick="this.parentElement.style.display='none'"></button>
                <?php echo htmlspecialchars($message); ?>
            </div>
        <?php endif; ?>

        <div class="columns">
            <!-- Main Pricing Management -->
            <div class="column is-8">
                <!-- Event Selection -->
                <div class="box pricing-card">
                    <h2 class="title is-5">
                        <i class="fas fa-calendar mr-2"></i>
                        Event-Based Pricing
                    </h2>
                    
                    <div class="field">
                        <label class="label">Select Event</label>
                        <div class="control">
                            <div class="select is-fullwidth">
                                <select onchange="window.location.href='?event_id=' + this.value">
                                    <option value="">Choose an event</option>
                                    <?php foreach ($events as $event): ?>
                                        <option value="<?php echo $event['id']; ?>" 
                                                <?php echo ($event['id'] == $selected_event_id) ? 'selected' : ''; ?>>
                                            <?php echo htmlspecialchars($event['event_name']); ?>
                                            <?php if ($event['event_date']): ?>
                                                - <?php echo date('M j, Y', strtotime($event['event_date'])); ?>
                                            <?php endif; ?>
                                        </option>
                                    <?php endforeach; ?>
                                </select>
                            </div>
                        </div>
                    </div>

                    <?php if ($selected_event_id && !empty($current_pricing)): ?>
                        <!-- Pricing Form -->
                        <form method="POST" action="">
                            <input type="hidden" name="csrf_token" value="<?php echo generate_csrf_token(); ?>">
                            <input type="hidden" name="action" value="update_pricing">
                            <input type="hidden" name="event_id" value="<?php echo $selected_event_id; ?>">
                            
                            <div class="table-container">
                                <table class="table is-fullwidth is-striped">
                                    <thead>
                                        <tr>
                                            <th>Photo Type</th>
                                            <th>Description</th>
                                            <th>Current Price (LKR)</th>
                                            <th>New Price (LKR)</th>
                                        </tr>
                                    </thead>
                                    <tbody>
                                        <?php foreach ($current_pricing as $pricing): ?>
                                            <tr>
                                                <td><strong><?php echo htmlspecialchars($pricing['type_name']); ?></strong></td>
                                                <td><?php echo htmlspecialchars($pricing['description'] ?? ''); ?></td>
                                                <td>
                                                    <?php if ($pricing['price']): ?>
                                                        <span class="tag is-info">LKR <?php echo number_format($pricing['price'], 2); ?></span>
                                                    <?php else: ?>
                                                        <span class="tag is-light">Not set</span>
                                                    <?php endif; ?>
                                                </td>
                                                <td>
                                                    <div class="control">
                                                        <input class="input" type="number" step="0.01" min="0" 
                                                               name="pricing[<?php echo $pricing['id']; ?>]" 
                                                               value="<?php echo $pricing['price'] ?? ''; ?>" 
                                                               placeholder="0.00">
                                                    </div>
                                                </td>
                                            </tr>
                                        <?php endforeach; ?>
                                    </tbody>
                                </table>
                            </div>
                            
                            <div class="field">
                                <div class="control">
                                    <button class="button is-primary" type="submit">
                                        <i class="fas fa-save mr-2"></i>
                                        Update Pricing
                                    </button>
                                </div>
                            </div>
                        </form>
                    <?php elseif ($selected_event_id): ?>
                        <div class="notification is-warning">
                            <i class="fas fa-exclamation-triangle mr-2"></i>
                            No photo types available. Please add photo types first.
                        </div>
                    <?php endif; ?>
                </div>
            </div>

            <!-- Management Tools -->
            <div class="column is-4">
                <!-- Add New Event -->
                <div class="box">
                    <h2 class="title is-6">
                        <i class="fas fa-plus mr-2"></i>
                        Add New Event
                    </h2>
                    
                    <form method="POST" action="">
                        <input type="hidden" name="csrf_token" value="<?php echo generate_csrf_token(); ?>">
                        <input type="hidden" name="action" value="add_event">
                        
                        <div class="field">
                            <label class="label is-small">Event Name</label>
                            <div class="control">
                                <input class="input is-small" type="text" name="event_name" required>
                            </div>
                        </div>
                        
                        <div class="field">
                            <label class="label is-small">Event Code</label>
                            <div class="control">
                                <input class="input is-small" type="text" name="event_code" required>
                            </div>
                        </div>
                        
                        <div class="field">
                            <label class="label is-small">Event Date</label>
                            <div class="control">
                                <input class="input is-small" type="date" name="event_date">
                            </div>
                        </div>
                        
                        <div class="field">
                            <label class="label is-small">Description</label>
                            <div class="control">
                                <textarea class="textarea is-small" name="description" rows="2"></textarea>
                            </div>
                        </div>
                        
                        <div class="field">
                            <div class="control">
                                <button class="button is-success is-small is-fullwidth" type="submit">
                                    <i class="fas fa-plus mr-2"></i>
                                    Add Event
                                </button>
                            </div>
                        </div>
                    </form>
                </div>

                <!-- Add New Photo Type -->
                <div class="box">
                    <h2 class="title is-6">
                        <i class="fas fa-camera mr-2"></i>
                        Add Photo Type
                    </h2>
                    
                    <form method="POST" action="">
                        <input type="hidden" name="csrf_token" value="<?php echo generate_csrf_token(); ?>">
                        <input type="hidden" name="action" value="add_photo_type">
                        
                        <div class="field">
                            <label class="label is-small">Type Name</label>
                            <div class="control">
                                <input class="input is-small" type="text" name="type_name" required>
                            </div>
                        </div>
                        
                        <div class="field">
                            <label class="label is-small">Type Code</label>
                            <div class="control">
                                <input class="input is-small" type="text" name="type_code" required>
                            </div>
                        </div>
                        
                        <div class="field">
                            <label class="label is-small">Description</label>
                            <div class="control">
                                <textarea class="textarea is-small" name="description" rows="2"></textarea>
                            </div>
                        </div>
                        
                        <div class="field">
                            <div class="control">
                                <button class="button is-info is-small is-fullwidth" type="submit">
                                    <i class="fas fa-plus mr-2"></i>
                                    Add Photo Type
                                </button>
                            </div>
                        </div>
                    </form>
                </div>

                <!-- Current Events -->
                <div class="box">
                    <h2 class="title is-6">
                        <i class="fas fa-list mr-2"></i>
                        Current Events
                    </h2>
                    
                    <?php if (empty($events)): ?>
                        <p class="has-text-grey is-size-7">No events available</p>
                    <?php else: ?>
                        <div style="max-height: 200px; overflow-y: auto;">
                            <?php foreach ($events as $event): ?>
                                <div class="box is-small mb-2">
                                    <p class="is-size-7">
                                        <strong><?php echo htmlspecialchars($event['event_name']); ?></strong><br>
                                        <span class="has-text-grey">
                                            <?php echo htmlspecialchars($event['event_code']); ?>
                                            <?php if ($event['event_date']): ?>
                                                | <?php echo date('M j, Y', strtotime($event['event_date'])); ?>
                                            <?php endif; ?>
                                        </span>
                                    </p>
                                </div>
                            <?php endforeach; ?>
                        </div>
                    <?php endif; ?>
                </div>
            </div>
        </div>
    </div>

    <script>
        // Auto-hide notifications after 5 seconds
        document.addEventListener('DOMContentLoaded', function() {
            const notifications = document.querySelectorAll('.notification');
            notifications.forEach(function(notification) {
                setTimeout(function() {
                    notification.style.display = 'none';
                }, 5000);
            });

            // Mobile navigation toggle
            const burger = document.querySelector('.navbar-burger');
            const menu = document.querySelector('.navbar-menu');

            if (burger && menu) {
                burger.addEventListener('click', function() {
                    burger.classList.toggle('is-active');
                    menu.classList.toggle('is-active');
                });
            }
        });
    </script>
</body>
</html>
