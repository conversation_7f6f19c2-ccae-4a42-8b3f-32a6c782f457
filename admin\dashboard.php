<?php
require_once '../config/config.php';
require_admin();

$studentService = new StudentService();
$photoSessionService = new PhotoSessionService();

// Get statistics
$totalRegistered = $studentService->getTotalRegisteredCount();
$totalCompleted = $photoSessionService->getCompletedSessionsCount();
$todayCompleted = $photoSessionService->getTodaySessionsCount();
$pendingCount = $totalRegistered - $totalCompleted;

$sessionStats = $photoSessionService->getSessionStatistics();
?>
<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Admin Dashboard - <?php echo APP_NAME; ?></title>
    <link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/bulma@0.9.4/css/bulma.min.css">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css">
    <script src="https://cdn.jsdelivr.net/npm/chart.js"></script>
    <style>
        .dashboard-header {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
        }
        .stat-card {
            border-left: 4px solid #667eea;
            transition: transform 0.2s;
        }
        .stat-card:hover {
            transform: translateY(-2px);
        }
        .stat-card.completed {
            border-left-color: #48c78e;
        }
        .stat-card.pending {
            border-left-color: #ffdd57;
        }
        .stat-card.today {
            border-left-color: #3273dc;
        }
        .chart-container {
            position: relative;
            height: 300px;
        }
    </style>
</head>
<body>
    <!-- Navigation -->
    <nav class="navbar dashboard-header" role="navigation">
        <div class="navbar-brand">
            <div class="navbar-item">
                <i class="fas fa-shield-alt mr-2"></i>
                <strong>Admin Dashboard</strong>
            </div>
        </div>
        
        <div class="navbar-menu">
            <div class="navbar-start">
                <a class="navbar-item" href="dashboard.php">
                    <i class="fas fa-tachometer-alt mr-2"></i>
                    Dashboard
                </a>
                <a class="navbar-item" href="operators.php">
                    <i class="fas fa-users mr-2"></i>
                    Operators
                </a>
                <a class="navbar-item" href="sessions.php">
                    <i class="fas fa-camera mr-2"></i>
                    Photo Sessions
                </a>
                <a class="navbar-item" href="reports.php">
                    <i class="fas fa-chart-bar mr-2"></i>
                    Reports
                </a>
            </div>
            
            <div class="navbar-end">
                <div class="navbar-item has-dropdown is-hoverable">
                    <a class="navbar-link has-text-white">
                        <i class="fas fa-user-shield mr-2"></i>
                        <?php echo htmlspecialchars($_SESSION['full_name']); ?>
                    </a>
                    <div class="navbar-dropdown">
                        <a class="navbar-item" href="profile.php">
                            <i class="fas fa-user-cog mr-2"></i>
                            Profile
                        </a>
                        <a class="navbar-item" href="settings.php">
                            <i class="fas fa-cog mr-2"></i>
                            Settings
                        </a>
                        <hr class="navbar-divider">
                        <a class="navbar-item" href="../logout.php">
                            <i class="fas fa-sign-out-alt mr-2"></i>
                            Logout
                        </a>
                    </div>
                </div>
            </div>
        </div>
    </nav>

    <div class="container mt-4">
        <!-- Welcome Section -->
        <div class="box">
            <h1 class="title is-4">
                <i class="fas fa-tachometer-alt mr-2"></i>
                Admin Dashboard
            </h1>
            <p class="subtitle is-6">Real-time overview of photo session management</p>
        </div>

        <!-- Statistics Cards -->
        <div class="columns">
            <div class="column">
                <div class="box stat-card">
                    <div class="level">
                        <div class="level-left">
                            <div>
                                <p class="heading">Total Registered</p>
                                <p class="title is-3"><?php echo number_format($totalRegistered); ?></p>
                            </div>
                        </div>
                        <div class="level-right">
                            <i class="fas fa-users fa-2x has-text-info"></i>
                        </div>
                    </div>
                </div>
            </div>
            <div class="column">
                <div class="box stat-card completed">
                    <div class="level">
                        <div class="level-left">
                            <div>
                                <p class="heading">Completed</p>
                                <p class="title is-3"><?php echo number_format($totalCompleted); ?></p>
                            </div>
                        </div>
                        <div class="level-right">
                            <i class="fas fa-check-circle fa-2x has-text-success"></i>
                        </div>
                    </div>
                </div>
            </div>
            <div class="column">
                <div class="box stat-card pending">
                    <div class="level">
                        <div class="level-left">
                            <div>
                                <p class="heading">Pending</p>
                                <p class="title is-3"><?php echo number_format($pendingCount); ?></p>
                            </div>
                        </div>
                        <div class="level-right">
                            <i class="fas fa-clock fa-2x has-text-warning"></i>
                        </div>
                    </div>
                </div>
            </div>
            <div class="column">
                <div class="box stat-card today">
                    <div class="level">
                        <div class="level-left">
                            <div>
                                <p class="heading">Today</p>
                                <p class="title is-3"><?php echo number_format($todayCompleted); ?></p>
                            </div>
                        </div>
                        <div class="level-right">
                            <i class="fas fa-calendar-day fa-2x has-text-primary"></i>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- Progress Overview -->
        <div class="columns">
            <div class="column is-8">
                <div class="box">
                    <h2 class="title is-5">
                        <i class="fas fa-chart-pie mr-2"></i>
                        Completion Progress
                    </h2>
                    <div class="chart-container">
                        <canvas id="progressChart"></canvas>
                    </div>
                </div>
            </div>
            <div class="column is-4">
                <div class="box">
                    <h2 class="title is-5">
                        <i class="fas fa-users-cog mr-2"></i>
                        Today's Operator Performance
                    </h2>
                    <?php if (!empty($sessionStats['operator_stats'])): ?>
                        <?php foreach ($sessionStats['operator_stats'] as $operator): ?>
                            <div class="level is-mobile mb-3">
                                <div class="level-left">
                                    <div>
                                        <p class="is-size-6"><?php echo htmlspecialchars($operator['full_name']); ?></p>
                                    </div>
                                </div>
                                <div class="level-right">
                                    <span class="tag is-info"><?php echo $operator['session_count']; ?></span>
                                </div>
                            </div>
                        <?php endforeach; ?>
                    <?php else: ?>
                        <div class="has-text-centered py-4">
                            <i class="fas fa-inbox fa-2x has-text-grey-light mb-3"></i>
                            <p class="has-text-grey">No sessions recorded today</p>
                        </div>
                    <?php endif; ?>
                </div>
            </div>
        </div>

        <!-- Hourly Activity -->
        <?php if (!empty($sessionStats['hourly_distribution'])): ?>
        <div class="box">
            <h2 class="title is-5">
                <i class="fas fa-chart-line mr-2"></i>
                Today's Hourly Activity
            </h2>
            <div class="chart-container">
                <canvas id="hourlyChart"></canvas>
            </div>
        </div>
        <?php endif; ?>

        <!-- Quick Actions -->
        <div class="box">
            <h2 class="title is-5">
                <i class="fas fa-bolt mr-2"></i>
                Quick Actions
            </h2>
            <div class="buttons">
                <a href="operators.php" class="button is-info">
                    <i class="fas fa-user-plus mr-2"></i>
                    Manage Operators
                </a>
                <a href="sessions.php" class="button is-primary">
                    <i class="fas fa-list mr-2"></i>
                    View All Sessions
                </a>
                <a href="reports.php" class="button is-success">
                    <i class="fas fa-download mr-2"></i>
                    Export Reports
                </a>
                <a href="settings.php" class="button is-light">
                    <i class="fas fa-cog mr-2"></i>
                    System Settings
                </a>
            </div>
        </div>
    </div>

    <script>
        // Progress Chart
        const progressCtx = document.getElementById('progressChart').getContext('2d');
        new Chart(progressCtx, {
            type: 'doughnut',
            data: {
                labels: ['Completed', 'Pending'],
                datasets: [{
                    data: [<?php echo $totalCompleted; ?>, <?php echo $pendingCount; ?>],
                    backgroundColor: ['#48c78e', '#ffdd57'],
                    borderWidth: 0
                }]
            },
            options: {
                responsive: true,
                maintainAspectRatio: false,
                plugins: {
                    legend: {
                        position: 'bottom'
                    }
                }
            }
        });

        <?php if (!empty($sessionStats['hourly_distribution'])): ?>
        // Hourly Chart
        const hourlyCtx = document.getElementById('hourlyChart').getContext('2d');
        const hourlyData = <?php echo json_encode($sessionStats['hourly_distribution']); ?>;
        
        // Create 24-hour array with zeros
        const hours = Array.from({length: 24}, (_, i) => i);
        const sessionCounts = new Array(24).fill(0);
        
        // Fill in actual data
        hourlyData.forEach(item => {
            sessionCounts[item.hour] = item.session_count;
        });
        
        new Chart(hourlyCtx, {
            type: 'bar',
            data: {
                labels: hours.map(h => h + ':00'),
                datasets: [{
                    label: 'Sessions',
                    data: sessionCounts,
                    backgroundColor: '#3273dc',
                    borderColor: '#3273dc',
                    borderWidth: 1
                }]
            },
            options: {
                responsive: true,
                maintainAspectRatio: false,
                scales: {
                    y: {
                        beginAtZero: true,
                        ticks: {
                            stepSize: 1
                        }
                    }
                },
                plugins: {
                    legend: {
                        display: false
                    }
                }
            }
        });
        <?php endif; ?>
    </script>
</body>
</html>
