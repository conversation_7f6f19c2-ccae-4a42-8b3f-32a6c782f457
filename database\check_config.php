<?php
/**
 * Database Configuration Checker
 * Verifies database connection and configuration before migration
 */

// Security check
$allowed_ips = ['127.0.0.1', '::1', 'localhost'];
if (!in_array($_SERVER['REMOTE_ADDR'], $allowed_ips) && !in_array($_SERVER['HTTP_HOST'], ['localhost', '127.0.0.1'])) {
    die('Access denied. Configuration check can only be run from localhost.');
}

// Include configuration
require_once '../config/database.php';

$checks = [];
$overall_status = true;

function add_check($name, $status, $message, $details = '') {
    global $checks, $overall_status;
    $checks[] = [
        'name' => $name,
        'status' => $status,
        'message' => $message,
        'details' => $details
    ];
    if (!$status) {
        $overall_status = false;
    }
}

// Check 1: Database constants
if (defined('DB_HOST') && defined('DB_USER') && defined('DB_PASS') && defined('DB_NAME')) {
    add_check('Database Constants', true, 'All database constants are defined', 
        "Host: " . DB_HOST . ", User: " . DB_USER . ", Database: " . DB_NAME);
} else {
    add_check('Database Constants', false, 'Missing database configuration constants', 
        'Please check config/database.php file');
}

// Check 2: Database connection
try {
    $pdo = new PDO(
        "mysql:host=" . DB_HOST . ";charset=utf8mb4",
        DB_USER,
        DB_PASS,
        [PDO::ATTR_ERRMODE => PDO::ERRMODE_EXCEPTION]
    );
    add_check('Database Connection', true, 'Successfully connected to MySQL server', 
        'Connection established to ' . DB_HOST);
} catch (PDOException $e) {
    add_check('Database Connection', false, 'Failed to connect to database', 
        'Error: ' . $e->getMessage());
}

// Check 3: Database exists
if (isset($pdo)) {
    try {
        $pdo->exec("USE " . DB_NAME);
        add_check('Database Exists', true, 'Target database exists', 
            'Database "' . DB_NAME . '" is accessible');
    } catch (PDOException $e) {
        // Try to create database
        try {
            $pdo->exec("CREATE DATABASE IF NOT EXISTS `" . DB_NAME . "` DEFAULT CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci");
            $pdo->exec("USE " . DB_NAME);
            add_check('Database Creation', true, 'Database created successfully', 
                'Created database "' . DB_NAME . '" with UTF8MB4 charset');
        } catch (PDOException $e2) {
            add_check('Database Creation', false, 'Cannot create database', 
                'Error: ' . $e2->getMessage());
        }
    }
}

// Check 4: Check existing tables
if (isset($pdo)) {
    try {
        $stmt = $pdo->query("SHOW TABLES");
        $tables = $stmt->fetchAll(PDO::FETCH_COLUMN);
        
        if (empty($tables)) {
            add_check('Existing Tables', true, 'No existing tables found', 
                'Clean database - ready for fresh installation');
        } else {
            $important_tables = ['users', 'photo_sessions', 'session_logs'];
            $found_tables = array_intersect($important_tables, $tables);
            
            if (!empty($found_tables)) {
                add_check('Existing Data', true, 'Found existing data to migrate', 
                    'Tables: ' . implode(', ', $found_tables));
            } else {
                add_check('Existing Tables', true, 'Found ' . count($tables) . ' existing tables', 
                    'Tables: ' . implode(', ', array_slice($tables, 0, 5)) . (count($tables) > 5 ? '...' : ''));
            }
        }
    } catch (PDOException $e) {
        add_check('Table Check', false, 'Cannot check existing tables', 
            'Error: ' . $e->getMessage());
    }
}

// Check 5: MySQL version
if (isset($pdo)) {
    try {
        $stmt = $pdo->query("SELECT VERSION() as version");
        $version = $stmt->fetch()['version'];
        $version_number = floatval($version);
        
        if ($version_number >= 5.7) {
            add_check('MySQL Version', true, 'MySQL version is compatible', 
                'Version: ' . $version . ' (>= 5.7 required)');
        } else {
            add_check('MySQL Version', false, 'MySQL version may be too old', 
                'Version: ' . $version . ' (5.7+ recommended)');
        }
    } catch (PDOException $e) {
        add_check('MySQL Version', false, 'Cannot check MySQL version', 
            'Error: ' . $e->getMessage());
    }
}

// Check 6: Required PHP extensions
$required_extensions = ['pdo', 'pdo_mysql', 'mbstring', 'json'];
foreach ($required_extensions as $ext) {
    if (extension_loaded($ext)) {
        add_check("PHP Extension: {$ext}", true, "Extension {$ext} is loaded", '');
    } else {
        add_check("PHP Extension: {$ext}", false, "Extension {$ext} is missing", 
            "Please install php-{$ext} extension");
    }
}

// Check 7: File permissions
$migration_file = __DIR__ . '/migrate_to_unified.sql';
if (file_exists($migration_file) && is_readable($migration_file)) {
    add_check('Migration File', true, 'Migration SQL file is accessible', 
        'File: ' . basename($migration_file) . ' (' . number_format(filesize($migration_file)) . ' bytes)');
} else {
    add_check('Migration File', false, 'Migration SQL file not found or not readable', 
        'Expected: ' . $migration_file);
}
?>
<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Database Configuration Check - Photo Management System</title>
    <link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/bulma@0.9.4/css/bulma.min.css">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css">
    <style>
        .config-header {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            padding: 2rem;
            border-radius: 8px;
            margin-bottom: 2rem;
        }
        .check-item {
            border-left: 4px solid #dbdbdb;
            padding: 1rem;
            margin-bottom: 0.5rem;
            border-radius: 0 4px 4px 0;
        }
        .check-item.success {
            border-left-color: #48c78e;
            background-color: #f0fff4;
        }
        .check-item.error {
            border-left-color: #f14668;
            background-color: #ffeaea;
        }
    </style>
</head>
<body>
    <div class="container mt-4">
        <!-- Header -->
        <div class="config-header">
            <h1 class="title is-2 has-text-white">
                <i class="fas fa-cog mr-3"></i>
                Database Configuration Check
            </h1>
            <p class="subtitle is-4 has-text-white">
                Verify system requirements before migration
            </p>
        </div>

        <!-- Overall Status -->
        <div class="notification <?php echo $overall_status ? 'is-success' : 'is-danger'; ?>">
            <h2 class="title is-5">
                <i class="fas fa-<?php echo $overall_status ? 'check-circle' : 'exclamation-triangle'; ?> mr-2"></i>
                Overall Status: <?php echo $overall_status ? 'Ready for Migration' : 'Issues Found'; ?>
            </h2>
            <?php if ($overall_status): ?>
                <p>All configuration checks passed. You can proceed with the database migration.</p>
            <?php else: ?>
                <p>Some configuration issues were found. Please resolve them before proceeding with migration.</p>
            <?php endif; ?>
        </div>

        <!-- Configuration Checks -->
        <div class="box">
            <h2 class="title is-5">
                <i class="fas fa-list-check mr-2"></i>
                Configuration Checks
            </h2>
            
            <?php foreach ($checks as $check): ?>
                <div class="check-item <?php echo $check['status'] ? 'success' : 'error'; ?>">
                    <div class="level">
                        <div class="level-left">
                            <div class="level-item">
                                <i class="fas fa-<?php echo $check['status'] ? 'check-circle has-text-success' : 'times-circle has-text-danger'; ?> mr-3"></i>
                                <div>
                                    <h3 class="title is-6 mb-1"><?php echo htmlspecialchars($check['name']); ?></h3>
                                    <p class="subtitle is-7 mb-1"><?php echo htmlspecialchars($check['message']); ?></p>
                                    <?php if ($check['details']): ?>
                                        <p class="is-size-7 has-text-grey"><?php echo htmlspecialchars($check['details']); ?></p>
                                    <?php endif; ?>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            <?php endforeach; ?>
        </div>

        <!-- Next Steps -->
        <div class="box">
            <h2 class="title is-5">
                <i class="fas fa-arrow-right mr-2"></i>
                Next Steps
            </h2>
            
            <?php if ($overall_status): ?>
                <div class="content">
                    <p>Your system is ready for migration. You can now:</p>
                    <ol>
                        <li>Proceed with the database migration</li>
                        <li>The migration will automatically backup existing data</li>
                        <li>New unified database structure will be created</li>
                        <li>Default data will be inserted</li>
                    </ol>
                </div>
                
                <div class="buttons">
                    <a href="migrate.php" class="button is-primary is-large">
                        <i class="fas fa-database mr-2"></i>
                        Start Migration
                    </a>
                    <button class="button is-light" onclick="location.reload()">
                        <i class="fas fa-refresh mr-2"></i>
                        Recheck Configuration
                    </button>
                </div>
            <?php else: ?>
                <div class="content">
                    <p>Please resolve the following issues before proceeding:</p>
                    <ul>
                        <?php foreach ($checks as $check): ?>
                            <?php if (!$check['status']): ?>
                                <li><strong><?php echo htmlspecialchars($check['name']); ?>:</strong> <?php echo htmlspecialchars($check['message']); ?></li>
                            <?php endif; ?>
                        <?php endforeach; ?>
                    </ul>
                </div>
                
                <div class="buttons">
                    <button class="button is-warning" onclick="location.reload()">
                        <i class="fas fa-refresh mr-2"></i>
                        Recheck Configuration
                    </button>
                </div>
            <?php endif; ?>
        </div>

        <!-- Footer -->
        <div class="has-text-centered mt-6 mb-4">
            <p class="has-text-grey">
                <i class="fas fa-shield-alt mr-2"></i>
                Photo Management System - Configuration Checker
            </p>
        </div>
    </div>
</body>
</html>
