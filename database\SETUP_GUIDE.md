# Database Setup Guide

## 🚨 Connection Error Fix

If you're getting the error:
```
SQLSTATE[HY000] [2002] php_network_getaddresses: getaddrinfo failed: No such host is known.
```

This means the database server cannot be reached. Here's how to fix it:

## ✅ Step-by-Step Solution

### 1. **Start Your Database Server**

#### For XAMPP Users:
1. Open XAMPP Control Panel
2. Click **"Start"** next to **MySQL**
3. Wait for it to show **"Running"** status
4. The port should show **3306**

#### For WAMP Users:
1. Start WAMP server
2. Ensure MySQL service is running (green icon)
3. Check that port 3306 is available

#### For MAMP Users:
1. Start MAMP
2. Ensure MySQL is running
3. Note the port (usually 3306 or 8889)

### 2. **Test Database Connection**

Visit: `http://localhost/your-project/database/test_connection.php`

This will test your connection step by step and show exactly what's wrong.

### 3. **Update Database Configuration**

Edit `config/database.php` and verify these settings:

```php
// For XAMPP (default):
define('DB_HOST', 'localhost');
define('DB_USER', 'root');
define('DB_PASS', '');
define('DB_NAME', 'photo_management_system');

// For MAMP (if using port 8889):
define('DB_HOST', 'localhost:8889');
define('DB_USER', 'root');
define('DB_PASS', 'root');
define('DB_NAME', 'photo_management_system');
```

### 4. **Common Host Variations to Try**

If `localhost` doesn't work, try these alternatives in `DB_HOST`:

```php
define('DB_HOST', '127.0.0.1');        // IP address
define('DB_HOST', 'localhost:3306');   // With port
define('DB_HOST', '127.0.0.1:3306');   // IP with port
```

### 5. **Create Database Manually (Optional)**

If you want to create the database before migration:

1. Open **phpMyAdmin** (usually `http://localhost/phpmyadmin`)
2. Click **"New"** in the left sidebar
3. Database name: `photo_management_system`
4. Collation: `utf8mb4_unicode_ci`
5. Click **"Create"**

## 🔧 Troubleshooting Common Issues

### Issue 1: "Access denied for user 'root'@'localhost'"
**Solution:** Check username and password in `config/database.php`

For XAMPP: Usually `root` with empty password
For MAMP: Usually `root` with password `root`

### Issue 2: "Unknown MySQL server host"
**Solution:** 
- Ensure MySQL is running
- Try `127.0.0.1` instead of `localhost`
- Check if you need to specify a port

### Issue 3: "Connection refused"
**Solution:**
- Start your web server (Apache/Nginx)
- Start MySQL service
- Check firewall settings

### Issue 4: "No such host is known"
**Solution:**
- This is the error you're seeing
- MySQL server is not running or not accessible
- Start XAMPP/WAMP/MAMP MySQL service

## 📋 Quick Checklist

Before running the migration, ensure:

- [ ] XAMPP/WAMP/MAMP is running
- [ ] MySQL service is started (green/running status)
- [ ] Port 3306 is available
- [ ] `config/database.php` has correct settings
- [ ] You can access `http://localhost/phpmyadmin`
- [ ] Connection test passes: `database/test_connection.php`

## 🚀 Next Steps

Once your connection is working:

1. **Configuration Check:** `http://localhost/your-project/database/check_config.php`
2. **Run Migration:** `http://localhost/your-project/database/migrate.php`

## 💡 Pro Tips

### For Development:
- Use XAMPP for simplicity (comes with everything pre-configured)
- Default XAMPP settings usually work out of the box
- Keep MySQL running while developing

### For Production:
- Use proper database credentials
- Enable SSL connections
- Set up proper user permissions
- Regular backups

## 🆘 Still Having Issues?

If you're still having connection problems:

1. **Check MySQL Status:**
   ```bash
   # Windows (in XAMPP)
   netstat -an | find "3306"
   
   # Should show: TCP 0.0.0.0:3306 LISTENING
   ```

2. **Test with Command Line:**
   ```bash
   mysql -u root -p -h localhost
   ```

3. **Check Error Logs:**
   - XAMPP: `xampp/mysql/data/mysql_error.log`
   - Check PHP error logs for more details

4. **Restart Services:**
   - Stop and start MySQL in XAMPP/WAMP
   - Restart your web server
   - Clear browser cache

## 📞 Getting Help

If none of these solutions work:

1. Check the exact error message in `database/test_connection.php`
2. Verify your XAMPP/WAMP installation
3. Try creating a simple PHP file to test basic MySQL connection
4. Check if other PHP applications can connect to your database

Remember: The most common cause is simply that MySQL is not running in XAMPP/WAMP!
