<?php
/**
 * Operator Service Class
 * Handles operator management operations
 */

class OperatorService {
    private $db;
    
    public function __construct() {
        $this->db = DatabaseConfig::getLocalConnection();
    }
    
    /**
     * Get all operators
     */
    public function getAllOperators($includeInactive = false) {
        try {
            $whereClause = $includeInactive ? "" : "WHERE is_active = 1";
            
            $stmt = $this->db->prepare("
                SELECT 
                    o.id,
                    o.username,
                    o.full_name,
                    o.role,
                    o.is_active,
                    o.created_at,
                    o.last_login,
                    creator.full_name as created_by_name,
                    (SELECT COUNT(*) FROM photo_sessions ps WHERE ps.operator_id = o.id) as total_sessions,
                    (SELECT COUNT(*) FROM photo_sessions ps WHERE ps.operator_id = o.id AND DATE(ps.completed_at) = CURDATE()) as today_sessions
                FROM operators o
                LEFT JOIN operators creator ON o.created_by = creator.id
                {$whereClause}
                ORDER BY o.role DESC, o.full_name ASC
            ");
            $stmt->execute();
            
            return $stmt->fetchAll();
        } catch (Exception $e) {
            error_log("Error getting operators: " . $e->getMessage());
            return [];
        }
    }
    
    /**
     * Get operator by ID
     */
    public function getOperatorById($id) {
        try {
            $stmt = $this->db->prepare("
                SELECT 
                    o.*,
                    creator.full_name as created_by_name
                FROM operators o
                LEFT JOIN operators creator ON o.created_by = creator.id
                WHERE o.id = ?
            ");
            $stmt->execute([$id]);
            
            return $stmt->fetch();
        } catch (Exception $e) {
            error_log("Error getting operator: " . $e->getMessage());
            return false;
        }
    }
    
    /**
     * Create new operator
     */
    public function createOperator($data) {
        try {
            // Validate required fields
            $required_fields = ['username', 'password', 'full_name', 'role'];
            foreach ($required_fields as $field) {
                if (empty($data[$field])) {
                    return [
                        'success' => false,
                        'message' => "Missing required field: {$field}"
                    ];
                }
            }
            
            // Validate username uniqueness
            if ($this->usernameExists($data['username'])) {
                return [
                    'success' => false,
                    'message' => 'Username already exists'
                ];
            }
            
            // Validate role
            if (!in_array($data['role'], ['admin', 'operator'])) {
                return [
                    'success' => false,
                    'message' => 'Invalid role specified'
                ];
            }
            
            // Validate password strength
            if (strlen($data['password']) < 6) {
                return [
                    'success' => false,
                    'message' => 'Password must be at least 6 characters long'
                ];
            }
            
            // Hash password
            $password_hash = password_hash($data['password'], PASSWORD_DEFAULT);
            
            // Insert operator
            $stmt = $this->db->prepare("
                INSERT INTO operators (username, password_hash, full_name, role, is_active, created_by)
                VALUES (?, ?, ?, ?, ?, ?)
            ");
            
            $stmt->execute([
                $data['username'],
                $password_hash,
                $data['full_name'],
                $data['role'],
                $data['is_active'] ?? true,
                $_SESSION['user_id']
            ]);
            
            $operator_id = $this->db->lastInsertId();
            
            // Log activity
            log_activity(
                'OPERATOR_CREATED',
                "Created operator: {$data['username']} ({$data['full_name']})"
            );
            
            return [
                'success' => true,
                'message' => 'Operator created successfully',
                'operator_id' => $operator_id
            ];
            
        } catch (Exception $e) {
            error_log("Error creating operator: " . $e->getMessage());
            return [
                'success' => false,
                'message' => 'Error creating operator'
            ];
        }
    }
    
    /**
     * Update operator
     */
    public function updateOperator($id, $data) {
        try {
            // Get current operator data
            $current = $this->getOperatorById($id);
            if (!$current) {
                return [
                    'success' => false,
                    'message' => 'Operator not found'
                ];
            }
            
            // Prevent self-deactivation for admins
            if ($id == $_SESSION['user_id'] && isset($data['is_active']) && !$data['is_active']) {
                return [
                    'success' => false,
                    'message' => 'You cannot deactivate your own account'
                ];
            }
            
            // Build update query dynamically
            $update_fields = [];
            $params = [];
            
            if (isset($data['full_name']) && !empty($data['full_name'])) {
                $update_fields[] = "full_name = ?";
                $params[] = $data['full_name'];
            }
            
            if (isset($data['role']) && in_array($data['role'], ['admin', 'operator'])) {
                $update_fields[] = "role = ?";
                $params[] = $data['role'];
            }
            
            if (isset($data['is_active'])) {
                $update_fields[] = "is_active = ?";
                $params[] = $data['is_active'] ? 1 : 0;
            }
            
            if (isset($data['password']) && !empty($data['password'])) {
                if (strlen($data['password']) < 6) {
                    return [
                        'success' => false,
                        'message' => 'Password must be at least 6 characters long'
                    ];
                }
                $update_fields[] = "password_hash = ?";
                $params[] = password_hash($data['password'], PASSWORD_DEFAULT);
            }
            
            if (empty($update_fields)) {
                return [
                    'success' => false,
                    'message' => 'No valid fields to update'
                ];
            }
            
            $update_fields[] = "updated_at = NOW()";
            $params[] = $id;
            
            $sql = "UPDATE operators SET " . implode(", ", $update_fields) . " WHERE id = ?";
            $stmt = $this->db->prepare($sql);
            $stmt->execute($params);
            
            // Log activity
            log_activity(
                'OPERATOR_UPDATED',
                "Updated operator: {$current['username']} ({$current['full_name']})"
            );
            
            return [
                'success' => true,
                'message' => 'Operator updated successfully'
            ];
            
        } catch (Exception $e) {
            error_log("Error updating operator: " . $e->getMessage());
            return [
                'success' => false,
                'message' => 'Error updating operator'
            ];
        }
    }
    
    /**
     * Delete operator (soft delete by deactivating)
     */
    public function deleteOperator($id) {
        try {
            // Prevent self-deletion
            if ($id == $_SESSION['user_id']) {
                return [
                    'success' => false,
                    'message' => 'You cannot delete your own account'
                ];
            }
            
            // Get operator info for logging
            $operator = $this->getOperatorById($id);
            if (!$operator) {
                return [
                    'success' => false,
                    'message' => 'Operator not found'
                ];
            }
            
            // Soft delete by deactivating
            $stmt = $this->db->prepare("UPDATE operators SET is_active = 0 WHERE id = ?");
            $stmt->execute([$id]);
            
            // Log activity
            log_activity(
                'OPERATOR_DELETED',
                "Deactivated operator: {$operator['username']} ({$operator['full_name']})"
            );
            
            return [
                'success' => true,
                'message' => 'Operator deactivated successfully'
            ];
            
        } catch (Exception $e) {
            error_log("Error deleting operator: " . $e->getMessage());
            return [
                'success' => false,
                'message' => 'Error deleting operator'
            ];
        }
    }
    
    /**
     * Check if username exists
     */
    private function usernameExists($username, $excludeId = null) {
        $sql = "SELECT id FROM operators WHERE username = ?";
        $params = [$username];
        
        if ($excludeId) {
            $sql .= " AND id != ?";
            $params[] = $excludeId;
        }
        
        $stmt = $this->db->prepare($sql);
        $stmt->execute($params);
        
        return $stmt->fetch() !== false;
    }
    
    /**
     * Get operator statistics
     */
    public function getOperatorStatistics() {
        try {
            $stats = [];
            
            // Total operators
            $stmt = $this->db->prepare("SELECT COUNT(*) as total FROM operators WHERE is_active = 1");
            $stmt->execute();
            $stats['total_operators'] = $stmt->fetch()['total'];
            
            // Active operators (logged in within last 24 hours)
            $stmt = $this->db->prepare("
                SELECT COUNT(*) as active 
                FROM operators 
                WHERE is_active = 1 AND last_login >= DATE_SUB(NOW(), INTERVAL 24 HOUR)
            ");
            $stmt->execute();
            $stats['active_operators'] = $stmt->fetch()['active'];
            
            // Operators by role
            $stmt = $this->db->prepare("
                SELECT role, COUNT(*) as count 
                FROM operators 
                WHERE is_active = 1 
                GROUP BY role
            ");
            $stmt->execute();
            $role_stats = $stmt->fetchAll();
            
            $stats['admins'] = 0;
            $stats['operators'] = 0;
            
            foreach ($role_stats as $role_stat) {
                if ($role_stat['role'] === 'admin') {
                    $stats['admins'] = $role_stat['count'];
                } else {
                    $stats['operators'] = $role_stat['count'];
                }
            }
            
            return $stats;
        } catch (Exception $e) {
            error_log("Error getting operator statistics: " . $e->getMessage());
            return [];
        }
    }
}
?>
