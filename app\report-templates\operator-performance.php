<?php if (!isset($reportData)) exit; ?>

<!-- Operator Performance Table -->
<div class="box">
    <h2 class="title is-5">
        <i class="fas fa-users mr-2"></i>
        Operator Performance Overview
    </h2>
    
    <?php if (empty($reportData['operators'])): ?>
        <div class="has-text-centered py-6">
            <i class="fas fa-users fa-3x has-text-grey-light mb-4"></i>
            <p class="title is-5 has-text-grey">No operators found</p>
        </div>
    <?php else: ?>
        <div class="table-container">
            <table class="table is-fullwidth is-striped">
                <thead>
                    <tr>
                        <th>Operator Name</th>
                        <th>Username</th>
                        <th>Total Sessions</th>
                        <th>Today's Sessions</th>
                        <th>Status</th>
                        <th>Last Login</th>
                        <th>Performance</th>
                    </tr>
                </thead>
                <tbody>
                    <?php 
                    // Sort by total sessions descending
                    usort($reportData['operators'], function($a, $b) {
                        return $b['total_sessions'] - $a['total_sessions'];
                    });
                    
                    foreach ($reportData['operators'] as $operator): 
                        $performanceClass = '';
                        $performanceText = '';
                        
                        if ($operator['total_sessions'] >= 100) {
                            $performanceClass = 'is-success';
                            $performanceText = 'Excellent';
                        } elseif ($operator['total_sessions'] >= 50) {
                            $performanceClass = 'is-info';
                            $performanceText = 'Good';
                        } elseif ($operator['total_sessions'] >= 20) {
                            $performanceClass = 'is-warning';
                            $performanceText = 'Average';
                        } else {
                            $performanceClass = 'is-light';
                            $performanceText = 'New';
                        }
                    ?>
                        <tr>
                            <td><strong><?php echo htmlspecialchars($operator['name']); ?></strong></td>
                            <td><?php echo htmlspecialchars($operator['username']); ?></td>
                            <td>
                                <span class="tag is-primary is-medium">
                                    <?php echo number_format($operator['total_sessions']); ?>
                                </span>
                            </td>
                            <td>
                                <span class="tag is-info">
                                    <?php echo number_format($operator['today_sessions']); ?>
                                </span>
                            </td>
                            <td>
                                <span class="tag <?php echo $operator['status'] === 'Active' ? 'is-success' : 'is-danger'; ?>">
                                    <?php echo htmlspecialchars($operator['status']); ?>
                                </span>
                            </td>
                            <td>
                                <?php if ($operator['last_login']): ?>
                                    <?php echo date('M j, Y H:i', strtotime($operator['last_login'])); ?>
                                <?php else: ?>
                                    <span class="has-text-grey">Never</span>
                                <?php endif; ?>
                            </td>
                            <td>
                                <span class="tag <?php echo $performanceClass; ?>">
                                    <?php echo $performanceText; ?>
                                </span>
                            </td>
                        </tr>
                    <?php endforeach; ?>
                </tbody>
            </table>
        </div>
        
        <!-- Performance Summary -->
        <div class="columns mt-4">
            <div class="column">
                <div class="box has-background-success-light">
                    <h3 class="title is-6 has-text-success">Top Performer</h3>
                    <?php 
                    $topPerformer = $reportData['operators'][0] ?? null;
                    if ($topPerformer): 
                    ?>
                        <p><strong><?php echo htmlspecialchars($topPerformer['name']); ?></strong></p>
                        <p><?php echo number_format($topPerformer['total_sessions']); ?> total sessions</p>
                    <?php else: ?>
                        <p>No data available</p>
                    <?php endif; ?>
                </div>
            </div>
            <div class="column">
                <div class="box has-background-info-light">
                    <h3 class="title is-6 has-text-info">Most Active Today</h3>
                    <?php 
                    $mostActiveToday = null;
                    $maxTodaySessions = 0;
                    
                    foreach ($reportData['operators'] as $operator) {
                        if ($operator['today_sessions'] > $maxTodaySessions) {
                            $maxTodaySessions = $operator['today_sessions'];
                            $mostActiveToday = $operator;
                        }
                    }
                    
                    if ($mostActiveToday && $maxTodaySessions > 0): 
                    ?>
                        <p><strong><?php echo htmlspecialchars($mostActiveToday['name']); ?></strong></p>
                        <p><?php echo number_format($mostActiveToday['today_sessions']); ?> sessions today</p>
                    <?php else: ?>
                        <p>No sessions recorded today</p>
                    <?php endif; ?>
                </div>
            </div>
            <div class="column">
                <div class="box has-background-warning-light">
                    <h3 class="title is-6 has-text-warning">Total Active Operators</h3>
                    <?php 
                    $activeCount = count(array_filter($reportData['operators'], function($op) {
                        return $op['status'] === 'Active';
                    }));
                    ?>
                    <p><strong><?php echo $activeCount; ?></strong> out of <?php echo count($reportData['operators']); ?></p>
                    <p><?php echo round(($activeCount / count($reportData['operators'])) * 100, 1); ?>% active</p>
                </div>
            </div>
        </div>
    <?php endif; ?>
</div>
