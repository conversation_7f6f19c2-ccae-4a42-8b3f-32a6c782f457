<?php
// Authentication check - must be first
require_once 'auth-check.php';
require_admin();

$message = '';
$message_type = '';

// Initialize services
$studentService = new StudentService();
$eventService = new EventService();

// Handle form submissions
if ($_SERVER['REQUEST_METHOD'] === 'POST') {
    if (!verify_csrf_token($_POST['csrf_token'] ?? '')) {
        $message = 'Invalid security token';
        $message_type = 'is-danger';
    } else {
        $action = $_POST['action'] ?? '';
        
        if ($action === 'update_status') {
            $student_id = (int)($_POST['student_id'] ?? 0);
            $new_status = sanitize_input($_POST['new_status'] ?? '');
            
            if ($student_id && $new_status) {
                if ($studentService->updateStudentStatus($student_id, $new_status)) {
                    $message = 'Student status updated successfully';
                    $message_type = 'is-success';
                    log_activity('update_student_status', "Updated student ID {$student_id} to {$new_status}");
                } else {
                    $message = 'Error updating student status';
                    $message_type = 'is-danger';
                }
            }
        }
    }
}

// Get filter parameters
$filters = [
    'event_id' => (int)($_GET['event_id'] ?? 0),
    'registration_status' => sanitize_input($_GET['status'] ?? ''),
    'payment_status' => sanitize_input($_GET['payment'] ?? ''),
    'search' => sanitize_input($_GET['search'] ?? '')
];

// Remove empty filters
$filters = array_filter($filters);

// Get students
$students = $studentService->getStudents($filters, 50, 0);
$events = $eventService->getAllEvents();
$stats = $studentService->getStudentStatistics();

// Status options
$status_options = [
    'registered' => 'Registered',
    'photo_taken' => 'Photo Taken',
    'in_development' => 'In Development',
    'dispatched' => 'Dispatched',
    'ready_for_pickup' => 'Ready for Pickup',
    'completed' => 'Completed'
];

$payment_options = [
    'pending' => 'Pending',
    'paid' => 'Paid',
    'failed' => 'Failed',
    'refunded' => 'Refunded'
];
?>
<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Student Management - <?php echo APP_NAME; ?></title>
    <link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/bulma@0.9.4/css/bulma.min.css">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css">
    <style>
        .dashboard-header {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
        }
        .navbar-item.has-text-white:hover {
            background-color: rgba(255,255,255,0.1);
            color: white !important;
        }
        .navbar-item.is-active {
            background-color: rgba(255,255,255,0.2);
            font-weight: bold;
        }
        .navbar-dropdown {
            background-color: white;
            border: 1px solid #dbdbdb;
            box-shadow: 0 8px 16px rgba(10,10,10,.1);
        }
        .navbar-dropdown .navbar-item {
            color: #363636 !important;
            padding: 0.75rem 1rem;
        }
        .navbar-dropdown .navbar-item:hover {
            background-color: #f5f5f5;
            color: #363636 !important;
        }
        .navbar-dropdown .navbar-divider {
            background-color: #dbdbdb;
        }
        .navbar-brand .navbar-item {
            color: white !important;
            font-weight: bold;
        }
        .navbar-item.has-dropdown .navbar-link {
            color: white !important;
        }
        .navbar-item.has-dropdown .navbar-link:hover {
            background-color: rgba(255,255,255,0.05) !important;
            color: white !important;
        }
        .navbar-item.has-dropdown.is-active .navbar-link,
        .navbar-item.has-dropdown:hover .navbar-link {
            background-color: rgba(255,255,255,0.05) !important;
            color: white !important;
        }
        .navbar-link::after {
            border-color: white !important;
        }
        .stat-card {
            border-left: 4px solid #667eea;
            transition: transform 0.2s;
        }
        .stat-card:hover {
            transform: translateY(-2px);
        }
        .stat-card.success {
            border-left-color: #48c78e;
        }
        .stat-card.warning {
            border-left-color: #ffdd57;
        }
        .stat-card.info {
            border-left-color: #3273dc;
        }
        .stat-card .heading {
            color: #7a7a7a;
            font-weight: 600;
            text-transform: uppercase;
            font-size: 0.75rem;
        }
        .stat-card .title {
            color: #363636;
            font-weight: 700;
        }

        /* Mobile responsiveness for admin pages */
        @media screen and (max-width: 1024px) {
            .navbar-menu {
                background-color: #667eea;
            }

            .container {
                padding: 0 1rem;
            }
        }

        @media screen and (max-width: 768px) {
            .title.is-4 {
                font-size: 1.5rem;
            }

            .title.is-5 {
                font-size: 1.25rem;
            }

            .subtitle.is-6 {
                font-size: 1rem;
            }

            .box {
                padding: 1rem;
            }

            .stat-card {
                margin-bottom: 1rem;
            }

            .columns .column {
                padding: 0.5rem;
            }

            .table-container {
                overflow-x: auto;
            }

            .table {
                font-size: 0.875rem;
            }

            .table th,
            .table td {
                padding: 0.5rem;
            }

            .buttons .button {
                margin-bottom: 0.5rem;
            }

            .field.is-grouped {
                flex-direction: column;
            }

            .field.is-grouped .field {
                margin-bottom: 0.5rem;
            }
        }

        @media screen and (max-width: 480px) {
            .container {
                padding: 0 0.5rem;
            }

            .title.is-4 {
                font-size: 1.25rem;
            }

            .box {
                padding: 0.75rem;
            }

            .stat-card .title.is-3 {
                font-size: 1.5rem;
            }

            .buttons {
                flex-direction: column;
            }

            .buttons .button {
                width: 100%;
                justify-content: flex-start;
            }

            .table {
                font-size: 0.8rem;
            }

            .table th,
            .table td {
                padding: 0.25rem;
            }

            .tag {
                font-size: 0.7rem;
            }
        }
    </style>
</head>
<body>
    <!-- Navigation -->
    <nav class="navbar dashboard-header" role="navigation">
        <div class="navbar-brand">
            <div class="navbar-item" style="border-right: 1px solid #ccc; padding-right: 1rem; margin-right: 1rem;">
                <i class="fas fa-shield-alt mr-2" style="color: #ffffffff;"></i>
                <span style="font-weight: bold; color: #ffffffff;">Admin Dashboard</span>
            </div>

            <a role="button" class="navbar-burger has-text-white" data-target="navbarMenu">
                <span></span>
                <span></span>
                <span></span>
            </a>
        </div>

        <div class="navbar-menu" id="navbarMenu">
            <div class="navbar-start">
                <a class="navbar-item has-text-white" href="dashboard.php">
                    <i class="fas fa-tachometer-alt mr-2"></i>
                    Dashboard
                </a>
                <a class="navbar-item has-text-white" href="operators.php">
                    <i class="fas fa-users mr-2"></i>
                    Operators
                </a>
                <a class="navbar-item has-text-white" href="sessions.php">
                    <i class="fas fa-camera mr-2"></i>
                    Photo Sessions
                </a>
                <a class="navbar-item has-text-white" href="event-sessions.php">
                    <i class="fas fa-calendar-alt mr-2"></i>
                    Event Sessions
                </a>
                <a class="navbar-item has-text-white is-active" href="students.php">
                    <i class="fas fa-graduation-cap mr-2"></i>
                    Students
                </a>
                <a class="navbar-item has-text-white" href="photo-pricing.php">
                    <i class="fas fa-tags mr-2"></i>
                    Photo Pricing
                </a>
                <a class="navbar-item has-text-white" href="reports.php">
                    <i class="fas fa-chart-bar mr-2"></i>
                    Reports
                </a>
            </div>
            
            <div class="navbar-end">
                <div class="navbar-item has-dropdown is-hoverable">
                    <a class="navbar-link has-text-white">
                        <i class="fas fa-user-shield mr-2"></i>
                        <?php echo htmlspecialchars($_SESSION['full_name']); ?>
                    </a>
                    <div class="navbar-dropdown">
                        <a class="navbar-item" href="profile.php">
                            <i class="fas fa-user-cog mr-2"></i>
                            Profile
                        </a>
                        <a class="navbar-item" href="settings.php">
                            <i class="fas fa-cog mr-2"></i>
                            Settings
                        </a>
                        <hr class="navbar-divider">
                        <a class="navbar-item" href="../logout.php">
                            <i class="fas fa-sign-out-alt mr-2"></i>
                            Logout
                        </a>
                    </div>
                </div>
            </div>
        </div>
    </nav>

    <div class="container mt-4">
        <!-- Header -->
        <div class="box">
            <h1 class="title is-4">
                <i class="fas fa-graduation-cap mr-2"></i>
                Student Management
            </h1>
            <p class="subtitle is-6">Manage student registrations and track progress</p>
        </div>

        <!-- Messages -->
        <?php if ($message): ?>
            <div class="notification <?php echo $message_type; ?>">
                <button class="delete" onclick="this.parentElement.style.display='none'"></button>
                <?php echo htmlspecialchars($message); ?>
            </div>
        <?php endif; ?>

        <!-- Statistics -->
        <div class="columns">
            <div class="column">
                <div class="box stat-card">
                    <div class="level">
                        <div class="level-left">
                            <div>
                                <p class="heading">Total Registered</p>
                                <p class="title is-3"><?php echo number_format($stats['total_registered'] ?? 0); ?></p>
                            </div>
                        </div>
                        <div class="level-right">
                            <i class="fas fa-users fa-2x has-text-info"></i>
                        </div>
                    </div>
                </div>
            </div>
            <div class="column">
                <div class="box stat-card success">
                    <div class="level">
                        <div class="level-left">
                            <div>
                                <p class="heading">Completed</p>
                                <p class="title is-3"><?php echo number_format($stats['by_status']['completed'] ?? 0); ?></p>
                            </div>
                        </div>
                        <div class="level-right">
                            <i class="fas fa-check-circle fa-2x has-text-success"></i>
                        </div>
                    </div>
                </div>
            </div>
            <div class="column">
                <div class="box stat-card warning">
                    <div class="level">
                        <div class="level-left">
                            <div>
                                <p class="heading">Pending Payment</p>
                                <p class="title is-3"><?php echo number_format($stats['by_payment']['pending'] ?? 0); ?></p>
                            </div>
                        </div>
                        <div class="level-right">
                            <i class="fas fa-clock fa-2x has-text-warning"></i>
                        </div>
                    </div>
                </div>
            </div>
            <div class="column">
                <div class="box stat-card info">
                    <div class="level">
                        <div class="level-left">
                            <div>
                                <p class="heading">Total Revenue</p>
                                <p class="title is-3">LKR <?php echo number_format($stats['total_revenue'] ?? 0, 2); ?></p>
                            </div>
                        </div>
                        <div class="level-right">
                            <i class="fas fa-money-bill-wave fa-2x has-text-primary"></i>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- Filters -->
        <div class="box">
            <h2 class="title is-5">
                <i class="fas fa-filter mr-2"></i>
                Filters
            </h2>
            
            <form method="GET" action="">
                <div class="columns">
                    <div class="column is-3">
                        <div class="field">
                            <label class="label is-small">Event</label>
                            <div class="control">
                                <div class="select is-small is-fullwidth">
                                    <select name="event_id">
                                        <option value="">All Events</option>
                                        <?php foreach ($events as $event): ?>
                                            <option value="<?php echo $event['id']; ?>" 
                                                    <?php echo (($_GET['event_id'] ?? '') == $event['id']) ? 'selected' : ''; ?>>
                                                <?php echo htmlspecialchars($event['event_name']); ?>
                                            </option>
                                        <?php endforeach; ?>
                                    </select>
                                </div>
                            </div>
                        </div>
                    </div>
                    <div class="column is-3">
                        <div class="field">
                            <label class="label is-small">Registration Status</label>
                            <div class="control">
                                <div class="select is-small is-fullwidth">
                                    <select name="status">
                                        <option value="">All Statuses</option>
                                        <?php foreach ($status_options as $value => $label): ?>
                                            <option value="<?php echo $value; ?>" 
                                                    <?php echo (($_GET['status'] ?? '') == $value) ? 'selected' : ''; ?>>
                                                <?php echo $label; ?>
                                            </option>
                                        <?php endforeach; ?>
                                    </select>
                                </div>
                            </div>
                        </div>
                    </div>
                    <div class="column is-3">
                        <div class="field">
                            <label class="label is-small">Payment Status</label>
                            <div class="control">
                                <div class="select is-small is-fullwidth">
                                    <select name="payment">
                                        <option value="">All Payments</option>
                                        <?php foreach ($payment_options as $value => $label): ?>
                                            <option value="<?php echo $value; ?>" 
                                                    <?php echo (($_GET['payment'] ?? '') == $value) ? 'selected' : ''; ?>>
                                                <?php echo $label; ?>
                                            </option>
                                        <?php endforeach; ?>
                                    </select>
                                </div>
                            </div>
                        </div>
                    </div>
                    <div class="column is-3">
                        <div class="field">
                            <label class="label is-small">Search</label>
                            <div class="control">
                                <input class="input is-small" type="text" name="search" 
                                       placeholder="Name, NIC, or Invoice" 
                                       value="<?php echo htmlspecialchars($_GET['search'] ?? ''); ?>">
                            </div>
                        </div>
                    </div>
                </div>
                <div class="field">
                    <div class="control">
                        <button class="button is-primary is-small" type="submit">
                            <i class="fas fa-search mr-2"></i>
                            Apply Filters
                        </button>
                        <a href="students.php" class="button is-light is-small">
                            <i class="fas fa-times mr-2"></i>
                            Clear
                        </a>
                    </div>
                </div>
            </form>
        </div>

        <!-- Students Table -->
        <div class="box">
            <h2 class="title is-5">
                <i class="fas fa-list mr-2"></i>
                Student Registrations
            </h2>
            
            <?php if (empty($students)): ?>
                <div class="has-text-centered py-6">
                    <i class="fas fa-inbox fa-3x has-text-grey-light mb-4"></i>
                    <p class="title is-5 has-text-grey">No students found</p>
                    <p class="has-text-grey">No student registrations match your current filters</p>
                </div>
            <?php else: ?>
                <div class="table-container">
                    <table class="table is-fullwidth is-striped is-hoverable">
                        <thead>
                            <tr>
                                <th>Invoice</th>
                                <th>Student Details</th>
                                <th>Event</th>
                                <th>Registration Status</th>
                                <th>Payment Status</th>
                                <th>Amount</th>
                                <th>Actions</th>
                            </tr>
                        </thead>
                        <tbody>
                            <?php foreach ($students as $student): ?>
                                <tr>
                                    <td>
                                        <strong><?php echo htmlspecialchars($student['invoice_number']); ?></strong><br>
                                        <small class="has-text-grey"><?php echo date('M j, Y', strtotime($student['created_at'])); ?></small>
                                    </td>
                                    <td>
                                        <strong><?php echo htmlspecialchars($student['full_name']); ?></strong><br>
                                        <small class="has-text-grey"><?php echo htmlspecialchars($student['nic_number']); ?></small>
                                        <?php if ($student['phone_number']): ?>
                                            <br><small class="has-text-grey"><?php echo htmlspecialchars($student['phone_number']); ?></small>
                                        <?php endif; ?>
                                    </td>
                                    <td>
                                        <?php echo htmlspecialchars($student['event_name'] ?? 'Unknown'); ?><br>
                                        <small class="has-text-grey"><?php echo htmlspecialchars($student['session'] ?? 'No session'); ?></small>
                                    </td>
                                    <td>
                                        <?php
                                        $status_class = '';
                                        switch ($student['registration_status']) {
                                            case 'completed': $status_class = 'is-success'; break;
                                            case 'dispatched': case 'ready_for_pickup': $status_class = 'is-info'; break;
                                            case 'in_development': case 'photo_taken': $status_class = 'is-warning'; break;
                                            default: $status_class = 'is-light';
                                        }
                                        ?>
                                        <span class="tag <?php echo $status_class; ?>">
                                            <?php echo $status_options[$student['registration_status']] ?? ucfirst($student['registration_status']); ?>
                                        </span>
                                    </td>
                                    <td>
                                        <?php
                                        $payment_class = '';
                                        switch ($student['payment_status']) {
                                            case 'paid': $payment_class = 'is-success'; break;
                                            case 'failed': $payment_class = 'is-danger'; break;
                                            case 'refunded': $payment_class = 'is-info'; break;
                                            default: $payment_class = 'is-warning';
                                        }
                                        ?>
                                        <span class="tag <?php echo $payment_class; ?>">
                                            <?php echo $payment_options[$student['payment_status']] ?? ucfirst($student['payment_status']); ?>
                                        </span>
                                    </td>
                                    <td>
                                        <strong>LKR <?php echo number_format($student['total_amount'], 2); ?></strong>
                                    </td>
                                    <td>
                                        <div class="dropdown is-hoverable">
                                            <div class="dropdown-trigger">
                                                <button class="button is-small" aria-haspopup="true">
                                                    <i class="fas fa-ellipsis-v"></i>
                                                </button>
                                            </div>
                                            <div class="dropdown-menu">
                                                <div class="dropdown-content">
                                                    <a href="student-details.php?id=<?php echo $student['id']; ?>" class="dropdown-item">
                                                        <i class="fas fa-eye mr-2"></i>
                                                        View Details
                                                    </a>
                                                    <div class="dropdown-item">
                                                        <form method="POST" action="" style="margin: 0;">
                                                            <input type="hidden" name="csrf_token" value="<?php echo generate_csrf_token(); ?>">
                                                            <input type="hidden" name="action" value="update_status">
                                                            <input type="hidden" name="student_id" value="<?php echo $student['id']; ?>">
                                                            <div class="field">
                                                                <div class="control">
                                                                    <div class="select is-small">
                                                                        <select name="new_status" onchange="this.form.submit()">
                                                                            <option value="">Update Status</option>
                                                                            <?php foreach ($status_options as $value => $label): ?>
                                                                                <?php if ($value !== $student['registration_status']): ?>
                                                                                    <option value="<?php echo $value; ?>"><?php echo $label; ?></option>
                                                                                <?php endif; ?>
                                                                            <?php endforeach; ?>
                                                                        </select>
                                                                    </div>
                                                                </div>
                                                            </div>
                                                        </form>
                                                    </div>
                                                </div>
                                            </div>
                                        </div>
                                    </td>
                                </tr>
                            <?php endforeach; ?>
                        </tbody>
                    </table>
                </div>
            <?php endif; ?>
        </div>
    </div>

    <script>
        // Auto-hide notifications after 5 seconds
        document.addEventListener('DOMContentLoaded', function() {
            const notifications = document.querySelectorAll('.notification');
            notifications.forEach(function(notification) {
                setTimeout(function() {
                    notification.style.display = 'none';
                }, 5000);
            });

            // Mobile navigation toggle
            const burger = document.querySelector('.navbar-burger');
            const menu = document.querySelector('.navbar-menu');

            if (burger && menu) {
                burger.addEventListener('click', function() {
                    burger.classList.toggle('is-active');
                    menu.classList.toggle('is-active');
                });
            }
        });
    </script>
</body>
</html>
