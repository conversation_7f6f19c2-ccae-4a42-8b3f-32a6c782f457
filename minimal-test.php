<?php
require_once 'config/simple-config.php';

echo "<h1>Minimal Test</h1>";
echo "<p>✅ PHP is working</p>";
echo "<p>✅ Session started</p>";
echo "<p>✅ Config loaded</p>";

// Test database connection
try {
    $pdo = new PDO("mysql:host=localhost", "root", "");
    echo "<p>✅ MySQL connection works</p>";
    
    // Check databases
    $stmt = $pdo->query("SHOW DATABASES");
    $databases = $stmt->fetchAll(PDO::FETCH_COLUMN);
    
    $local_exists = in_array('photo_session_management', $databases);
    $external_exists = in_array('student_registration', $databases);
    
    echo "<p>Local DB exists: " . ($local_exists ? "✅ YES" : "❌ NO") . "</p>";
    echo "<p>External DB exists: " . ($external_exists ? "✅ YES" : "❌ NO") . "</p>";
    
    if (!$local_exists || !$external_exists) {
        echo "<div style='background: #fff3cd; padding: 15px; border: 1px solid #ffeaa7; border-radius: 5px; margin: 10px 0;'>";
        echo "<h3>⚠️ Databases Missing</h3>";
        echo "<p><strong>You need to create the databases first!</strong></p>";
        echo "<ol>";
        echo "<li>Go to <a href='http://localhost/phpmyadmin' target='_blank'>phpMyAdmin</a></li>";
        echo "<li>Click 'SQL' tab</li>";
        echo "<li>Copy and paste the contents of <code>database/local_database.sql</code></li>";
        echo "<li>Click 'Go'</li>";
        echo "<li>Repeat for <code>database/external_database.sql</code></li>";
        echo "</ol>";
        echo "</div>";
    } else {
        echo "<div style='background: #d4edda; padding: 15px; border: 1px solid #c3e6cb; border-radius: 5px; margin: 10px 0;'>";
        echo "<h3>✅ Ready to Test</h3>";
        echo "<p>Databases are ready! Try the <a href='login.php'>login page</a></p>";
        echo "<p>Default credentials: admin / admin123</p>";
        echo "</div>";
    }
    
} catch (Exception $e) {
    echo "<p>❌ Database error: " . htmlspecialchars($e->getMessage()) . "</p>";
    echo "<div style='background: #f8d7da; padding: 15px; border: 1px solid #f5c6cb; border-radius: 5px; margin: 10px 0;'>";
    echo "<h3>🔧 Fix MySQL Connection</h3>";
    echo "<ol>";
    echo "<li>Open XAMPP Control Panel</li>";
    echo "<li>Start MySQL service</li>";
    echo "<li>Make sure it's running on port 3306</li>";
    echo "<li>Refresh this page</li>";
    echo "</ol>";
    echo "</div>";
}
?>
