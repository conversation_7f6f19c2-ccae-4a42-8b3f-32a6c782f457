<?php
require_once '../config/config.php';
require_login();

$photoSessionService = new PhotoSessionService();

// Get filters
$filters = [
    'search' => sanitize_input($_GET['search'] ?? ''),
    'date_from' => sanitize_input($_GET['date_from'] ?? ''),
    'date_to' => sanitize_input($_GET['date_to'] ?? ''),
    'operator_id' => $_SESSION['user_id'] // Only show current operator's sessions
];

$page = max(1, intval($_GET['page'] ?? 1));
$limit = 20;
$offset = ($page - 1) * $limit;

$sessions = $photoSessionService->getPhotoSessions($filters, $limit, $offset);
?>
<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>My Sessions - <?php echo APP_NAME; ?></title>
    <link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/bulma@0.9.4/css/bulma.min.css">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css">
    <style>
        .dashboard-header {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
        }
        .filter-section {
            background: #f8f9fa;
            border-radius: 8px;
            padding: 1rem;
            margin-bottom: 1rem;
        }
        .session-card {
            border-left: 4px solid #667eea;
            transition: transform 0.2s;
        }
        .session-card:hover {
            transform: translateY(-2px);
        }
        .back-button {
            position: fixed;
            top: 20px;
            left: 20px;
            z-index: 1000;
        }
    </style>
</head>
<body>
    <!-- Back Button -->
    <a href="dashboard.php" class="button back-button">
        <i class="fas fa-arrow-left mr-2"></i>
        Back to Dashboard
    </a>

    <div class="container mt-6">
        <!-- Header -->
        <div class="box">
            <h1 class="title is-4">
                <i class="fas fa-list mr-2"></i>
                My Photo Sessions
            </h1>
            <p class="subtitle is-6">View your completed photo sessions</p>
        </div>

        <!-- Filters -->
        <div class="filter-section">
            <form method="GET" action="">
                <div class="columns">
                    <div class="column is-4">
                        <div class="field">
                            <label class="label is-small">Search</label>
                            <div class="control">
                                <input class="input is-small" type="text" name="search" 
                                       placeholder="Student name, NIC, or Invoice" 
                                       value="<?php echo htmlspecialchars($filters['search']); ?>">
                            </div>
                        </div>
                    </div>
                    <div class="column is-3">
                        <div class="field">
                            <label class="label is-small">From Date</label>
                            <div class="control">
                                <input class="input is-small" type="date" name="date_from" 
                                       value="<?php echo htmlspecialchars($filters['date_from']); ?>">
                            </div>
                        </div>
                    </div>
                    <div class="column is-3">
                        <div class="field">
                            <label class="label is-small">To Date</label>
                            <div class="control">
                                <input class="input is-small" type="date" name="date_to" 
                                       value="<?php echo htmlspecialchars($filters['date_to']); ?>">
                            </div>
                        </div>
                    </div>
                    <div class="column is-2">
                        <div class="field">
                            <label class="label is-small">&nbsp;</label>
                            <div class="control">
                                <button class="button is-primary is-small is-fullwidth" type="submit">
                                    <i class="fas fa-search mr-2"></i>
                                    Search
                                </button>
                            </div>
                        </div>
                    </div>
                </div>
                
                <?php if ($filters['search'] || $filters['date_from'] || $filters['date_to']): ?>
                    <div class="has-text-centered">
                        <a href="sessions.php" class="button is-small is-light">
                            <i class="fas fa-times mr-1"></i>
                            Clear Filters
                        </a>
                    </div>
                <?php endif; ?>
            </form>
        </div>

        <!-- Sessions -->
        <?php if (empty($sessions)): ?>
            <div class="box">
                <div class="has-text-centered py-6">
                    <i class="fas fa-inbox fa-3x has-text-grey-light mb-4"></i>
                    <p class="title is-5 has-text-grey">No photo sessions found</p>
                    <p class="has-text-grey">
                        <?php if ($filters['search'] || $filters['date_from'] || $filters['date_to']): ?>
                            Try adjusting your search filters
                        <?php else: ?>
                            Start scanning QR codes to record photo sessions
                        <?php endif; ?>
                    </p>
                    <div class="mt-4">
                        <a href="qr-scanner.php" class="button is-primary">
                            <i class="fas fa-qrcode mr-2"></i>
                            Scan QR Code
                        </a>
                    </div>
                </div>
            </div>
        <?php else: ?>
            <div class="columns is-multiline">
                <?php foreach ($sessions as $session): ?>
                    <div class="column is-6-desktop is-12-tablet">
                        <div class="box session-card">
                            <div class="level is-mobile">
                                <div class="level-left">
                                    <div>
                                        <h3 class="title is-6 mb-2">
                                            <?php echo htmlspecialchars($session['student_name']); ?>
                                        </h3>
                                        <p class="subtitle is-7 has-text-grey mb-1">
                                            Invoice: <?php echo htmlspecialchars($session['invoice_number']); ?>
                                        </p>
                                        <p class="subtitle is-7 has-text-grey">
                                            NIC: <?php echo htmlspecialchars($session['student_nic']); ?>
                                        </p>
                                    </div>
                                </div>
                                <div class="level-right">
                                    <span class="tag is-primary is-large">
                                        <?php echo htmlspecialchars($session['photo_sequence_number']); ?>
                                    </span>
                                </div>
                            </div>
                            
                            <div class="content is-small">
                                <?php if ($session['session']): ?>
                                    <p><strong>Session:</strong> <?php echo htmlspecialchars($session['session']); ?></p>
                                <?php endif; ?>
                                
                                <?php if ($session['seat_number']): ?>
                                    <p><strong>Seat:</strong> <?php echo htmlspecialchars($session['seat_number']); ?></p>
                                <?php endif; ?>
                                
                                <?php if (!empty($session['photo_types'])): ?>
                                    <p><strong>Photo Types:</strong></p>
                                    <div class="tags are-small">
                                        <?php foreach ($session['photo_types'] as $type): ?>
                                            <span class="tag is-info"><?php echo htmlspecialchars($type); ?></span>
                                        <?php endforeach; ?>
                                    </div>
                                <?php endif; ?>
                                
                                <div class="level is-mobile mt-3">
                                    <div class="level-left">
                                        <div class="level-item">
                                            <div>
                                                <p class="heading">Completed</p>
                                                <p class="title is-7">
                                                    <?php echo date('M j, Y H:i', strtotime($session['completed_at'])); ?>
                                                </p>
                                            </div>
                                        </div>
                                    </div>
                                    <div class="level-right">
                                        <?php if ($session['whatsapp_number']): ?>
                                            <a href="tel:<?php echo htmlspecialchars($session['whatsapp_number']); ?>" 
                                               class="button is-small is-success">
                                                <i class="fab fa-whatsapp"></i>
                                            </a>
                                        <?php endif; ?>
                                        <?php if ($session['alternate_phone']): ?>
                                            <a href="tel:<?php echo htmlspecialchars($session['alternate_phone']); ?>" 
                                               class="button is-small is-info ml-1">
                                                <i class="fas fa-phone"></i>
                                            </a>
                                        <?php endif; ?>
                                    </div>
                                </div>
                                
                                <?php if ($session['notes']): ?>
                                    <div class="box has-background-light is-small mt-2">
                                        <p class="is-size-7">
                                            <strong>Notes:</strong> <?php echo htmlspecialchars($session['notes']); ?>
                                        </p>
                                    </div>
                                <?php endif; ?>
                            </div>
                        </div>
                    </div>
                <?php endforeach; ?>
            </div>
            
            <!-- Pagination -->
            <nav class="pagination is-centered mt-4" role="navigation">
                <?php if ($page > 1): ?>
                    <a href="?<?php echo http_build_query(array_merge($_GET, ['page' => $page - 1])); ?>" 
                       class="pagination-previous">Previous</a>
                <?php endif; ?>
                
                <?php if (count($sessions) == $limit): ?>
                    <a href="?<?php echo http_build_query(array_merge($_GET, ['page' => $page + 1])); ?>" 
                       class="pagination-next">Next</a>
                <?php endif; ?>
                
                <ul class="pagination-list">
                    <li><span class="pagination-link is-current">Page <?php echo $page; ?></span></li>
                </ul>
            </nav>
        <?php endif; ?>

        <!-- Quick Actions -->
        <div class="box mt-4">
            <h2 class="title is-6">
                <i class="fas fa-bolt mr-2"></i>
                Quick Actions
            </h2>
            <div class="buttons">
                <a href="qr-scanner.php" class="button is-primary">
                    <i class="fas fa-qrcode mr-2"></i>
                    Scan QR Code
                </a>
                <a href="dashboard.php" class="button is-light">
                    <i class="fas fa-tachometer-alt mr-2"></i>
                    Dashboard
                </a>
            </div>
        </div>
    </div>
</body>
</html>
