<?php
// Authentication check - must be first
require_once 'auth-check.php';
require_admin();

$eventService = new EventService();
$message = '';
$message_type = '';

// Handle form submissions
if ($_SERVER['REQUEST_METHOD'] === 'POST') {
    $action = $_POST['action'] ?? '';
    
    if ($action === 'add_session') {
        $data = [
            'event_id' => (int)($_POST['event_id'] ?? 0),
            'session_name' => sanitize_input($_POST['session_name'] ?? ''),
            'session_code' => sanitize_input($_POST['session_code'] ?? ''),
            'session_date' => sanitize_input($_POST['session_date'] ?? '') ?: null,
            'session_time' => sanitize_input($_POST['session_time'] ?? '') ?: null,
            'venue' => sanitize_input($_POST['venue'] ?? ''),
            'max_capacity' => (int)($_POST['max_capacity'] ?? 0) ?: null,
            'description' => sanitize_input($_POST['description'] ?? ''),
            'is_active' => isset($_POST['is_active']) ? 1 : 0
        ];
        
        if ($data['event_id'] && $data['session_name'] && $data['session_code']) {
            $result = $eventService->createSession($data);
            if ($result) {
                $message = 'Session added successfully';
                $message_type = 'is-success';
            } else {
                $message = 'Error adding session. Please check if session code already exists.';
                $message_type = 'is-danger';
            }
        } else {
            $message = 'Event, session name, and session code are required';
            $message_type = 'is-warning';
        }
    } elseif ($action === 'edit_session') {
        $session_id = (int)($_POST['session_id'] ?? 0);
        $data = [
            'session_name' => sanitize_input($_POST['session_name'] ?? ''),
            'session_code' => sanitize_input($_POST['session_code'] ?? ''),
            'session_date' => sanitize_input($_POST['session_date'] ?? '') ?: null,
            'session_time' => sanitize_input($_POST['session_time'] ?? '') ?: null,
            'venue' => sanitize_input($_POST['venue'] ?? ''),
            'max_capacity' => (int)($_POST['max_capacity'] ?? 0) ?: null,
            'description' => sanitize_input($_POST['description'] ?? ''),
            'is_active' => isset($_POST['is_active']) ? 1 : 0
        ];
        
        if ($session_id && $data['session_name'] && $data['session_code']) {
            $result = $eventService->updateSession($session_id, $data);
            if ($result) {
                $message = 'Session updated successfully';
                $message_type = 'is-success';
            } else {
                $message = 'Error updating session';
                $message_type = 'is-danger';
            }
        } else {
            $message = 'Session name and code are required';
            $message_type = 'is-warning';
        }
    }
}

// Get events and sessions
$events = $eventService->getAllEvents(true);
$selected_event_id = (int)($_GET['event_id'] ?? 0);
$sessions = [];

if ($selected_event_id) {
    $sessions = $eventService->getEventSessions($selected_event_id, true);
}
?>
<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Event Sessions Management - <?php echo APP_NAME; ?></title>
    <link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/bulma@0.9.4/css/bulma.min.css">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css">
    <style>
        .dashboard-header {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
        }
        .navbar-item.has-text-white:hover {
            background-color: rgba(255,255,255,0.1);
            color: white !important;
        }
        .session-card {
            border-left: 4px solid #3273dc;
            transition: all 0.3s ease;
        }
        .session-card:hover {
            transform: translateY(-2px);
            box-shadow: 0 8px 25px rgba(0,0,0,0.1);
        }
        .session-card.inactive {
            border-left-color: #dbdbdb;
            opacity: 0.7;
        }
        .capacity-indicator {
            background: linear-gradient(90deg, #48c78e 0%, #48c78e var(--fill-percent), #f5f5f5 var(--fill-percent), #f5f5f5 100%);
            height: 8px;
            border-radius: 4px;
        }

        /* Mobile responsiveness for admin pages */
        @media screen and (max-width: 1024px) {
            .navbar-menu {
                background-color: #667eea;
            }

            .container {
                padding: 0 1rem;
            }

            .session-card .level {
                display: block !important;
            }

            .session-card .level-left {
                margin-bottom: 1rem;
            }

            .session-card .level-right {
                text-align: right;
            }
        }

        @media screen and (max-width: 768px) {
            .title.is-4 {
                font-size: 1.5rem;
            }

            .subtitle.is-6 {
                font-size: 1rem;
            }

            .box {
                padding: 1rem;
            }

            .session-card {
                margin-bottom: 1rem;
            }

            .session-card .title.is-6 {
                font-size: 1rem;
                line-height: 1.3;
                margin-bottom: 0.5rem;
            }

            .session-card .subtitle.is-7 {
                font-size: 0.8rem;
                margin-bottom: 1rem;
            }

            .columns.is-multiline .column {
                padding: 0.5rem;
            }

            .level.is-mobile .level-left,
            .level.is-mobile .level-right {
                flex-basis: auto;
            }

            .modal-card {
                margin: 1rem;
                max-height: calc(100vh - 2rem);
            }

            .modal-card-body {
                padding: 1rem;
            }
        }

        @media screen and (max-width: 480px) {
            .container {
                padding: 0 0.5rem;
            }

            .box {
                padding: 0.75rem;
            }

            .title.is-4 {
                font-size: 1.25rem;
            }

            .session-card .content.is-small {
                font-size: 0.8rem;
            }

            .session-card .content.is-small p {
                margin-bottom: 0.5rem;
            }

            .buttons.are-small .button {
                font-size: 0.7rem;
                padding: 0.25rem 0.5rem;
            }
        }

        /* Custom select dropdown arrow positioning */
        .select:not(.is-multiple):not(.is-loading)::after {
            margin-top: -0.9em !important;
            transform: none !important;
        }
    </style>
</head>
<body>
    <!-- Navigation -->
    <nav class="navbar dashboard-header" role="navigation">
        <div class="navbar-brand">
            <div class="navbar-item" style="border-right: 1px solid #ccc; padding-right: 1rem; margin-right: 1rem;">
                <i class="fas fa-shield-alt mr-2" style="color: #ffffffff;"></i>
                <span style="font-weight: bold; color: #ffffffff;">Admin Dashboard</span>
            </div>

            <a role="button" class="navbar-burger has-text-white" data-target="navbarMenu">
                <span></span>
                <span></span>
                <span></span>
            </a>
        </div>

        <div class="navbar-menu" id="navbarMenu">
            <div class="navbar-start">
                <a class="navbar-item has-text-white" href="dashboard.php">
                    <i class="fas fa-tachometer-alt mr-2"></i>
                    Dashboard
                </a>
                <a class="navbar-item has-text-white" href="operators.php">
                    <i class="fas fa-users mr-2"></i>
                    Operators
                </a>
                <a class="navbar-item has-text-white" href="sessions.php">
                    <i class="fas fa-camera mr-2"></i>
                    Photo Sessions
                </a>
                <a class="navbar-item has-text-white is-active" href="event-sessions.php">
                    <i class="fas fa-calendar-alt mr-2"></i>
                    Event Sessions
                </a>
                <a class="navbar-item has-text-white" href="students.php">
                    <i class="fas fa-graduation-cap mr-2"></i>
                    Students
                </a>
                <a class="navbar-item has-text-white" href="photo-pricing.php">
                    <i class="fas fa-tags mr-2"></i>
                    Photo Pricing
                </a>
                <a class="navbar-item has-text-white" href="reports.php">
                    <i class="fas fa-chart-bar mr-2"></i>
                    Reports
                </a>
            </div>
            
            <div class="navbar-end">
                <div class="navbar-item has-dropdown is-hoverable">
                    <a class="navbar-link has-text-white">
                        <i class="fas fa-user-shield mr-2"></i>
                        <?php echo htmlspecialchars($_SESSION['full_name']); ?>
                    </a>
                    <div class="navbar-dropdown">
                        <a class="navbar-item" href="profile.php">
                            <i class="fas fa-user-cog mr-2"></i>
                            Profile
                        </a>
                        <a class="navbar-item" href="settings.php">
                            <i class="fas fa-cog mr-2"></i>
                            Settings
                        </a>
                        <hr class="navbar-divider">
                        <a class="navbar-item" href="../logout.php">
                            <i class="fas fa-sign-out-alt mr-2"></i>
                            Logout
                        </a>
                    </div>
                </div>
            </div>
        </div>
    </nav>

    <div class="container mt-4">
        <!-- Header -->
        <div class="box">
            <div class="level">
                <div class="level-left">
                    <div>
                        <h1 class="title is-4">
                            <i class="fas fa-calendar-alt mr-2"></i>
                            Event Sessions Management
                        </h1>
                        <p class="subtitle is-6">Manage sessions for each event</p>
                    </div>
                </div>
                <div class="level-right">
                    <button class="button is-primary" onclick="openAddModal()">
                        <i class="fas fa-plus mr-2"></i>
                        Add New Session
                    </button>
                </div>
            </div>
        </div>

        <!-- Messages -->
        <?php if ($message): ?>
            <div class="notification <?php echo $message_type; ?>">
                <button class="delete" onclick="this.parentElement.style.display='none'"></button>
                <?php echo htmlspecialchars($message); ?>
            </div>
        <?php endif; ?>

        <!-- Event Selection -->
        <div class="box">
            <h2 class="title is-5">
                <i class="fas fa-filter mr-2"></i>
                Select Event
            </h2>
            <form method="GET" action="">
                <div class="field has-addons">
                    <div class="control is-expanded">
                        <div class="select is-fullwidth">
                            <select name="event_id" onchange="this.form.submit()">
                                <option value="">Choose an event to manage sessions</option>
                                <?php foreach ($events as $event): ?>
                                    <option value="<?php echo $event['id']; ?>" 
                                            <?php echo ($selected_event_id == $event['id']) ? 'selected' : ''; ?>>
                                        <?php echo htmlspecialchars($event['event_name']); ?>
                                        <?php if ($event['event_date']): ?>
                                            - <?php echo date('M j, Y', strtotime($event['event_date'])); ?>
                                        <?php endif; ?>
                                        <?php if (!$event['is_active']): ?>
                                            (Inactive)
                                        <?php endif; ?>
                                    </option>
                                <?php endforeach; ?>
                            </select>
                        </div>
                    </div>
                </div>
            </form>
        </div>

        <!-- Sessions Display -->
        <?php if ($selected_event_id): ?>
            <?php $selected_event = array_filter($events, fn($e) => $e['id'] == $selected_event_id)[0] ?? null; ?>
            <?php if ($selected_event): ?>
                <div class="box">
                    <h2 class="title is-5">
                        <i class="fas fa-list mr-2"></i>
                        Sessions for: <?php echo htmlspecialchars($selected_event['event_name']); ?>
                    </h2>
                    
                    <?php if (empty($sessions)): ?>
                        <div class="has-text-centered py-6">
                            <i class="fas fa-calendar-plus fa-3x has-text-grey-light mb-4"></i>
                            <p class="title is-5 has-text-grey">No sessions found</p>
                            <p class="has-text-grey">Add sessions to organize your event</p>
                            <button class="button is-primary mt-4" onclick="openAddModal()">
                                <i class="fas fa-plus mr-2"></i>
                                Add First Session
                            </button>
                        </div>
                    <?php else: ?>
                        <div class="columns is-multiline">
                            <?php foreach ($sessions as $session): ?>
                                <div class="column is-6-desktop is-12-tablet">
                                    <div class="box session-card <?php echo $session['is_active'] ? '' : 'inactive'; ?>">
                                        <div class="level is-mobile">
                                            <div class="level-left">
                                                <div>
                                                    <h3 class="title is-6 mb-2">
                                                        <?php echo htmlspecialchars($session['session_name']); ?>
                                                        <?php if (!$session['is_active']): ?>
                                                            <span class="tag is-light is-small ml-2">Inactive</span>
                                                        <?php endif; ?>
                                                    </h3>
                                                    <p class="subtitle is-7 has-text-grey mb-2">
                                                        Code: <?php echo htmlspecialchars($session['session_code']); ?>
                                                    </p>
                                                </div>
                                            </div>
                                            <div class="level-right">
                                                <div class="buttons are-small">
                                                    <button class="button is-info is-small" 
                                                            onclick="editSession(<?php echo htmlspecialchars(json_encode($session)); ?>)">
                                                        <i class="fas fa-edit"></i>
                                                    </button>
                                                </div>
                                            </div>
                                        </div>
                                        
                                        <div class="content is-small">
                                            <?php if ($session['session_date'] || $session['session_time']): ?>
                                                <p class="mb-2">
                                                    <i class="fas fa-calendar mr-2 has-text-info"></i>
                                                    <?php if ($session['session_date']): ?>
                                                        <?php echo date('M j, Y', strtotime($session['session_date'])); ?>
                                                    <?php endif; ?>
                                                    <?php if ($session['session_time']): ?>
                                                        at <?php echo date('g:i A', strtotime($session['session_time'])); ?>
                                                    <?php endif; ?>
                                                </p>
                                            <?php endif; ?>
                                            
                                            <?php if ($session['venue']): ?>
                                                <p class="mb-2">
                                                    <i class="fas fa-map-marker-alt mr-2 has-text-danger"></i>
                                                    <?php echo htmlspecialchars($session['venue']); ?>
                                                </p>
                                            <?php endif; ?>
                                            
                                            <?php if ($session['max_capacity']): ?>
                                                <p class="mb-2">
                                                    <i class="fas fa-users mr-2 has-text-success"></i>
                                                    Capacity: <?php echo $session['registered_count']; ?>/<?php echo $session['max_capacity']; ?>
                                                </p>
                                                <div class="capacity-indicator" 
                                                     style="--fill-percent: <?php echo min(100, ($session['registered_count'] / $session['max_capacity']) * 100); ?>%"></div>
                                            <?php else: ?>
                                                <p class="mb-2">
                                                    <i class="fas fa-users mr-2 has-text-success"></i>
                                                    Registered: <?php echo $session['registered_count']; ?>
                                                </p>
                                            <?php endif; ?>
                                            
                                            <?php if ($session['description']): ?>
                                                <p class="mt-3 has-text-grey">
                                                    <?php echo htmlspecialchars($session['description']); ?>
                                                </p>
                                            <?php endif; ?>
                                        </div>
                                    </div>
                                </div>
                            <?php endforeach; ?>
                        </div>
                    <?php endif; ?>
                </div>
            <?php endif; ?>
        <?php endif; ?>
    </div>

    <!-- Add Session Modal -->
    <div class="modal" id="addSessionModal">
        <div class="modal-background" onclick="closeAddModal()"></div>
        <div class="modal-card">
            <header class="modal-card-head">
                <p class="modal-card-title">
                    <i class="fas fa-plus mr-2"></i>
                    Add New Session
                </p>
                <button class="delete" onclick="closeAddModal()"></button>
            </header>
            <form method="POST" action="">
                <input type="hidden" name="action" value="add_session">
                <section class="modal-card-body">
                    <div class="field">
                        <label class="label">Event *</label>
                        <div class="control">
                            <div class="select is-fullwidth">
                                <select name="event_id" required>
                                    <option value="">Select an event</option>
                                    <?php foreach ($events as $event): ?>
                                        <?php if ($event['is_active']): ?>
                                            <option value="<?php echo $event['id']; ?>"
                                                    <?php echo ($selected_event_id == $event['id']) ? 'selected' : ''; ?>>
                                                <?php echo htmlspecialchars($event['event_name']); ?>
                                            </option>
                                        <?php endif; ?>
                                    <?php endforeach; ?>
                                </select>
                            </div>
                        </div>
                    </div>

                    <div class="columns">
                        <div class="column is-8">
                            <div class="field">
                                <label class="label">Session Name *</label>
                                <div class="control">
                                    <input class="input" type="text" name="session_name" required
                                           placeholder="e.g., Morning Session - Faculty of Engineering">
                                </div>
                            </div>
                        </div>
                        <div class="column is-4">
                            <div class="field">
                                <label class="label">Session Code *</label>
                                <div class="control">
                                    <input class="input" type="text" name="session_code" required
                                           placeholder="e.g., CONV2024_ENG_AM">
                                </div>
                            </div>
                        </div>
                    </div>

                    <div class="columns">
                        <div class="column is-6">
                            <div class="field">
                                <label class="label">Date</label>
                                <div class="control">
                                    <input class="input" type="date" name="session_date">
                                </div>
                            </div>
                        </div>
                        <div class="column is-6">
                            <div class="field">
                                <label class="label">Time</label>
                                <div class="control">
                                    <input class="input" type="time" name="session_time">
                                </div>
                            </div>
                        </div>
                    </div>

                    <div class="columns">
                        <div class="column is-8">
                            <div class="field">
                                <label class="label">Venue</label>
                                <div class="control">
                                    <input class="input" type="text" name="venue"
                                           placeholder="e.g., Main Auditorium">
                                </div>
                            </div>
                        </div>
                        <div class="column is-4">
                            <div class="field">
                                <label class="label">Max Capacity</label>
                                <div class="control">
                                    <input class="input" type="number" name="max_capacity" min="1"
                                           placeholder="e.g., 200">
                                </div>
                            </div>
                        </div>
                    </div>

                    <div class="field">
                        <label class="label">Description</label>
                        <div class="control">
                            <textarea class="textarea" name="description" rows="3"
                                      placeholder="Optional description of the session"></textarea>
                        </div>
                    </div>

                    <div class="field">
                        <div class="control">
                            <label class="checkbox">
                                <input type="checkbox" name="is_active" checked>
                                Active (students can register for this session)
                            </label>
                        </div>
                    </div>
                </section>
                <footer class="modal-card-foot">
                    <button class="button is-primary" type="submit">
                        <i class="fas fa-save mr-2"></i>
                        Add Session
                    </button>
                    <button class="button" type="button" onclick="closeAddModal()">Cancel</button>
                </footer>
            </form>
        </div>
    </div>

    <!-- Edit Session Modal -->
    <div class="modal" id="editSessionModal">
        <div class="modal-background" onclick="closeEditModal()"></div>
        <div class="modal-card">
            <header class="modal-card-head">
                <p class="modal-card-title">
                    <i class="fas fa-edit mr-2"></i>
                    Edit Session
                </p>
                <button class="delete" onclick="closeEditModal()"></button>
            </header>
            <form method="POST" action="">
                <input type="hidden" name="action" value="edit_session">
                <input type="hidden" name="session_id" id="edit_session_id">
                <section class="modal-card-body">
                    <div class="columns">
                        <div class="column is-8">
                            <div class="field">
                                <label class="label">Session Name *</label>
                                <div class="control">
                                    <input class="input" type="text" name="session_name" id="edit_session_name" required>
                                </div>
                            </div>
                        </div>
                        <div class="column is-4">
                            <div class="field">
                                <label class="label">Session Code *</label>
                                <div class="control">
                                    <input class="input" type="text" name="session_code" id="edit_session_code" required>
                                </div>
                            </div>
                        </div>
                    </div>

                    <div class="columns">
                        <div class="column is-6">
                            <div class="field">
                                <label class="label">Date</label>
                                <div class="control">
                                    <input class="input" type="date" name="session_date" id="edit_session_date">
                                </div>
                            </div>
                        </div>
                        <div class="column is-6">
                            <div class="field">
                                <label class="label">Time</label>
                                <div class="control">
                                    <input class="input" type="time" name="session_time" id="edit_session_time">
                                </div>
                            </div>
                        </div>
                    </div>

                    <div class="columns">
                        <div class="column is-8">
                            <div class="field">
                                <label class="label">Venue</label>
                                <div class="control">
                                    <input class="input" type="text" name="venue" id="edit_venue">
                                </div>
                            </div>
                        </div>
                        <div class="column is-4">
                            <div class="field">
                                <label class="label">Max Capacity</label>
                                <div class="control">
                                    <input class="input" type="number" name="max_capacity" id="edit_max_capacity" min="1">
                                </div>
                            </div>
                        </div>
                    </div>

                    <div class="field">
                        <label class="label">Description</label>
                        <div class="control">
                            <textarea class="textarea" name="description" id="edit_description" rows="3"></textarea>
                        </div>
                    </div>

                    <div class="field">
                        <div class="control">
                            <label class="checkbox">
                                <input type="checkbox" name="is_active" id="edit_is_active">
                                Active (students can register for this session)
                            </label>
                        </div>
                    </div>
                </section>
                <footer class="modal-card-foot">
                    <button class="button is-primary" type="submit">
                        <i class="fas fa-save mr-2"></i>
                        Update Session
                    </button>
                    <button class="button" type="button" onclick="closeEditModal()">Cancel</button>
                </footer>
            </form>
        </div>
    </div>

    <script>
        function openAddModal() {
            document.getElementById('addSessionModal').classList.add('is-active');
        }

        function closeAddModal() {
            document.getElementById('addSessionModal').classList.remove('is-active');
        }

        function openEditModal() {
            document.getElementById('editSessionModal').classList.add('is-active');
        }

        function closeEditModal() {
            document.getElementById('editSessionModal').classList.remove('is-active');
        }

        function editSession(session) {
            document.getElementById('edit_session_id').value = session.id;
            document.getElementById('edit_session_name').value = session.session_name;
            document.getElementById('edit_session_code').value = session.session_code;
            document.getElementById('edit_session_date').value = session.session_date || '';
            document.getElementById('edit_session_time').value = session.session_time || '';
            document.getElementById('edit_venue').value = session.venue || '';
            document.getElementById('edit_max_capacity').value = session.max_capacity || '';
            document.getElementById('edit_description').value = session.description || '';
            document.getElementById('edit_is_active').checked = session.is_active == 1;

            openEditModal();
        }

        // Close modals when clicking outside
        document.addEventListener('click', function(event) {
            if (event.target.classList.contains('modal-background')) {
                closeAddModal();
                closeEditModal();
            }
        });

        // Close modals with Escape key
        document.addEventListener('keydown', function(event) {
            if (event.key === 'Escape') {
                closeAddModal();
                closeEditModal();
            }
        });

        // Mobile navigation toggle
        document.addEventListener('DOMContentLoaded', function() {
            const burger = document.querySelector('.navbar-burger');
            const menu = document.querySelector('.navbar-menu');

            if (burger && menu) {
                burger.addEventListener('click', function() {
                    burger.classList.toggle('is-active');
                    menu.classList.toggle('is-active');
                });
            }
        });
    </script>
</body>
</html>
