<?php if (!isset($reportData)) exit; ?>

<!-- Summary Statistics -->
<div class="columns">
    <div class="column">
        <div class="box stat-card">
            <div class="level">
                <div class="level-left">
                    <div>
                        <p class="heading">Total Registered</p>
                        <p class="title is-3"><?php echo number_format($reportData['summary']['total_registered']); ?></p>
                    </div>
                </div>
                <div class="level-right">
                    <i class="fas fa-users fa-2x has-text-info"></i>
                </div>
            </div>
        </div>
    </div>
    <div class="column">
        <div class="box stat-card">
            <div class="level">
                <div class="level-left">
                    <div>
                        <p class="heading">Completed</p>
                        <p class="title is-3"><?php echo number_format($reportData['summary']['total_completed']); ?></p>
                    </div>
                </div>
                <div class="level-right">
                    <i class="fas fa-check-circle fa-2x has-text-success"></i>
                </div>
            </div>
        </div>
    </div>
    <div class="column">
        <div class="box stat-card">
            <div class="level">
                <div class="level-left">
                    <div>
                        <p class="heading">Pending</p>
                        <p class="title is-3"><?php echo number_format($reportData['summary']['pending']); ?></p>
                    </div>
                </div>
                <div class="level-right">
                    <i class="fas fa-clock fa-2x has-text-warning"></i>
                </div>
            </div>
        </div>
    </div>
    <div class="column">
        <div class="box stat-card">
            <div class="level">
                <div class="level-left">
                    <div>
                        <p class="heading">Completion Rate</p>
                        <p class="title is-3"><?php echo $reportData['summary']['completion_rate']; ?>%</p>
                    </div>
                </div>
                <div class="level-right">
                    <i class="fas fa-percentage fa-2x has-text-primary"></i>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Progress Visualization -->
<div class="columns">
    <div class="column is-8">
        <div class="box">
            <h2 class="title is-5">
                <i class="fas fa-chart-pie mr-2"></i>
                Completion Progress
            </h2>
            <div class="chart-container">
                <canvas id="progressChart"></canvas>
            </div>
        </div>
    </div>
    <div class="column is-4">
        <div class="box">
            <h2 class="title is-5">
                <i class="fas fa-info-circle mr-2"></i>
                Status Breakdown
            </h2>
            <div class="content">
                <div class="level is-mobile">
                    <div class="level-left">
                        <span class="tag is-success">Completed</span>
                    </div>
                    <div class="level-right">
                        <strong><?php echo number_format($reportData['summary']['total_completed']); ?></strong>
                    </div>
                </div>
                <div class="level is-mobile">
                    <div class="level-left">
                        <span class="tag is-warning">Pending</span>
                    </div>
                    <div class="level-right">
                        <strong><?php echo number_format($reportData['summary']['pending']); ?></strong>
                    </div>
                </div>
                <hr>
                <div class="level is-mobile">
                    <div class="level-left">
                        <span class="tag is-info">Total</span>
                    </div>
                    <div class="level-right">
                        <strong><?php echo number_format($reportData['summary']['total_registered']); ?></strong>
                    </div>
                </div>
            </div>
            
            <!-- Progress Bar -->
            <div class="field">
                <label class="label is-small">Overall Progress</label>
                <progress class="progress is-success" 
                          value="<?php echo $reportData['summary']['completion_rate']; ?>" 
                          max="100">
                    <?php echo $reportData['summary']['completion_rate']; ?>%
                </progress>
            </div>
            
            <!-- Status Indicators -->
            <div class="content is-small">
                <?php if ($reportData['summary']['completion_rate'] >= 90): ?>
                    <div class="notification is-success is-light">
                        <i class="fas fa-trophy mr-2"></i>
                        <strong>Excellent Progress!</strong><br>
                        Nearly all students have completed their photo sessions.
                    </div>
                <?php elseif ($reportData['summary']['completion_rate'] >= 70): ?>
                    <div class="notification is-info is-light">
                        <i class="fas fa-thumbs-up mr-2"></i>
                        <strong>Good Progress</strong><br>
                        Most students have completed their sessions.
                    </div>
                <?php elseif ($reportData['summary']['completion_rate'] >= 50): ?>
                    <div class="notification is-warning is-light">
                        <i class="fas fa-clock mr-2"></i>
                        <strong>Moderate Progress</strong><br>
                        About half of the students have completed their sessions.
                    </div>
                <?php else: ?>
                    <div class="notification is-danger is-light">
                        <i class="fas fa-exclamation-triangle mr-2"></i>
                        <strong>Needs Attention</strong><br>
                        Many students still need to complete their photo sessions.
                    </div>
                <?php endif; ?>
            </div>
        </div>
    </div>
</div>

<script>
document.addEventListener('DOMContentLoaded', function() {
    // Progress Chart
    const ctx = document.getElementById('progressChart').getContext('2d');
    new Chart(ctx, {
        type: 'doughnut',
        data: {
            labels: ['Completed', 'Pending'],
            datasets: [{
                data: [
                    <?php echo $reportData['summary']['total_completed']; ?>, 
                    <?php echo $reportData['summary']['pending']; ?>
                ],
                backgroundColor: ['#48c78e', '#ffdd57'],
                borderWidth: 0
            }]
        },
        options: {
            responsive: true,
            maintainAspectRatio: false,
            plugins: {
                legend: {
                    position: 'bottom'
                }
            }
        }
    });
});
</script>
