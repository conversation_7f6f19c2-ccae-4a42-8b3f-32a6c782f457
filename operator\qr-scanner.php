<?php
require_once '../config/config.php';
require_login();
?>
<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>QR Scanner - <?php echo APP_NAME; ?></title>
    <link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/bulma@0.9.4/css/bulma.min.css">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css">
    <script src="https://unpkg.com/html5-qrcode@2.3.8/html5-qrcode.min.js"></script>
    <style>
        .scanner-container {
            max-width: 500px;
            margin: 0 auto;
        }
        .scanner-box {
            border: 3px dashed #667eea;
            border-radius: 10px;
            padding: 20px;
            background: #f8f9fa;
        }
        #qr-reader {
            border-radius: 10px;
            overflow: hidden;
        }
        .scanner-controls {
            margin-top: 20px;
        }
        .back-button {
            position: fixed;
            top: 20px;
            left: 20px;
            z-index: 1000;
        }
        .status-message {
            position: fixed;
            top: 20px;
            right: 20px;
            z-index: 1000;
            max-width: 300px;
        }
    </style>
</head>
<body>
    <!-- Back Button -->
    <a href="dashboard.php" class="button back-button">
        <i class="fas fa-arrow-left mr-2"></i>
        Back to Dashboard
    </a>

    <!-- Status Messages -->
    <div id="status-message" class="status-message" style="display: none;"></div>

    <div class="container mt-6">
        <div class="scanner-container">
            <div class="box">
                <h1 class="title is-4 has-text-centered">
                    <i class="fas fa-qrcode mr-2"></i>
                    QR Code Scanner
                </h1>
                <p class="subtitle is-6 has-text-centered has-text-grey">
                    Position the QR code within the camera view
                </p>

                <div class="scanner-box">
                    <div id="qr-reader"></div>
                </div>

                <div class="scanner-controls has-text-centered">
                    <button id="start-button" class="button is-primary is-large" onclick="startScanner()">
                        <i class="fas fa-play mr-2"></i>
                        Start Scanner
                    </button>
                    <button id="stop-button" class="button is-danger is-large" onclick="stopScanner()" style="display: none;">
                        <i class="fas fa-stop mr-2"></i>
                        Stop Scanner
                    </button>
                </div>

                <!-- Camera Selection -->
                <div class="field mt-4">
                    <label class="label">Select Camera</label>
                    <div class="control">
                        <div class="select is-fullwidth">
                            <select id="camera-select">
                                <option value="">Loading cameras...</option>
                            </select>
                        </div>
                    </div>
                </div>

                <!-- Camera Issues Help -->
                <div class="box has-background-warning-light mt-4">
                    <h3 class="title is-6">📱 Mobile Camera Issues?</h3>
                    <div class="content is-small">
                        <p><strong>If camera doesn't work:</strong></p>
                        <ul>
                            <li>Make sure you granted camera permission</li>
                            <li>Try refreshing the page</li>
                            <li>Try a different browser (Chrome recommended)</li>
                            <li>For best results, use HTTPS connection</li>
                        </ul>
                    </div>
                </div>

                <!-- Manual Entry Fallback -->
                <div class="box has-background-light mt-4">
                    <h3 class="title is-6">Manual Entry</h3>
                    <p class="is-size-7 has-text-grey mb-3">
                        If QR scanning doesn't work, enter the invoice number manually:
                    </p>
                    <div class="field has-addons">
                        <div class="control is-expanded">
                            <input class="input" type="text" id="manual-invoice" placeholder="Enter invoice number (e.g., INV001)">
                        </div>
                        <div class="control">
                            <button class="button is-info" onclick="processManualEntry()">
                                <i class="fas fa-search mr-2"></i>
                                Lookup
                            </button>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <script>
        let html5QrcodeScanner = null;
        let isScanning = false;

        // Initialize camera list
        Html5Qrcode.getCameras().then(devices => {
            const cameraSelect = document.getElementById('camera-select');
            cameraSelect.innerHTML = '';

            if (devices && devices.length) {
                // Add default option
                const defaultOption = document.createElement('option');
                defaultOption.value = 'default';
                defaultOption.text = 'Default Camera';
                cameraSelect.appendChild(defaultOption);

                devices.forEach((device, index) => {
                    const option = document.createElement('option');
                    option.value = device.id;
                    option.text = device.label || `Camera ${index + 1}`;
                    cameraSelect.appendChild(option);
                });

                // Try to select back camera by default
                const backCamera = devices.find(device =>
                    device.label && (
                        device.label.toLowerCase().includes('back') ||
                        device.label.toLowerCase().includes('rear') ||
                        device.label.toLowerCase().includes('environment')
                    )
                );
                if (backCamera) {
                    cameraSelect.value = backCamera.id;
                } else {
                    // If no back camera found, select default
                    cameraSelect.value = 'default';
                }
            } else {
                // If no cameras enumerated, still provide default option
                const defaultOption = document.createElement('option');
                defaultOption.value = 'default';
                defaultOption.text = 'Default Camera';
                cameraSelect.appendChild(defaultOption);
                cameraSelect.value = 'default';
            }
        }).catch(err => {
            console.error('Error getting cameras:', err);
            // Fallback: provide default camera option
            const cameraSelect = document.getElementById('camera-select');
            cameraSelect.innerHTML = '';
            const defaultOption = document.createElement('option');
            defaultOption.value = 'default';
            defaultOption.text = 'Default Camera (Fallback)';
            cameraSelect.appendChild(defaultOption);
            cameraSelect.value = 'default';
        });

        function startScanner() {
            const cameraSelect = document.getElementById('camera-select');
            const selectedCameraId = cameraSelect.value;

            if (!selectedCameraId) {
                showMessage('Please select a camera first', 'is-warning');
                return;
            }

            const config = {
                fps: 10,
                qrbox: { width: 250, height: 250 },
                aspectRatio: 1.0
            };

            html5QrcodeScanner = new Html5Qrcode("qr-reader");

            // Use different camera selection methods
            let cameraToUse;
            if (selectedCameraId === 'default') {
                // Use environment facing camera (back camera on mobile)
                cameraToUse = { facingMode: "environment" };
            } else {
                // Use specific camera ID
                cameraToUse = selectedCameraId;
            }

            html5QrcodeScanner.start(
                cameraToUse,
                config,
                onScanSuccess,
                onScanFailure
            ).then(() => {
                isScanning = true;
                document.getElementById('start-button').style.display = 'none';
                document.getElementById('stop-button').style.display = 'inline-block';
                showMessage('Scanner started. Point camera at QR code', 'is-info');
            }).catch(err => {
                console.error('Error starting scanner:', err);
                showMessage('Failed to start scanner. Try a different camera or check permissions.', 'is-danger');

                // If default camera fails, try with user facing camera
                if (selectedCameraId === 'default') {
                    setTimeout(() => {
                        tryUserFacingCamera();
                    }, 2000);
                }
            });
        }

        function tryUserFacingCamera() {
            showMessage('Trying front camera...', 'is-info');
            const config = {
                fps: 10,
                qrbox: { width: 250, height: 250 },
                aspectRatio: 1.0
            };

            html5QrcodeScanner.start(
                { facingMode: "user" },
                config,
                onScanSuccess,
                onScanFailure
            ).then(() => {
                isScanning = true;
                document.getElementById('start-button').style.display = 'none';
                document.getElementById('stop-button').style.display = 'inline-block';
                showMessage('Scanner started with front camera. Point camera at QR code', 'is-info');
            }).catch(err => {
                console.error('Front camera also failed:', err);
                showMessage('Camera access failed. Please check permissions and try manual entry.', 'is-danger');
            });
        }

        function stopScanner() {
            if (html5QrcodeScanner && isScanning) {
                html5QrcodeScanner.stop().then(() => {
                    isScanning = false;
                    document.getElementById('start-button').style.display = 'inline-block';
                    document.getElementById('stop-button').style.display = 'none';
                    showMessage('Scanner stopped', 'is-info');
                }).catch(err => {
                    console.error('Error stopping scanner:', err);
                });
            }
        }

        function onScanSuccess(decodedText, decodedResult) {
            console.log('QR Code scanned:', decodedText);
            
            // Stop scanner
            stopScanner();
            
            // Show success message
            showMessage('QR Code detected! Redirecting...', 'is-success');
            
            // Extract invoice number from QR code
            // Assuming QR code contains just the invoice number
            const invoiceNumber = decodedText.trim();
            
            // Redirect to student lookup
            setTimeout(() => {
                window.location.href = `student-lookup.php?invoice=${encodeURIComponent(invoiceNumber)}`;
            }, 1000);
        }

        function onScanFailure(error) {
            // This is called continuously while scanning, so we don't show errors
            // console.log('Scan failed:', error);
        }

        function processManualEntry() {
            const invoiceNumber = document.getElementById('manual-invoice').value.trim();
            if (!invoiceNumber) {
                showMessage('Please enter an invoice number', 'is-warning');
                return;
            }
            
            showMessage('Looking up invoice...', 'is-info');
            window.location.href = `student-lookup.php?invoice=${encodeURIComponent(invoiceNumber)}`;
        }

        function showMessage(message, type) {
            const statusDiv = document.getElementById('status-message');
            statusDiv.className = `notification ${type} status-message`;
            statusDiv.innerHTML = `
                <button class="delete" onclick="hideMessage()"></button>
                ${message}
            `;
            statusDiv.style.display = 'block';
            
            // Auto-hide after 5 seconds
            setTimeout(hideMessage, 5000);
        }

        function hideMessage() {
            document.getElementById('status-message').style.display = 'none';
        }

        // Allow Enter key for manual entry
        document.getElementById('manual-invoice').addEventListener('keypress', function(e) {
            if (e.key === 'Enter') {
                processManualEntry();
            }
        });

        // Cleanup on page unload
        window.addEventListener('beforeunload', function() {
            if (html5QrcodeScanner && isScanning) {
                html5QrcodeScanner.stop();
            }
        });
    </script>
</body>
</html>
