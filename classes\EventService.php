<?php

class EventService {
    private $pdo;
    
    public function __construct() {
        $this->pdo = DatabaseConfig::getUnifiedConnection();
    }
    
    /**
     * Get all active events
     */
    public function getAllEvents($includeInactive = false) {
        try {
            $whereClause = $includeInactive ? "" : "WHERE is_active = 1";
            
            $stmt = $this->pdo->prepare("
                SELECT 
                    e.id,
                    e.event_name,
                    e.event_code,
                    e.event_date,
                    e.description,
                    e.is_active,
                    e.created_at,
                    COUNT(s.id) as registered_count,
                    SUM(s.total_amount) as total_revenue
                FROM events e
                LEFT JOIN students s ON e.id = s.event_id
                {$whereClause}
                GROUP BY e.id, e.event_name, e.event_code, e.event_date, e.description, e.is_active, e.created_at
                ORDER BY e.event_date DESC
            ");
            $stmt->execute();
            return $stmt->fetchAll(PDO::FETCH_ASSOC);
        } catch (Exception $e) {
            error_log("Error getting events: " . $e->getMessage());
            return [];
        }
    }
    
    /**
     * Get event by ID
     */
    public function getEventById($id) {
        try {
            $stmt = $this->pdo->prepare("
                SELECT * FROM events WHERE id = ?
            ");
            $stmt->execute([$id]);
            return $stmt->fetch(PDO::FETCH_ASSOC);
        } catch (Exception $e) {
            error_log("Error getting event by ID: " . $e->getMessage());
            return null;
        }
    }
    
    /**
     * Get all photo types
     */
    public function getAllPhotoTypes($includeInactive = false) {
        try {
            $whereClause = $includeInactive ? "" : "WHERE is_active = 1";
            
            $stmt = $this->pdo->prepare("
                SELECT * FROM photo_types 
                {$whereClause}
                ORDER BY type_name
            ");
            $stmt->execute();
            return $stmt->fetchAll(PDO::FETCH_ASSOC);
        } catch (Exception $e) {
            error_log("Error getting photo types: " . $e->getMessage());
            return [];
        }
    }
    
    /**
     * Get photo pricing for event
     */
    public function getEventPhotoPricing($eventId) {
        try {
            $stmt = $this->pdo->prepare("
                SELECT 
                    epp.id,
                    epp.event_id,
                    epp.photo_type_id,
                    epp.price,
                    epp.currency,
                    epp.is_active,
                    pt.type_name,
                    pt.type_code,
                    pt.description
                FROM event_photo_pricing epp
                JOIN photo_types pt ON epp.photo_type_id = pt.id
                WHERE epp.event_id = ? AND epp.is_active = 1
                ORDER BY pt.type_name
            ");
            $stmt->execute([$eventId]);
            return $stmt->fetchAll(PDO::FETCH_ASSOC);
        } catch (Exception $e) {
            error_log("Error getting event photo pricing: " . $e->getMessage());
            return [];
        }
    }
    
    /**
     * Update photo pricing
     */
    public function updatePhotoPricing($eventId, $photoTypeId, $price) {
        try {
            $stmt = $this->pdo->prepare("
                INSERT INTO event_photo_pricing (event_id, photo_type_id, price, currency, is_active)
                VALUES (?, ?, ?, 'LKR', 1)
                ON DUPLICATE KEY UPDATE 
                price = VALUES(price),
                updated_at = CURRENT_TIMESTAMP
            ");
            $stmt->execute([$eventId, $photoTypeId, $price]);
            return true;
        } catch (Exception $e) {
            error_log("Error updating photo pricing: " . $e->getMessage());
            return false;
        }
    }
    
    /**
     * Get all delivery methods
     */
    public function getAllDeliveryMethods($includeInactive = false) {
        try {
            $whereClause = $includeInactive ? "" : "WHERE is_active = 1";
            
            $stmt = $this->pdo->prepare("
                SELECT * FROM delivery_methods 
                {$whereClause}
                ORDER BY method_name
            ");
            $stmt->execute();
            return $stmt->fetchAll(PDO::FETCH_ASSOC);
        } catch (Exception $e) {
            error_log("Error getting delivery methods: " . $e->getMessage());
            return [];
        }
    }
    
    /**
     * Create new event
     */
    public function createEvent($data) {
        try {
            $stmt = $this->pdo->prepare("
                INSERT INTO events (event_name, event_code, event_date, description, is_active)
                VALUES (?, ?, ?, ?, ?)
            ");
            $stmt->execute([
                $data['event_name'],
                $data['event_code'],
                $data['event_date'],
                $data['description'],
                $data['is_active'] ?? 1
            ]);
            return $this->pdo->lastInsertId();
        } catch (Exception $e) {
            error_log("Error creating event: " . $e->getMessage());
            return false;
        }
    }
    
    /**
     * Update event
     */
    public function updateEvent($id, $data) {
        try {
            $stmt = $this->pdo->prepare("
                UPDATE events 
                SET event_name = ?, event_code = ?, event_date = ?, description = ?, is_active = ?, updated_at = CURRENT_TIMESTAMP
                WHERE id = ?
            ");
            $stmt->execute([
                $data['event_name'],
                $data['event_code'],
                $data['event_date'],
                $data['description'],
                $data['is_active'] ?? 1,
                $id
            ]);
            return $stmt->rowCount() > 0;
        } catch (Exception $e) {
            error_log("Error updating event: " . $e->getMessage());
            return false;
        }
    }
    
    /**
     * Get event sessions
     */
    public function getEventSessions($eventId, $includeInactive = false) {
        try {
            $whereClause = $includeInactive ? "" : "AND es.is_active = 1";

            $stmt = $this->pdo->prepare("
                SELECT
                    es.*,
                    COUNT(s.id) as registered_count
                FROM event_sessions es
                LEFT JOIN students s ON es.id = s.session_id
                WHERE es.event_id = ? {$whereClause}
                GROUP BY es.id
                ORDER BY es.session_date, es.session_time
            ");
            $stmt->execute([$eventId]);
            return $stmt->fetchAll(PDO::FETCH_ASSOC);
        } catch (Exception $e) {
            error_log("Error getting event sessions: " . $e->getMessage());
            return [];
        }
    }

    /**
     * Get session by ID
     */
    public function getSessionById($sessionId) {
        try {
            $stmt = $this->pdo->prepare("
                SELECT es.*, e.event_name
                FROM event_sessions es
                JOIN events e ON es.event_id = e.id
                WHERE es.id = ?
            ");
            $stmt->execute([$sessionId]);
            return $stmt->fetch(PDO::FETCH_ASSOC);
        } catch (Exception $e) {
            error_log("Error getting session by ID: " . $e->getMessage());
            return null;
        }
    }

    /**
     * Create new session
     */
    public function createSession($data) {
        try {
            $stmt = $this->pdo->prepare("
                INSERT INTO event_sessions (event_id, session_name, session_code, session_date, session_time, venue, max_capacity, description, is_active)
                VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?)
            ");
            $stmt->execute([
                $data['event_id'],
                $data['session_name'],
                $data['session_code'],
                $data['session_date'],
                $data['session_time'],
                $data['venue'],
                $data['max_capacity'],
                $data['description'],
                $data['is_active'] ?? 1
            ]);
            return $this->pdo->lastInsertId();
        } catch (Exception $e) {
            error_log("Error creating session: " . $e->getMessage());
            return false;
        }
    }

    /**
     * Update session
     */
    public function updateSession($sessionId, $data) {
        try {
            $stmt = $this->pdo->prepare("
                UPDATE event_sessions
                SET session_name = ?, session_code = ?, session_date = ?, session_time = ?,
                    venue = ?, max_capacity = ?, description = ?, is_active = ?, updated_at = CURRENT_TIMESTAMP
                WHERE id = ?
            ");
            $stmt->execute([
                $data['session_name'],
                $data['session_code'],
                $data['session_date'],
                $data['session_time'],
                $data['venue'],
                $data['max_capacity'],
                $data['description'],
                $data['is_active'] ?? 1,
                $sessionId
            ]);
            return $stmt->rowCount() > 0;
        } catch (Exception $e) {
            error_log("Error updating session: " . $e->getMessage());
            return false;
        }
    }

    /**
     * Get event statistics
     */
    public function getEventStatistics($eventId) {
        try {
            $stats = [];

            // Basic stats
            $stmt = $this->pdo->prepare("
                SELECT
                    COUNT(*) as total_registered,
                    SUM(CASE WHEN payment_status = 'paid' THEN 1 ELSE 0 END) as paid_count,
                    SUM(CASE WHEN payment_status = 'pending' THEN 1 ELSE 0 END) as pending_count,
                    SUM(total_amount) as total_revenue,
                    SUM(CASE WHEN payment_status = 'paid' THEN total_amount ELSE 0 END) as confirmed_revenue
                FROM students
                WHERE event_id = ?
            ");
            $stmt->execute([$eventId]);
            $stats = $stmt->fetch(PDO::FETCH_ASSOC);

            // Registration status breakdown
            $stmt = $this->pdo->prepare("
                SELECT registration_status, COUNT(*) as count
                FROM students
                WHERE event_id = ?
                GROUP BY registration_status
            ");
            $stmt->execute([$eventId]);
            $status_breakdown = $stmt->fetchAll(PDO::FETCH_ASSOC);

            $stats['status_breakdown'] = [];
            foreach ($status_breakdown as $status) {
                $stats['status_breakdown'][$status['registration_status']] = $status['count'];
            }

            return $stats;

        } catch (Exception $e) {
            error_log("Error getting event statistics: " . $e->getMessage());
            return [];
        }
    }
}
