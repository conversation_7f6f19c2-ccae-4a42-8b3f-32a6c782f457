-- Local Database Schema for Photo Session Management System
-- This database stores operators, photo sessions, and system data

CREATE DATABASE IF NOT EXISTS photo_session_management;
USE photo_session_management;

-- Operators table for system users
CREATE TABLE operators (
    id INT AUTO_INCREMENT PRIMARY KEY,
    username VARCHAR(50) UNIQUE NOT NULL,
    password_hash VARCHAR(255) NOT NULL,
    full_name VARCHAR(100) NOT NULL,
    role ENUM('admin', 'operator') NOT NULL DEFAULT 'operator',
    is_active BOOLEAN DEFAULT TRUE,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    last_login TIMESTAMP NULL,
    created_by INT,
    FOREIGN KEY (created_by) REFERENCES operators(id) ON DELETE SET NULL
);

-- Photo sessions table to track completed sessions
CREATE TABLE photo_sessions (
    id INT AUTO_INCREMENT PRIMARY KEY,
    invoice_number VARCHAR(50) NOT NULL,
    student_nic VARCHAR(20) NOT NULL,
    student_name VARCHAR(100) NOT NULL,
    student_address TEXT,
    whatsapp_number VARCHAR(20),
    alternate_phone VARCHAR(20),
    session VARCHAR(50),
    seat_number VARCHAR(20),
    photo_types JSON, -- Store selected photo types as JSON
    photo_sequence_number VARCHAR(20) NOT NULL,
    operator_id INT NOT NULL,
    completed_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    notes TEXT,
    FOREIGN KEY (operator_id) REFERENCES operators(id),
    UNIQUE KEY unique_invoice (invoice_number),
    INDEX idx_invoice (invoice_number),
    INDEX idx_student_nic (student_nic),
    INDEX idx_operator (operator_id),
    INDEX idx_completed_at (completed_at)
);

-- System settings table
CREATE TABLE system_settings (
    id INT AUTO_INCREMENT PRIMARY KEY,
    setting_key VARCHAR(100) UNIQUE NOT NULL,
    setting_value TEXT,
    description TEXT,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    updated_by INT,
    FOREIGN KEY (updated_by) REFERENCES operators(id) ON DELETE SET NULL
);

-- Session logs for security and audit
CREATE TABLE session_logs (
    id INT AUTO_INCREMENT PRIMARY KEY,
    operator_id INT,
    action VARCHAR(100) NOT NULL,
    details TEXT,
    ip_address VARCHAR(45),
    user_agent TEXT,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    FOREIGN KEY (operator_id) REFERENCES operators(id) ON DELETE SET NULL,
    INDEX idx_operator_action (operator_id, action),
    INDEX idx_created_at (created_at)
);

-- Insert default admin user (password: admin123)
INSERT INTO operators (username, password_hash, full_name, role, is_active) VALUES 
('admin', '$2y$10$92IXUNpkjO0rOQ5byMi.Ye4oKoEa3Ro9llC/.og/at2.uheWG/igi', 'System Administrator', 'admin', TRUE);

-- Insert default system settings
INSERT INTO system_settings (setting_key, setting_value, description) VALUES 
('external_db_host', 'localhost', 'External database host'),
('external_db_name', 'student_registration', 'External database name'),
('external_db_user', 'root', 'External database username'),
('external_db_pass', '', 'External database password'),
('session_timeout', '3600', 'Session timeout in seconds'),
('max_login_attempts', '5', 'Maximum login attempts before lockout'),
('system_name', 'University Convocation Photo Management', 'System display name');
