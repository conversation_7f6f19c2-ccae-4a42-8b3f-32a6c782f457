<?php
// Authentication check - must be first
require_once 'auth-check.php';
require_admin();

$message = '';
$message_type = '';

// Handle settings update
if ($_SERVER['REQUEST_METHOD'] === 'POST') {
    if (!verify_csrf_token($_POST['csrf_token'] ?? '')) {
        $message = 'Invalid security token';
        $message_type = 'is-danger';
    } else {
        try {
            $db = DatabaseConfig::getLocalConnection();
            
            // Update system settings
            $settings = [
                'external_db_host' => sanitize_input($_POST['external_db_host'] ?? ''),
                'external_db_name' => sanitize_input($_POST['external_db_name'] ?? ''),
                'external_db_user' => sanitize_input($_POST['external_db_user'] ?? ''),
                'external_db_pass' => $_POST['external_db_pass'] ?? '',
                'session_timeout' => intval($_POST['session_timeout'] ?? 3600),
                'max_login_attempts' => intval($_POST['max_login_attempts'] ?? 5),
                'system_name' => sanitize_input($_POST['system_name'] ?? ''),
                'courier_service_charge' => floatval($_POST['courier_service_charge'] ?? 500)
            ];
            
            foreach ($settings as $key => $value) {
                $stmt = $db->prepare("
                    INSERT INTO system_settings (setting_key, setting_value, updated_by) 
                    VALUES (?, ?, ?) 
                    ON DUPLICATE KEY UPDATE 
                    setting_value = VALUES(setting_value), 
                    updated_by = VALUES(updated_by),
                    updated_at = CURRENT_TIMESTAMP
                ");
                $stmt->execute([$key, $value, $_SESSION['user_id']]);
            }
            
            log_activity('SETTINGS_UPDATED', 'System settings updated');
            $message = 'Settings updated successfully';
            $message_type = 'is-success';
            
        } catch (Exception $e) {
            error_log("Settings update error: " . $e->getMessage());
            $message = 'Error updating settings';
            $message_type = 'is-danger';
        }
    }
}

// Get current settings
try {
    $db = DatabaseConfig::getLocalConnection();
    $stmt = $db->prepare("SELECT setting_key, setting_value FROM system_settings");
    $stmt->execute();
    $settings_data = $stmt->fetchAll();
    
    $settings = [];
    foreach ($settings_data as $setting) {
        $settings[$setting['setting_key']] = $setting['setting_value'];
    }
} catch (Exception $e) {
    $settings = [];
}

// Get system statistics
$operatorService = new OperatorService();
$photoSessionService = new PhotoSessionService();
$studentService = new StudentService();

$stats = [
    'total_operators' => $operatorService->getOperatorStatistics()['total_operators'] ?? 0,
    'total_sessions' => $photoSessionService->getCompletedSessionsCount(),
    'total_registered' => $studentService->getTotalRegisteredCount(),
    'database_size' => 'N/A' // Could be calculated if needed
];
?>
<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>System Settings - <?php echo APP_NAME; ?></title>
    <link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/bulma@0.9.4/css/bulma.min.css">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css">
    <style>
        .dashboard-header {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
        }
        .navbar-item.has-text-white:hover {
            background-color: rgba(255,255,255,0.1);
            color: white !important;
        }
        .navbar-item.is-active {
            background-color: rgba(255,255,255,0.2);
            font-weight: bold;
        }
        .navbar-dropdown {
            background-color: white;
            border: 1px solid #dbdbdb;
            box-shadow: 0 8px 16px rgba(10,10,10,.1);
        }
        .navbar-dropdown .navbar-item {
            color: #363636 !important;
            padding: 0.75rem 1rem;
        }
        .navbar-dropdown .navbar-item:hover {
            background-color: #f5f5f5;
            color: #363636 !important;
        }
        .navbar-dropdown .navbar-divider {
            background-color: #dbdbdb;
        }
        .stat-card .heading {
            color: #7a7a7a;
            font-weight: 600;
            text-transform: uppercase;
            font-size: 0.75rem;
        }
        .stat-card .title {
            color: #363636;
            font-weight: 700;
        }
        .navbar-brand .navbar-item {
            color: white !important;
            font-weight: bold;
        }
        .navbar-item.has-dropdown .navbar-link {
            color: white !important;
        }
        .navbar-item.has-dropdown .navbar-link:hover {
            background-color: rgba(255,255,255,0.05) !important;
            color: white !important;
        }
        .navbar-item.has-dropdown.is-active .navbar-link,
        .navbar-item.has-dropdown:hover .navbar-link {
            background-color: rgba(255,255,255,0.05) !important;
            color: white !important;
        }
        .navbar-link::after {
            border-color: white !important;
        }
        .settings-card {
            border-left: 4px solid #667eea;
        }
        .stat-card {
            border-left: 4px solid #48c78e;
        }
        .danger-zone {
            border-left: 4px solid #f14668;
            background: #fef5f5;
        }

        /* Custom select dropdown arrow positioning */
        .select:not(.is-multiple):not(.is-loading)::after {
            margin-top: -0.9em !important;
            transform: none !important;
        }
    </style>
</head>
<body>
    <!-- Navigation -->
    <nav class="navbar dashboard-header" role="navigation">
        <div class="navbar-brand">
            <div class="navbar-item" style="border-right: 1px solid #ccc; padding-right: 1rem; margin-right: 1rem;">
                <i class="fas fa-shield-alt mr-2" style="color: #ffffffff;"></i>
                <span style="font-weight: bold; color: #ffffffff;">Admin Dashboard</span>
            </div>
        </div>
        
        <div class="navbar-menu">
            <div class="navbar-start">
                <a class="navbar-item has-text-white" href="dashboard.php">
                    <i class="fas fa-tachometer-alt mr-2"></i>
                    Dashboard
                </a>
                <a class="navbar-item has-text-white" href="operators.php">
                    <i class="fas fa-users mr-2"></i>
                    Operators
                </a>
                <a class="navbar-item has-text-white" href="sessions.php">
                    <i class="fas fa-camera mr-2"></i>
                    Photo Sessions
                </a>
                <a class="navbar-item has-text-white" href="students.php">
                    <i class="fas fa-graduation-cap mr-2"></i>
                    Students
                </a>
                <a class="navbar-item has-text-white" href="photo-pricing.php">
                    <i class="fas fa-tags mr-2"></i>
                    Photo Pricing
                </a>
                <a class="navbar-item has-text-white" href="reports.php">
                    <i class="fas fa-chart-bar mr-2"></i>
                    Reports
                </a>
            </div>
            
            <div class="navbar-end">
                <div class="navbar-item has-dropdown is-hoverable">
                    <a class="navbar-link has-text-white">
                        <i class="fas fa-user-shield mr-2"></i>
                        <?php echo htmlspecialchars($_SESSION['full_name']); ?>
                    </a>
                    <div class="navbar-dropdown">
                        <a class="navbar-item" href="profile.php">
                            <i class="fas fa-user-cog mr-2"></i>
                            Profile
                        </a>
                        <a class="navbar-item is-active" href="settings.php">
                            <i class="fas fa-cog mr-2"></i>
                            Settings
                        </a>
                        <hr class="navbar-divider">
                        <a class="navbar-item" href="../logout.php">
                            <i class="fas fa-sign-out-alt mr-2"></i>
                            Logout
                        </a>
                    </div>
                </div>
            </div>
        </div>
    </nav>

    <div class="container mt-4">
        <!-- Header -->
        <div class="box">
            <h1 class="title is-4">
                <i class="fas fa-cog mr-2"></i>
                System Settings
            </h1>
            <p class="subtitle is-6">Configure system parameters and database connections</p>
        </div>

        <!-- Messages -->
        <?php if ($message): ?>
            <div class="notification <?php echo $message_type; ?>">
                <button class="delete" onclick="this.parentElement.style.display='none'"></button>
                <?php echo htmlspecialchars($message); ?>
            </div>
        <?php endif; ?>

        <div class="columns">
            <!-- Settings Form -->
            <div class="column is-8">
                <form method="POST" action="">
                    <input type="hidden" name="csrf_token" value="<?php echo generate_csrf_token(); ?>">
                    
                    <!-- General Settings -->
                    <div class="box settings-card">
                        <h2 class="title is-5">
                            <i class="fas fa-sliders-h mr-2"></i>
                            General Settings
                        </h2>
                        
                        <div class="field">
                            <label class="label">System Name</label>
                            <div class="control">
                                <input class="input" type="text" name="system_name" 
                                       value="<?php echo htmlspecialchars($settings['system_name'] ?? APP_NAME); ?>">
                            </div>
                            <p class="help">Display name for the system</p>
                        </div>
                        
                        <div class="columns">
                            <div class="column is-4">
                                <div class="field">
                                    <label class="label">Session Timeout (seconds)</label>
                                    <div class="control">
                                        <input class="input" type="number" name="session_timeout"
                                               value="<?php echo htmlspecialchars($settings['session_timeout'] ?? '3600'); ?>" min="300" max="86400">
                                    </div>
                                    <p class="help">User session timeout (300-86400 seconds)</p>
                                </div>
                            </div>
                            <div class="column is-4">
                                <div class="field">
                                    <label class="label">Max Login Attempts</label>
                                    <div class="control">
                                        <input class="input" type="number" name="max_login_attempts"
                                               value="<?php echo htmlspecialchars($settings['max_login_attempts'] ?? '5'); ?>" min="3" max="10">
                                    </div>
                                    <p class="help">Maximum failed login attempts before lockout</p>
                                </div>
                            </div>
                            <div class="column is-4">
                                <div class="field">
                                    <label class="label">Courier Service Charge (LKR)</label>
                                    <div class="control">
                                        <input class="input" type="number" name="courier_service_charge"
                                               value="<?php echo htmlspecialchars($settings['courier_service_charge'] ?? '500'); ?>" min="0" step="0.01">
                                    </div>
                                    <p class="help">Additional charge for courier delivery</p>
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- Database Settings -->
                    <div class="box settings-card">
                        <h2 class="title is-5">
                            <i class="fas fa-database mr-2"></i>
                            External Database Settings
                        </h2>
                        <p class="subtitle is-6 has-text-grey">Configure connection to student registration database</p>
                        
                        <div class="columns">
                            <div class="column is-6">
                                <div class="field">
                                    <label class="label">Database Host</label>
                                    <div class="control">
                                        <input class="input" type="text" name="external_db_host" 
                                               value="<?php echo htmlspecialchars($settings['external_db_host'] ?? 'localhost'); ?>">
                                    </div>
                                </div>
                            </div>
                            <div class="column is-6">
                                <div class="field">
                                    <label class="label">Database Name</label>
                                    <div class="control">
                                        <input class="input" type="text" name="external_db_name" 
                                               value="<?php echo htmlspecialchars($settings['external_db_name'] ?? 'student_registration'); ?>">
                                    </div>
                                </div>
                            </div>
                        </div>
                        
                        <div class="columns">
                            <div class="column is-6">
                                <div class="field">
                                    <label class="label">Database Username</label>
                                    <div class="control">
                                        <input class="input" type="text" name="external_db_user" 
                                               value="<?php echo htmlspecialchars($settings['external_db_user'] ?? 'root'); ?>">
                                    </div>
                                </div>
                            </div>
                            <div class="column is-6">
                                <div class="field">
                                    <label class="label">Database Password</label>
                                    <div class="control">
                                        <input class="input" type="password" name="external_db_pass" 
                                               value="<?php echo htmlspecialchars($settings['external_db_pass'] ?? ''); ?>" 
                                               placeholder="Leave blank if no password">
                                    </div>
                                </div>
                            </div>
                        </div>
                        
                        <div class="notification is-info is-light">
                            <i class="fas fa-info-circle mr-2"></i>
                            <strong>Note:</strong> Changes to database settings will take effect immediately. 
                            Make sure the database is accessible before saving.
                        </div>
                    </div>

                    <!-- Save Button -->
                    <div class="field">
                        <div class="control">
                            <button class="button is-primary is-large" type="submit">
                                <i class="fas fa-save mr-2"></i>
                                Save Settings
                            </button>
                        </div>
                    </div>
                </form>
            </div>
            
            <!-- System Information -->
            <div class="column is-4">
                <div class="box stat-card">
                    <h2 class="title is-5">
                        <i class="fas fa-info-circle mr-2"></i>
                        System Information
                    </h2>
                    
                    <div class="content">
                        <p><strong>Application:</strong> <?php echo APP_NAME; ?></p>
                        <p><strong>Version:</strong> <?php echo APP_VERSION; ?></p>
                        <p><strong>PHP Version:</strong> <?php echo phpversion(); ?></p>
                        <p><strong>Server:</strong> <?php echo $_SERVER['SERVER_SOFTWARE'] ?? 'Unknown'; ?></p>
                    </div>
                    
                    <hr>
                    
                    <div class="level is-mobile mb-3">
                        <div class="level-left">
                            <div>
                                <p class="heading">Total Operators</p>
                                <p class="title is-6"><?php echo $stats['total_operators']; ?></p>
                            </div>
                        </div>
                        <div class="level-right">
                            <i class="fas fa-users has-text-info"></i>
                        </div>
                    </div>
                    
                    <div class="level is-mobile mb-3">
                        <div class="level-left">
                            <div>
                                <p class="heading">Total Sessions</p>
                                <p class="title is-6"><?php echo $stats['total_sessions']; ?></p>
                            </div>
                        </div>
                        <div class="level-right">
                            <i class="fas fa-camera has-text-success"></i>
                        </div>
                    </div>
                    
                    <div class="level is-mobile">
                        <div class="level-left">
                            <div>
                                <p class="heading">Registered Students</p>
                                <p class="title is-6"><?php echo $stats['total_registered']; ?></p>
                            </div>
                        </div>
                        <div class="level-right">
                            <i class="fas fa-graduation-cap has-text-primary"></i>
                        </div>
                    </div>
                </div>
                
                <!-- Developer Information -->
                <div class="box">
                    <h2 class="title is-6">
                        <i class="fas fa-code mr-2"></i>
                        Developer Information
                    </h2>
                    <div class="content is-small">
                        <p><strong>Developed by:</strong><br>
                        Dr. Upanith Liyanaarachchi</p>
                        <p><strong>Contact:</strong><br>
                        <a href="mailto:<EMAIL>"><EMAIL></a></p>
                        <p><strong>Last Updated:</strong><br>
                        <?php echo date('F Y'); ?></p>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <script>
        // Auto-hide notifications after 5 seconds
        document.addEventListener('DOMContentLoaded', function() {
            const notifications = document.querySelectorAll('.notification');
            notifications.forEach(function(notification) {
                setTimeout(function() {
                    notification.style.display = 'none';
                }, 5000);
            });
        });
    </script>
</body>
</html>
