/**
 * Custom CSS Overrides for Photo Management System
 * This file contains custom styling that overrides Bulma defaults
 */

/* ========================================
   SELECT DROPDOWN ARROW POSITIONING
   ======================================== */

/* Custom select dropdown arrow positioning */
.select:not(.is-multiple):not(.is-loading)::after {
    margin-top: -0.9em !important;
    transform: none !important;
}

/* ========================================
   FORM ELEMENTS
   ======================================== */

/* Consistent font sizing for form elements */
.input, .select select, .textarea {
    font-size: 0.9rem;
}

/* ========================================
   STEP INDICATORS
   ======================================== */

/* Step indicator styling */
.step-indicator {
    display: flex;
    justify-content: space-between;
    margin-bottom: 2rem;
    padding: 0 1rem;
}

.step {
    flex: 1;
    text-align: center;
    padding: 0.75rem 0.5rem;
    border-radius: 8px;
    margin: 0 0.25rem;
}

.step.completed {
    background: #48c78e;
    color: white;
}

.step.active {
    background: #3273dc;
    color: white;
}

.step.pending {
    background: #f5f5f5;
    color: #7a7a7a;
}

.step-number {
    font-weight: bold;
    font-size: 0.9rem;
}

.step-label {
    font-size: 0.75rem;
    margin-top: 0.25rem;
}

/* ========================================
   MOBILE RESPONSIVENESS
   ======================================== */

@media screen and (max-width: 768px) {
    .step {
        padding: 0.5rem 0.25rem;
    }
    
    .step-label {
        font-size: 0.65rem;
    }
    
    .step-number {
        font-size: 0.8rem;
    }
}

/* ========================================
   UTILITY CLASSES
   ======================================== */

/* Additional utility classes for consistent styling */
.has-equal-height {
    display: flex;
    align-items: stretch;
}

.has-equal-height .column {
    display: flex;
    flex-direction: column;
}

.has-equal-height .box {
    flex: 1;
}

/* ========================================
   PRINT STYLES
   ======================================== */

@media print {
    .no-print {
        display: none !important;
    }
    
    .hero-section {
        background: white !important;
        color: black !important;
    }
    
    .box, .card {
        box-shadow: none !important;
        border: 1px solid #ddd !important;
    }
}

/* ========================================
   ANIMATION UTILITIES
   ======================================== */

@keyframes fadeInDown {
    from {
        opacity: 0;
        transform: translateY(-20px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

@keyframes bounce {
    0%, 20%, 50%, 80%, 100% {
        transform: translateY(0);
    }
    40% {
        transform: translateY(-10px);
    }
    60% {
        transform: translateY(-5px);
    }
}

.animate-fade-in {
    animation: fadeInDown 1s ease-out;
}

.animate-bounce {
    animation: bounce 2s infinite;
}
