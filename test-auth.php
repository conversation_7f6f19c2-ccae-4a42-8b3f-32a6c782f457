<?php
/**
 * Authentication Test Script
 * This script helps test the authentication protection
 * Remove this file after testing
 */

session_start();
require_once 'config/config.php';

echo "<h2>Authentication Test Results</h2>";

echo "<h3>Session Information:</h3>";
echo "<pre>";
echo "Session ID: " . session_id() . "\n";
echo "User ID: " . ($_SESSION['user_id'] ?? 'Not set') . "\n";
echo "Username: " . ($_SESSION['username'] ?? 'Not set') . "\n";
echo "Role: " . ($_SESSION['role'] ?? 'Not set') . "\n";
echo "Last Activity: " . ($_SESSION['last_activity'] ?? 'Not set') . "\n";
echo "</pre>";

echo "<h3>Authentication Status:</h3>";
echo "<pre>";
echo "is_logged_in(): " . (is_logged_in() ? 'TRUE' : 'FALSE') . "\n";
echo "is_admin(): " . (is_admin() ? 'TRUE' : 'FALSE') . "\n";
echo "</pre>";

echo "<h3>Test Links:</h3>";
echo "<ul>";
echo "<li><a href='app/dashboard.php'>App Dashboard (Admin Only)</a></li>";
echo "<li><a href='operator/dashboard.php'>Operator Dashboard</a></li>";
echo "<li><a href='login.php'>Login Page</a></li>";
echo "<li><a href='logout.php'>Logout</a></li>";
echo "</ul>";

echo "<h3>Security Headers Test:</h3>";
echo "<pre>";
$headers = getallheaders();
foreach ($headers as $name => $value) {
    if (stripos($name, 'x-') === 0 || stripos($name, 'security') !== false) {
        echo "$name: $value\n";
    }
}
echo "</pre>";

echo "<p><strong>Note:</strong> Delete this file after testing for security reasons.</p>";
?>
