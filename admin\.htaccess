# Security Configuration for Admin Directory
# This provides additional server-level protection

# Deny access to sensitive files
<Files "auth-check.php">
    Order Allow,<PERSON>y
    Den<PERSON> from all
</Files>

<Files "*.log">
    Order Allow,Deny
    Deny from all
</Files>

<Files "*.bak">
    Order Allow,<PERSON>y
    <PERSON> from all
</Files>

<Files "*.backup">
    Order Allow,<PERSON>y
    <PERSON> from all
</Files>

# Prevent directory browsing
Options -Indexes

# Security headers
<IfModule mod_headers.c>
    # Prevent clickjacking
    Header always append X-Frame-Options SAMEORIGIN
    
    # Prevent MIME type sniffing
    Header always set X-Content-Type-Options nosniff
    
    # Enable XSS protection
    Header always set X-XSS-Protection "1; mode=block"
    
    # Referrer policy
    Header always set Referrer-Policy "strict-origin-when-cross-origin"
</IfModule>

# Cache control for PHP files (prevent caching of sensitive content)
<FilesMatch "\.(php)$">
    <IfModule mod_headers.c>
        Header set Cache-Control "no-cache, no-store, must-revalidate"
        Header set <PERSON>ragma "no-cache"
        Header set Expires 0
    </IfModule>
</FilesMatch>

# Redirect HTTP to HTTPS (if SSL is available)
# Uncomment the following lines if you have SSL configured
# RewriteEngine On
# RewriteCond %{HTTPS} off
# RewriteRule ^(.*)$ https://%{HTTP_HOST}%{REQUEST_URI} [L,R=301]

# Block common attack patterns
<IfModule mod_rewrite.c>
    RewriteEngine On
    
    # Block SQL injection attempts
    RewriteCond %{QUERY_STRING} (\<|%3C).*script.*(\>|%3E) [NC,OR]
    RewriteCond %{QUERY_STRING} GLOBALS(=|\[|\%[0-9A-Z]{0,2}) [OR]
    RewriteCond %{QUERY_STRING} _REQUEST(=|\[|\%[0-9A-Z]{0,2}) [OR]
    RewriteCond %{QUERY_STRING} (\<|%3C).*iframe.*(\>|%3E) [NC,OR]
    RewriteCond %{QUERY_STRING} (\<|%3C).*object.*(\>|%3E) [NC,OR]
    RewriteCond %{QUERY_STRING} (\<|%3C).*embed.*(\>|%3E) [NC,OR]
    RewriteCond %{QUERY_STRING} (SELECT|UNION|INSERT|DROP|DELETE|UPDATE|CREATE|ALTER) [NC]
    RewriteRule ^(.*)$ - [F,L]
</IfModule>

# Error pages (optional)
# ErrorDocument 403 /error/403.php
# ErrorDocument 404 /error/404.php
# ErrorDocument 500 /error/500.php
