<?php
/**
 * Database Migration Script
 * Migrates existing photo management system to unified database structure
 */

// Security check - only allow from localhost or specific IPs
$allowed_ips = ['127.0.0.1', '::1', 'localhost'];
if (!in_array($_SERVER['REMOTE_ADDR'], $allowed_ips) && !in_array($_SERVER['HTTP_HOST'], ['localhost', '127.0.0.1'])) {
    die('Access denied. Migration can only be run from localhost.');
}

// Include configuration
require_once '../config/database.php';

// Set execution time limit
set_time_limit(300); // 5 minutes

$migration_log = [];
$errors = [];

function log_message($message, $type = 'info') {
    global $migration_log;
    $migration_log[] = [
        'time' => date('Y-m-d H:i:s'),
        'type' => $type,
        'message' => $message
    ];
    
    if ($type === 'error') {
        global $errors;
        $errors[] = $message;
    }
}

function execute_sql_file($pdo, $file_path) {
    if (!file_exists($file_path)) {
        log_message("SQL file not found: {$file_path}", 'error');
        return false;
    }

    $sql = file_get_contents($file_path);
    if ($sql === false) {
        log_message("Could not read SQL file: {$file_path}", 'error');
        return false;
    }

    // Split SQL into individual statements
    $statements = array_filter(array_map('trim', explode(';', $sql)));

    $success_count = 0;
    $error_count = 0;
    $warning_count = 0;

    foreach ($statements as $statement) {
        if (empty($statement) || strpos($statement, '--') === 0 || strpos($statement, 'SELECT ') === 0) {
            continue; // Skip empty statements, comments, and SELECT statements
        }

        try {
            $pdo->exec($statement);
            $success_count++;

            // Log important operations
            if (stripos($statement, 'CREATE DATABASE') !== false) {
                log_message("Database created successfully", 'success');
            } elseif (stripos($statement, 'CREATE TABLE') !== false) {
                preg_match('/CREATE TABLE.*?`?(\w+)`?/i', $statement, $matches);
                if (isset($matches[1])) {
                    log_message("Created table: {$matches[1]}", 'info');
                }
            } elseif (stripos($statement, 'INSERT') !== false) {
                preg_match('/INSERT.*?INTO.*?`?(\w+)`?/i', $statement, $matches);
                if (isset($matches[1])) {
                    log_message("Inserted data into: {$matches[1]}", 'info');
                }
            }

        } catch (PDOException $e) {
            // Check if it's a non-critical error
            if (strpos($e->getMessage(), 'already exists') !== false ||
                strpos($e->getMessage(), 'Duplicate entry') !== false) {
                $warning_count++;
                log_message("Warning (non-critical): " . $e->getMessage(), 'warning');
            } else {
                $error_count++;
                log_message("SQL Error: " . $e->getMessage() . " | Statement: " . substr($statement, 0, 100) . "...", 'error');
            }
        }
    }

    $total_processed = $success_count + $error_count + $warning_count;
    log_message("Processed {$total_processed} statements: {$success_count} successful, {$warning_count} warnings, {$error_count} errors",
                $error_count > 0 ? 'error' : ($warning_count > 0 ? 'warning' : 'success'));

    // Consider migration successful if there are no critical errors
    return $error_count === 0;
}

function backup_existing_data($pdo) {
    log_message("Starting data backup...", 'info');

    try {
        // First, check if the target database exists
        $stmt = $pdo->query("SHOW DATABASES LIKE '" . DB_NAME . "'");
        if ($stmt->rowCount() === 0) {
            log_message("Target database '" . DB_NAME . "' does not exist - creating it", 'info');
            $pdo->exec("CREATE DATABASE IF NOT EXISTS `" . DB_NAME . "` DEFAULT CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci");
            $pdo->exec("USE `" . DB_NAME . "`");
            log_message("Database '" . DB_NAME . "' created successfully", 'success');
            log_message("No existing data to backup (fresh installation)", 'info');
            return true;
        }

        // Use the database
        $pdo->exec("USE `" . DB_NAME . "`");

        // Check if old tables exist and backup data
        $tables_to_backup = ['users', 'photo_sessions', 'session_logs'];
        $backed_up_count = 0;

        foreach ($tables_to_backup as $table) {
            $stmt = $pdo->query("SHOW TABLES LIKE '{$table}'");
            if ($stmt->rowCount() > 0) {
                // Get row count first
                $count_stmt = $pdo->query("SELECT COUNT(*) FROM {$table}");
                $row_count = $count_stmt->fetchColumn();

                if ($row_count > 0) {
                    // Rename table to backup
                    $backup_table = $table . '_backup_' . date('Y_m_d_H_i_s');
                    $pdo->exec("CREATE TABLE {$backup_table} AS SELECT * FROM {$table}");
                    log_message("Backed up {$table} ({$row_count} records) to {$backup_table}", 'success');
                    $backed_up_count++;
                } else {
                    log_message("Table {$table} exists but is empty - no backup needed", 'info');
                }
            } else {
                log_message("Table {$table} does not exist - no backup needed", 'info');
            }
        }

        if ($backed_up_count === 0) {
            log_message("No existing data found to backup (fresh installation)", 'info');
        } else {
            log_message("Successfully backed up {$backed_up_count} tables", 'success');
        }

        return true;
    } catch (Exception $e) {
        log_message("Backup error: " . $e->getMessage(), 'error');

        // If it's a "no database selected" error, try to create the database
        if (strpos($e->getMessage(), 'No database selected') !== false ||
            strpos($e->getMessage(), 'Unknown database') !== false) {
            try {
                log_message("Attempting to create database '" . DB_NAME . "'", 'info');
                $pdo->exec("CREATE DATABASE IF NOT EXISTS `" . DB_NAME . "` DEFAULT CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci");
                $pdo->exec("USE `" . DB_NAME . "`");
                log_message("Database created successfully - proceeding with fresh installation", 'success');
                return true;
            } catch (Exception $e2) {
                log_message("Failed to create database: " . $e2->getMessage(), 'error');
                return false;
            }
        }

        return false;
    }
}

function verify_migration($pdo) {
    log_message("Verifying migration...", 'info');
    
    $required_tables = [
        'operators', 'events', 'photo_types', 'event_photo_pricing', 
        'delivery_methods', 'students', 'student_photo_orders', 
        'photo_sessions', 'session_logs', 'system_settings'
    ];
    
    $missing_tables = [];
    
    foreach ($required_tables as $table) {
        $stmt = $pdo->query("SHOW TABLES LIKE '{$table}'");
        if ($stmt->rowCount() === 0) {
            $missing_tables[] = $table;
        }
    }
    
    if (empty($missing_tables)) {
        log_message("All required tables created successfully", 'success');
        
        // Check data counts
        foreach (['operators', 'events', 'photo_types', 'delivery_methods'] as $table) {
            $stmt = $pdo->query("SELECT COUNT(*) FROM {$table}");
            $count = $stmt->fetchColumn();
            log_message("{$table}: {$count} records", 'info');
        }
        
        return true;
    } else {
        log_message("Missing tables: " . implode(', ', $missing_tables), 'error');
        return false;
    }
}

// Start migration process
if ($_SERVER['REQUEST_METHOD'] === 'POST' && isset($_POST['start_migration'])) {
    log_message("Starting database migration...", 'info');
    
    try {
        // Connect to MySQL server without specifying database first
        $pdo = new PDO(
            "mysql:host=" . DB_HOST . ";charset=utf8mb4",
            DB_USER,
            DB_PASS,
            [
                PDO::ATTR_ERRMODE => PDO::ERRMODE_EXCEPTION,
                PDO::ATTR_DEFAULT_FETCH_MODE => PDO::FETCH_ASSOC,
                PDO::MYSQL_ATTR_INIT_COMMAND => "SET NAMES utf8mb4"
            ]
        );

        log_message("MySQL server connection established", 'success');
        
        // Step 1: Backup existing data
        if (!backup_existing_data($pdo)) {
            log_message("Backup failed, stopping migration", 'error');
        } else {
            // Step 2: Execute migration SQL
            log_message("Executing migration SQL...", 'info');
            if (execute_sql_file($pdo, __DIR__ . '/migrate_to_unified.sql')) {
                log_message("Migration SQL executed successfully", 'success');
                
                // Step 3: Verify migration
                if (verify_migration($pdo)) {
                    log_message("Migration completed successfully!", 'success');
                } else {
                    log_message("Migration verification failed", 'error');
                }
            } else {
                log_message("Migration SQL execution failed", 'error');
            }
        }
        
    } catch (Exception $e) {
        log_message("Migration failed: " . $e->getMessage(), 'error');
    }
}
?>
<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Database Migration - Photo Management System</title>
    <link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/bulma@0.9.4/css/bulma.min.css">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css">
    <style>
        .migration-header {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            padding: 2rem;
            border-radius: 8px;
            margin-bottom: 2rem;
        }
        .log-entry {
            padding: 0.5rem;
            margin-bottom: 0.25rem;
            border-radius: 4px;
            font-family: monospace;
            font-size: 0.875rem;
        }
        .log-info { background-color: #f0f8ff; }
        .log-success { background-color: #f0fff0; color: #006400; }
        .log-warning { background-color: #fff8dc; color: #b8860b; }
        .log-error { background-color: #ffe4e1; color: #dc143c; }
    </style>
</head>
<body>
    <div class="container mt-4">
        <!-- Header -->
        <div class="migration-header">
            <h1 class="title is-2 has-text-white">
                <i class="fas fa-database mr-3"></i>
                Database Migration
            </h1>
            <p class="subtitle is-4 has-text-white">
                Migrate to Unified Photo Management System
            </p>
        </div>

        <!-- Warning -->
        <div class="notification is-warning">
            <h2 class="title is-5">
                <i class="fas fa-exclamation-triangle mr-2"></i>
                Important Notice
            </h2>
            <div class="content">
                <ul>
                    <li><strong>Backup:</strong> This migration will automatically backup your existing data</li>
                    <li><strong>Downtime:</strong> The system may be unavailable during migration</li>
                    <li><strong>Testing:</strong> Run this on a test environment first</li>
                    <li><strong>Access:</strong> This script only works from localhost for security</li>
                </ul>
            </div>
        </div>

        <!-- Migration Status -->
        <?php if (!empty($migration_log)): ?>
            <div class="box">
                <h2 class="title is-5">
                    <i class="fas fa-list mr-2"></i>
                    Migration Log
                </h2>
                
                <?php if (empty($errors)): ?>
                    <div class="notification is-success">
                        <i class="fas fa-check-circle mr-2"></i>
                        Migration completed successfully!
                    </div>
                <?php else: ?>
                    <div class="notification is-danger">
                        <i class="fas fa-exclamation-circle mr-2"></i>
                        Migration completed with <?php echo count($errors); ?> error(s). Please review the log below.
                    </div>
                <?php endif; ?>
                
                <div style="max-height: 400px; overflow-y: auto; border: 1px solid #ddd; padding: 1rem;">
                    <?php foreach ($migration_log as $entry): ?>
                        <div class="log-entry log-<?php echo $entry['type']; ?>">
                            <strong>[<?php echo $entry['time']; ?>]</strong> 
                            <?php echo htmlspecialchars($entry['message']); ?>
                        </div>
                    <?php endforeach; ?>
                </div>
            </div>
        <?php endif; ?>

        <!-- Migration Form -->
        <?php if (empty($migration_log) || !empty($errors)): ?>
            <div class="box">
                <h2 class="title is-5">
                    <i class="fas fa-play mr-2"></i>
                    Start Migration
                </h2>
                
                <div class="content">
                    <p>This migration will:</p>
                    <ol>
                        <li>Create the unified database structure</li>
                        <li>Migrate existing operator and session data</li>
                        <li>Add new tables for student registration system</li>
                        <li>Insert default data (events, photo types, delivery methods)</li>
                        <li>Set up proper relationships and indexes</li>
                    </ol>
                </div>
                
                <form method="POST" action="">
                    <div class="field">
                        <div class="control">
                            <label class="checkbox">
                                <input type="checkbox" required>
                                I understand that this will modify the database structure and have backed up my data
                            </label>
                        </div>
                    </div>
                    
                    <div class="field">
                        <div class="control">
                            <button class="button is-primary is-large" type="submit" name="start_migration">
                                <i class="fas fa-rocket mr-2"></i>
                                Start Migration
                            </button>
                        </div>
                    </div>
                </form>
            </div>
        <?php endif; ?>

        <!-- Success Actions -->
        <?php if (!empty($migration_log) && empty($errors)): ?>
            <div class="box">
                <h2 class="title is-5">
                    <i class="fas fa-check-circle mr-2"></i>
                    Next Steps
                </h2>
                
                <div class="content">
                    <p>Migration completed successfully! You can now:</p>
                    <ul>
                        <li>Access the admin dashboard to manage the system</li>
                        <li>Set up photo pricing for your events</li>
                        <li>Test the student registration portal</li>
                        <li>Configure payment gateway settings</li>
                    </ul>
                </div>
                
                <div class="buttons">
                    <a href="../admin/dashboard.php" class="button is-primary">
                        <i class="fas fa-tachometer-alt mr-2"></i>
                        Admin Dashboard
                    </a>
                    <a href="../student/index.php" class="button is-info">
                        <i class="fas fa-user-graduate mr-2"></i>
                        Student Portal
                    </a>
                    <a href="../admin/photo-pricing.php" class="button is-success">
                        <i class="fas fa-tags mr-2"></i>
                        Setup Pricing
                    </a>
                </div>
            </div>
        <?php endif; ?>

        <!-- Footer -->
        <div class="has-text-centered mt-6 mb-4">
            <p class="has-text-grey">
                <i class="fas fa-shield-alt mr-2"></i>
                Photo Management System - Database Migration Tool
            </p>
        </div>
    </div>
</body>
</html>
