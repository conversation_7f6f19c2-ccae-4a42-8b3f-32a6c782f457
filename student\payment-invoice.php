<?php
require_once '../config/config.php';

if (session_status() === PHP_SESSION_NONE) {
    session_start();
}

// Check if payment success data exists
if (!isset($_SESSION['payment_success'])) {
    redirect('payment.php');
}

$payment_data = $_SESSION['payment_success'];
$invoice_number = $payment_data['invoice_number'];

// Get detailed student and order data
$student_data = null;
$photo_orders = [];

try {
    $pdo = get_pdo_connection();
    
    // Get student details
    $stmt = $pdo->prepare("
        SELECT s.*, e.event_name, e.event_date, es.session_name, es.session_date, es.session_time, es.venue,
               dm.method_name as delivery_method
        FROM students s
        LEFT JOIN events e ON s.event_id = e.id
        LEFT JOIN event_sessions es ON s.session_id = es.id
        LEFT JOIN delivery_methods dm ON s.delivery_method_id = dm.id
        WHERE s.invoice_number = ?
    ");
    $stmt->execute([$invoice_number]);
    $student_data = $stmt->fetch(PDO::FETCH_ASSOC);
    
    // Get photo orders
    $stmt = $pdo->prepare("
        SELECT po.*, pt.type_name, pt.description
        FROM photo_orders po
        LEFT JOIN photo_types pt ON po.photo_type_id = pt.id
        WHERE po.student_id = ?
    ");
    $stmt->execute([$student_data['id']]);
    $photo_orders = $stmt->fetchAll(PDO::FETCH_ASSOC);
    
} catch (Exception $e) {
    error_log("Invoice data loading error: " . $e->getMessage());
    redirect('payment.php');
}

// Clear the success data after loading
unset($_SESSION['payment_success']);
unset($_SESSION['selected_payment_method']);
unset($_SESSION['payment_student_data']);
unset($_SESSION['payment_invoice']);
unset($_SESSION['payment_attempt_made']);
?>
<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Invoice - <?php echo APP_NAME; ?></title>
    <link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/bulma@0.9.4/css/bulma.min.css">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css">
    <style>
        .hero-section {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
        }
        .invoice-card {
            background: white;
            border-radius: 15px;
            box-shadow: 0 10px 30px rgba(0,0,0,0.1);
            overflow: hidden;
        }
        .step-indicator {
            display: flex;
            justify-content: space-between;
            margin-bottom: 2rem;
            padding: 0 1rem;
        }
        .step {
            flex: 1;
            text-align: center;
            padding: 0.75rem 0.5rem;
            border-radius: 8px;
            margin: 0 0.25rem;
            position: relative;
        }
        .step.completed {
            background: #48c78e;
            color: white;
        }
        .step.active {
            background: #3273dc;
            color: white;
        }
        .step.pending {
            background: #f5f5f5;
            color: #7a7a7a;
        }
        .step-number {
            font-weight: bold;
            font-size: 0.9rem;
        }
        .step-label {
            font-size: 0.75rem;
            margin-top: 0.25rem;
        }
        .invoice-header {
            background: linear-gradient(135deg, #3273dc 0%, #667eea 100%);
            color: white;
            padding: 2rem;
            text-align: center;
        }
        .invoice-details {
            padding: 2rem;
        }
        .invoice-table th {
            background-color: #f8f9fa;
        }
        @media print {
            .no-print {
                display: none;
            }
            .hero-section {
                background: white !important;
            }
            .invoice-card {
                box-shadow: none;
                border: 1px solid #ddd;
            }
        }
        @media screen and (max-width: 768px) {
            .step {
                padding: 0.5rem 0.25rem;
            }
            .step-label {
                font-size: 0.65rem;
            }
        }
    </style>
</head>
<body>
    <section class="hero hero-section">
        <div class="hero-body">
            <div class="container">
                <!-- Header -->
                <div class="has-text-centered mb-6 no-print">
                    <h1 class="title is-2 has-text-white">
                        <i class="fas fa-file-invoice mr-3"></i>
                        Invoice
                    </h1>
                    <p class="subtitle is-4 has-text-white">
                        Your registration is complete
                    </p>
                </div>

                <!-- Invoice -->
                <div class="columns is-centered">
                    <div class="column is-10">
                        <div class="box invoice-card">
                            
                            <!-- Step Indicator -->
                            <div class="step-indicator no-print">
                                <div class="step completed">
                                    <div class="step-number">1</div>
                                    <div class="step-label">Verification</div>
                                </div>
                                <div class="step completed">
                                    <div class="step-number">2</div>
                                    <div class="step-label">Details</div>
                                </div>
                                <div class="step completed">
                                    <div class="step-number">3</div>
                                    <div class="step-label">Photos</div>
                                </div>
                                <div class="step completed">
                                    <div class="step-number">4</div>
                                    <div class="step-label">Review</div>
                                </div>
                                <div class="step completed">
                                    <div class="step-number">5</div>
                                    <div class="step-label">Payment</div>
                                </div>
                                <div class="step active">
                                    <div class="step-number">6</div>
                                    <div class="step-label">Invoice</div>
                                </div>
                            </div>
                            
                            <!-- Invoice Header -->
                            <div class="invoice-header">
                                <h2 class="title is-3 has-text-white">
                                    <i class="fas fa-receipt mr-2"></i>
                                    INVOICE
                                </h2>
                                <p class="subtitle is-5 has-text-white">
                                    <?php echo htmlspecialchars($invoice_number); ?>
                                </p>
                            </div>
                            
                            <!-- Invoice Details -->
                            <div class="invoice-details">
                                <div class="columns">
                                    <!-- Customer Details -->
                                    <div class="column is-6">
                                        <h3 class="title is-5">
                                            <i class="fas fa-user mr-2"></i>
                                            Customer Details
                                        </h3>
                                        <div class="content">
                                            <p><strong>Name:</strong> <?php echo htmlspecialchars($student_data['full_name']); ?></p>
                                            <p><strong>NIC:</strong> <?php echo htmlspecialchars($student_data['nic_number']); ?></p>
                                            <?php if (!empty($student_data['email'])): ?>
                                                <p><strong>Email:</strong> <?php echo htmlspecialchars($student_data['email']); ?></p>
                                            <?php endif; ?>
                                            <?php if (!empty($student_data['phone'])): ?>
                                                <p><strong>Phone:</strong> <?php echo htmlspecialchars($student_data['phone']); ?></p>
                                            <?php endif; ?>
                                        </div>
                                    </div>
                                    
                                    <!-- Event Details -->
                                    <div class="column is-6">
                                        <h3 class="title is-5">
                                            <i class="fas fa-calendar mr-2"></i>
                                            Event Details
                                        </h3>
                                        <div class="content">
                                            <p><strong>Event:</strong> <?php echo htmlspecialchars($student_data['event_name']); ?></p>
                                            <p><strong>Date:</strong> <?php echo date('F j, Y', strtotime($student_data['event_date'])); ?></p>
                                            <?php if (!empty($student_data['session_name'])): ?>
                                                <p><strong>Session:</strong> <?php echo htmlspecialchars($student_data['session_name']); ?></p>
                                                <p><strong>Time:</strong> <?php echo date('g:i A', strtotime($student_data['session_time'])); ?></p>
                                                <?php if (!empty($student_data['venue'])): ?>
                                                    <p><strong>Venue:</strong> <?php echo htmlspecialchars($student_data['venue']); ?></p>
                                                <?php endif; ?>
                                            <?php endif; ?>
                                        </div>
                                    </div>
                                </div>
                                
                                <!-- Order Details -->
                                <h3 class="title is-5">
                                    <i class="fas fa-shopping-cart mr-2"></i>
                                    Order Details
                                </h3>
                                
                                <div class="table-container">
                                    <table class="table is-fullwidth is-striped invoice-table">
                                        <thead>
                                            <tr>
                                                <th>Item</th>
                                                <th class="has-text-centered">Quantity</th>
                                                <th class="has-text-right">Unit Price</th>
                                                <th class="has-text-right">Total</th>
                                            </tr>
                                        </thead>
                                        <tbody>
                                            <?php foreach ($photo_orders as $order): ?>
                                                <tr>
                                                    <td>
                                                        <strong><?php echo htmlspecialchars($order['type_name']); ?></strong>
                                                        <?php if (!empty($order['description'])): ?>
                                                            <br><small class="has-text-grey"><?php echo htmlspecialchars($order['description']); ?></small>
                                                        <?php endif; ?>
                                                    </td>
                                                    <td class="has-text-centered"><?php echo $order['quantity']; ?></td>
                                                    <td class="has-text-right">LKR <?php echo number_format($order['unit_price'], 2); ?></td>
                                                    <td class="has-text-right"><strong>LKR <?php echo number_format($order['total_price'], 2); ?></strong></td>
                                                </tr>
                                            <?php endforeach; ?>
                                            
                                            <?php if ($student_data['delivery_method'] && stripos($student_data['delivery_method'], 'courier') !== false): ?>
                                                <tr>
                                                    <td><strong><?php echo htmlspecialchars($student_data['delivery_method']); ?></strong></td>
                                                    <td class="has-text-centered">1</td>
                                                    <td class="has-text-right">LKR <?php echo number_format($student_data['total_amount'] - array_sum(array_column($photo_orders, 'total_price')), 2); ?></td>
                                                    <td class="has-text-right"><strong>LKR <?php echo number_format($student_data['total_amount'] - array_sum(array_column($photo_orders, 'total_price')), 2); ?></strong></td>
                                                </tr>
                                            <?php endif; ?>
                                        </tbody>
                                        <tfoot>
                                            <tr class="has-background-primary-light">
                                                <th colspan="3" class="title is-5">Total Amount</th>
                                                <th class="title is-4 has-text-right has-text-primary">LKR <?php echo number_format($student_data['total_amount'], 2); ?></th>
                                            </tr>
                                        </tfoot>
                                    </table>
                                </div>
                                
                                <!-- Payment Information -->
                                <?php if ($payment_data['method'] === 'Pay at Event'): ?>
                                    <div class="notification is-warning is-light">
                                        <h4 class="title is-5">
                                            <i class="fas fa-calendar-check mr-2"></i>
                                            Payment Instructions - Pay at Event
                                        </h4>
                                        <div class="content">
                                            <p><strong>What to bring:</strong></p>
                                            <ul>
                                                <li>This invoice (printed or on mobile)</li>
                                                <li>Exact cash amount: <strong>LKR <?php echo number_format($student_data['total_amount'], 2); ?></strong></li>
                                                <li>Valid ID (NIC or Passport)</li>
                                            </ul>
                                            <p><strong>At the event:</strong></p>
                                            <ol>
                                                <li>Look for the "Photo Payment Counter"</li>
                                                <li>Show this invoice and your ID</li>
                                                <li>Pay the exact amount in cash</li>
                                                <li>Collect your payment receipt</li>
                                                <li>Proceed to your photo session</li>
                                            </ol>
                                        </div>
                                    </div>
                                <?php elseif ($payment_data['method'] === 'Bank Transfer'): ?>
                                    <div class="notification is-info is-light">
                                        <h4 class="title is-5">
                                            <i class="fas fa-university mr-2"></i>
                                            Bank Transfer - Under Verification
                                        </h4>
                                        <div class="content">
                                            <p><strong>Status:</strong> Your bank transfer slip has been uploaded and is being verified.</p>
                                            <p><strong>Processing Time:</strong> 1-2 business days</p>
                                            <p><strong>What's Next:</strong></p>
                                            <ol>
                                                <li>We will verify your bank transfer</li>
                                                <li>You'll receive email confirmation once verified</li>
                                                <li>Your booking will be confirmed</li>
                                                <li>Attend your scheduled photo session</li>
                                            </ol>
                                        </div>
                                    </div>
                                <?php elseif ($payment_data['method'] === 'Credit/Debit Card'): ?>
                                    <div class="notification is-success is-light">
                                        <h4 class="title is-5">
                                            <i class="fas fa-credit-card mr-2"></i>
                                            Payment Completed Successfully
                                        </h4>
                                        <div class="content">
                                            <p><strong>Status:</strong> Your payment has been processed successfully.</p>
                                            <p><strong>What's Next:</strong></p>
                                            <ol>
                                                <li>You'll receive email confirmation shortly</li>
                                                <li>Attend your scheduled photo session</li>
                                                <li>Photos will be delivered as per your selected method</li>
                                            </ol>
                                        </div>
                                    </div>
                                <?php endif; ?>
                                
                                <!-- Invoice Footer -->
                                <div class="has-text-centered mt-5">
                                    <p class="is-size-7 has-text-grey">
                                        Invoice generated on <?php echo date('F j, Y \a\t g:i A'); ?><br>
                                        Thank you for choosing our photo services!
                                    </p>
                                </div>
                            </div>
                            
                            <!-- Action Buttons -->
                            <div class="buttons is-centered no-print">
                                <button class="button is-primary is-large" onclick="window.print()">
                                    <i class="fas fa-print mr-2"></i>
                                    Print Invoice
                                </button>
                                
                                <a href="status-check.php" class="button is-info is-large">
                                    <i class="fas fa-search mr-2"></i>
                                    Check Status
                                </a>
                                
                                <a href="registration.php" class="button is-light is-large">
                                    <i class="fas fa-plus mr-2"></i>
                                    New Registration
                                </a>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </section>

    <script>
        // Auto-print option (optional)
        document.addEventListener('DOMContentLoaded', function() {
            // Add some celebration animation
            const invoiceHeader = document.querySelector('.invoice-header');
            if (invoiceHeader) {
                invoiceHeader.style.animation = 'fadeInDown 1s ease-out';
            }
        });
        
        // Add fade animation
        const style = document.createElement('style');
        style.textContent = `
            @keyframes fadeInDown {
                from {
                    opacity: 0;
                    transform: translateY(-20px);
                }
                to {
                    opacity: 1;
                    transform: translateY(0);
                }
            }
        `;
        document.head.appendChild(style);
    </script>
</body>
</html>
