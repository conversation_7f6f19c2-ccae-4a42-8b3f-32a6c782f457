<?php
/**
 * Database Configuration
 * Handles connections to both local and external databases
 */

class DatabaseConfig {
    // Local database configuration
    private static $local_config = [
        'host' => 'localhost',
        'dbname' => 'photo_session_management',
        'username' => 'root',
        'password' => '',
        'charset' => 'utf8mb4'
    ];
    
    // External database configuration (will be loaded from system settings)
    private static $external_config = [
        'host' => 'localhost',
        'dbname' => 'student_registration',
        'username' => 'root',
        'password' => '',
        'charset' => 'utf8mb4'
    ];
    
    private static $local_connection = null;
    private static $external_connection = null;
    
    /**
     * Get local database connection
     */
    public static function getLocalConnection() {
        if (self::$local_connection === null) {
            try {
                $dsn = "mysql:host=" . self::$local_config['host'] . 
                       ";dbname=" . self::$local_config['dbname'] . 
                       ";charset=" . self::$local_config['charset'];
                
                self::$local_connection = new PDO(
                    $dsn,
                    self::$local_config['username'],
                    self::$local_config['password'],
                    [
                        PDO::ATTR_ERRMODE => PDO::ERRMODE_EXCEPTION,
                        PDO::ATTR_DEFAULT_FETCH_MODE => PDO::FETCH_ASSOC,
                        PDO::ATTR_EMULATE_PREPARES => false
                    ]
                );
            } catch (PDOException $e) {
                error_log("Local database connection failed: " . $e->getMessage());
                throw new Exception("Database connection failed");
            }
        }
        return self::$local_connection;
    }
    
    /**
     * Get external database connection
     */
    public static function getExternalConnection() {
        if (self::$external_connection === null) {
            // Load external DB config from system settings
            self::loadExternalConfig();
            
            try {
                $dsn = "mysql:host=" . self::$external_config['host'] . 
                       ";dbname=" . self::$external_config['dbname'] . 
                       ";charset=" . self::$external_config['charset'];
                
                self::$external_connection = new PDO(
                    $dsn,
                    self::$external_config['username'],
                    self::$external_config['password'],
                    [
                        PDO::ATTR_ERRMODE => PDO::ERRMODE_EXCEPTION,
                        PDO::ATTR_DEFAULT_FETCH_MODE => PDO::FETCH_ASSOC,
                        PDO::ATTR_EMULATE_PREPARES => false
                    ]
                );
            } catch (PDOException $e) {
                error_log("External database connection failed: " . $e->getMessage());
                throw new Exception("External database connection failed");
            }
        }
        return self::$external_connection;
    }
    
    /**
     * Load external database configuration from system settings
     */
    private static function loadExternalConfig() {
        try {
            $local_db = self::getLocalConnection();
            $stmt = $local_db->prepare("
                SELECT setting_key, setting_value 
                FROM system_settings 
                WHERE setting_key IN ('external_db_host', 'external_db_name', 'external_db_user', 'external_db_pass')
            ");
            $stmt->execute();
            $settings = $stmt->fetchAll();
            
            foreach ($settings as $setting) {
                switch ($setting['setting_key']) {
                    case 'external_db_host':
                        self::$external_config['host'] = $setting['setting_value'];
                        break;
                    case 'external_db_name':
                        self::$external_config['dbname'] = $setting['setting_value'];
                        break;
                    case 'external_db_user':
                        self::$external_config['username'] = $setting['setting_value'];
                        break;
                    case 'external_db_pass':
                        self::$external_config['password'] = $setting['setting_value'];
                        break;
                }
            }
        } catch (Exception $e) {
            error_log("Failed to load external database config: " . $e->getMessage());
            // Use default values if loading fails
        }
    }
    
    /**
     * Close all database connections
     */
    public static function closeConnections() {
        self::$local_connection = null;
        self::$external_connection = null;
    }
}
?>
