<?php
/**
 * Database Configuration
 * Unified Photo Management System - Database Configuration
 * Handles connections to both local and external databases
 */

// =============================================
// UNIFIED DATABASE CONFIGURATION CONSTANTS
// =============================================

// Database Configuration Constants (for migration scripts)
define('DB_HOST', 'localhost');        // Database host (usually 'localhost' for XAMPP)
define('DB_USER', 'root');             // Database username (default 'root' for XAMPP)
define('DB_PASS', '');                 // Database password (usually empty for XAMPP)
define('DB_NAME', 'photo_management_system'); // Unified database name

// Database Connection Settings
define('DB_CHARSET', 'utf8mb4');
define('DB_COLLATE', 'utf8mb4_unicode_ci');

/**
 * Get PDO Database Connection (for migration and new unified system)
 * @return PDO Database connection instance
 * @throws Exception If connection fails
 */
function get_pdo_connection() {
    try {
        $dsn = "mysql:host=" . DB_HOST . ";dbname=" . DB_NAME . ";charset=" . DB_CHARSET;
        $pdo = new PDO($dsn, DB_USER, DB_PASS, [
            PDO::ATTR_ERRMODE => PDO::ERRMODE_EXCEPTION,
            PDO::ATTR_DEFAULT_FETCH_MODE => PDO::FETCH_ASSOC,
            PDO::ATTR_EMULATE_PREPARES => false,
            PDO::MYSQL_ATTR_INIT_COMMAND => "SET NAMES " . DB_CHARSET
        ]);
        return $pdo;
    } catch (PDOException $e) {
        error_log("Database connection failed: " . $e->getMessage());
        throw new Exception("Database connection failed: " . $e->getMessage());
    }
}

// =============================================
// LEGACY DATABASE CLASS (for backward compatibility)
// =============================================

class DatabaseConfig {
    // Unified database configuration (uses constants defined above)
    private static $unified_config = [
        'host' => DB_HOST,
        'dbname' => DB_NAME,
        'username' => DB_USER,
        'password' => DB_PASS,
        'charset' => DB_CHARSET
    ];

    // Legacy local database configuration (for backward compatibility)
    private static $local_config = [
        'host' => 'localhost',
        'dbname' => 'photo_session_management',
        'username' => 'root',
        'password' => '',
        'charset' => 'utf8mb4'
    ];

    // Legacy external database configuration (for backward compatibility)
    private static $external_config = [
        'host' => 'localhost',
        'dbname' => 'student_registration',
        'username' => 'root',
        'password' => '',
        'charset' => 'utf8mb4'
    ];
    
    private static $unified_connection = null;
    private static $local_connection = null;
    private static $external_connection = null;
    
    /**
     * Get unified database connection (recommended for new code)
     */
    public static function getUnifiedConnection() {
        if (self::$unified_connection === null) {
            try {
                $dsn = "mysql:host=" . self::$unified_config['host'] .
                       ";dbname=" . self::$unified_config['dbname'] .
                       ";charset=" . self::$unified_config['charset'];

                self::$unified_connection = new PDO(
                    $dsn,
                    self::$unified_config['username'],
                    self::$unified_config['password'],
                    [
                        PDO::ATTR_ERRMODE => PDO::ERRMODE_EXCEPTION,
                        PDO::ATTR_DEFAULT_FETCH_MODE => PDO::FETCH_ASSOC,
                        PDO::ATTR_EMULATE_PREPARES => false,
                        PDO::MYSQL_ATTR_INIT_COMMAND => "SET NAMES " . self::$unified_config['charset']
                    ]
                );
            } catch (PDOException $e) {
                error_log("Unified database connection failed: " . $e->getMessage());
                throw new Exception("Database connection failed: " . $e->getMessage());
            }
        }
        return self::$unified_connection;
    }

    /**
     * Get local database connection (legacy - for backward compatibility)
     */
    public static function getLocalConnection() {
        if (self::$local_connection === null) {
            try {
                $dsn = "mysql:host=" . self::$local_config['host'] . 
                       ";dbname=" . self::$local_config['dbname'] . 
                       ";charset=" . self::$local_config['charset'];
                
                self::$local_connection = new PDO(
                    $dsn,
                    self::$local_config['username'],
                    self::$local_config['password'],
                    [
                        PDO::ATTR_ERRMODE => PDO::ERRMODE_EXCEPTION,
                        PDO::ATTR_DEFAULT_FETCH_MODE => PDO::FETCH_ASSOC,
                        PDO::ATTR_EMULATE_PREPARES => false
                    ]
                );
            } catch (PDOException $e) {
                error_log("Local database connection failed: " . $e->getMessage());
                throw new Exception("Database connection failed");
            }
        }
        return self::$local_connection;
    }
    
    /**
     * Get external database connection
     */
    public static function getExternalConnection() {
        if (self::$external_connection === null) {
            // Load external DB config from system settings
            self::loadExternalConfig();
            
            try {
                $dsn = "mysql:host=" . self::$external_config['host'] . 
                       ";dbname=" . self::$external_config['dbname'] . 
                       ";charset=" . self::$external_config['charset'];
                
                self::$external_connection = new PDO(
                    $dsn,
                    self::$external_config['username'],
                    self::$external_config['password'],
                    [
                        PDO::ATTR_ERRMODE => PDO::ERRMODE_EXCEPTION,
                        PDO::ATTR_DEFAULT_FETCH_MODE => PDO::FETCH_ASSOC,
                        PDO::ATTR_EMULATE_PREPARES => false
                    ]
                );
            } catch (PDOException $e) {
                error_log("External database connection failed: " . $e->getMessage());
                throw new Exception("External database connection failed");
            }
        }
        return self::$external_connection;
    }
    
    /**
     * Load external database configuration from system settings
     */
    private static function loadExternalConfig() {
        try {
            // Only try to load from database if local connection exists
            if (self::$local_connection !== null) {
                $local_db = self::getLocalConnection();
                $stmt = $local_db->prepare("
                    SELECT setting_key, setting_value
                    FROM system_settings
                    WHERE setting_key IN ('external_db_host', 'external_db_name', 'external_db_user', 'external_db_pass')
                ");
                $stmt->execute();
                $settings = $stmt->fetchAll();

                foreach ($settings as $setting) {
                    switch ($setting['setting_key']) {
                        case 'external_db_host':
                            self::$external_config['host'] = $setting['setting_value'];
                            break;
                        case 'external_db_name':
                            self::$external_config['dbname'] = $setting['setting_value'];
                            break;
                        case 'external_db_user':
                            self::$external_config['username'] = $setting['setting_value'];
                            break;
                        case 'external_db_pass':
                            self::$external_config['password'] = $setting['setting_value'];
                            break;
                    }
                }
            }
        } catch (Exception $e) {
            error_log("Failed to load external database config: " . $e->getMessage());
            // Use default values if loading fails
        }
    }
    
    /**
     * Close all database connections
     */
    public static function closeConnections() {
        self::$local_connection = null;
        self::$external_connection = null;
    }
}
?>
