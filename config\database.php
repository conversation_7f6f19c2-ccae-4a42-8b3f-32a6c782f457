<?php
/**
 * Database Configuration
 * Unified Photo Management System - Database Configuration
 * Handles connections to both local and external databases
 */

// =============================================
// UNIFIED DATABASE CONFIGURATION CONSTANTS
// =============================================

// Database Configuration Constants (for migration scripts)
define('DB_HOST', 'localhost');        // Database host (usually 'localhost' for XAMPP)
define('DB_USER', 'root');             // Database username (default 'root' for XAMPP)
define('DB_PASS', '');                 // Database password (usually empty for XAMPP)
define('DB_NAME', 'photo_management_system'); // Unified database name

// Database Connection Settings
define('DB_CHARSET', 'utf8mb4');
define('DB_COLLATE', 'utf8mb4_unicode_ci');

/**
 * Get PDO Database Connection (for migration and new unified system)
 * @return PDO Database connection instance
 * @throws Exception If connection fails
 */
function get_pdo_connection() {
    try {
        $dsn = "mysql:host=" . DB_HOST . ";dbname=" . DB_NAME . ";charset=" . DB_CHARSET;
        $pdo = new PDO($dsn, DB_USER, DB_PASS, [
            PDO::ATTR_ERRMODE => PDO::ERRMODE_EXCEPTION,
            PDO::ATTR_DEFAULT_FETCH_MODE => PDO::FETCH_ASSOC,
            PDO::ATTR_EMULATE_PREPARES => false,
            PDO::MYSQL_ATTR_INIT_COMMAND => "SET NAMES " . DB_CHARSET
        ]);
        return $pdo;
    } catch (PDOException $e) {
        error_log("Database connection failed: " . $e->getMessage());
        throw new Exception("Database connection failed: " . $e->getMessage());
    }
}

// =============================================
// LEGACY DATABASE CLASS (for backward compatibility)
// =============================================

class DatabaseConfig {
    // Unified database configuration (uses constants defined above)
    private static $unified_config = [
        'host' => DB_HOST,
        'dbname' => DB_NAME,
        'username' => DB_USER,
        'password' => DB_PASS,
        'charset' => DB_CHARSET
    ];

    // Note: Legacy database configurations removed - now using unified database only
    
    private static $unified_connection = null;
    
    /**
     * Get unified database connection (recommended for new code)
     */
    public static function getUnifiedConnection() {
        if (self::$unified_connection === null) {
            try {
                $dsn = "mysql:host=" . self::$unified_config['host'] .
                       ";dbname=" . self::$unified_config['dbname'] .
                       ";charset=" . self::$unified_config['charset'];

                self::$unified_connection = new PDO(
                    $dsn,
                    self::$unified_config['username'],
                    self::$unified_config['password'],
                    [
                        PDO::ATTR_ERRMODE => PDO::ERRMODE_EXCEPTION,
                        PDO::ATTR_DEFAULT_FETCH_MODE => PDO::FETCH_ASSOC,
                        PDO::ATTR_EMULATE_PREPARES => false,
                        PDO::MYSQL_ATTR_INIT_COMMAND => "SET NAMES " . self::$unified_config['charset']
                    ]
                );
            } catch (PDOException $e) {
                error_log("Unified database connection failed: " . $e->getMessage());
                throw new Exception("Database connection failed: " . $e->getMessage());
            }
        }
        return self::$unified_connection;
    }

    /**
     * Get local database connection (legacy - redirects to unified connection)
     * @deprecated Use getUnifiedConnection() instead
     */
    public static function getLocalConnection() {
        return self::getUnifiedConnection();
    }
    
    /**
     * Get external database connection (legacy - redirects to unified connection)
     * @deprecated Use getUnifiedConnection() instead
     */
    public static function getExternalConnection() {
        return self::getUnifiedConnection();
    }
    
    /**
     * Close database connection
     */
    public static function closeConnections() {
        self::$unified_connection = null;
    }
}
?>
