<?php

class EventService {
    private $pdo;
    
    public function __construct() {
        $this->pdo = get_pdo_connection();
    }
    
    /**
     * Get all active events
     */
    public function getActiveEvents() {
        try {
            $stmt = $this->pdo->query("
                SELECT * FROM events 
                WHERE is_active = 1 
                ORDER BY event_date DESC, event_name
            ");
            return $stmt->fetchAll(PDO::FETCH_ASSOC);
        } catch (Exception $e) {
            error_log("Error getting active events: " . $e->getMessage());
            return [];
        }
    }
    
    /**
     * Get all events
     */
    public function getAllEvents() {
        try {
            $stmt = $this->pdo->query("
                SELECT * FROM events 
                ORDER BY event_date DESC, event_name
            ");
            return $stmt->fetchAll(PDO::FETCH_ASSOC);
        } catch (Exception $e) {
            error_log("Error getting all events: " . $e->getMessage());
            return [];
        }
    }
    
    /**
     * Get event by ID
     */
    public function getEventById($event_id) {
        try {
            $stmt = $this->pdo->prepare("SELECT * FROM events WHERE id = ?");
            $stmt->execute([$event_id]);
            return $stmt->fetch(PDO::FETCH_ASSOC);
        } catch (Exception $e) {
            error_log("Error getting event by ID: " . $e->getMessage());
            return null;
        }
    }
    
    /**
     * Create new event
     */
    public function createEvent($data) {
        try {
            $stmt = $this->pdo->prepare("
                INSERT INTO events (event_name, event_code, event_date, description, is_active, created_at)
                VALUES (?, ?, ?, ?, 1, NOW())
            ");
            
            $stmt->execute([
                $data['event_name'],
                $data['event_code'],
                $data['event_date'] ?: null,
                $data['description'] ?? ''
            ]);
            
            return [
                'success' => true,
                'event_id' => $this->pdo->lastInsertId()
            ];
            
        } catch (Exception $e) {
            error_log("Error creating event: " . $e->getMessage());
            return [
                'success' => false,
                'error' => $e->getMessage()
            ];
        }
    }
    
    /**
     * Update event
     */
    public function updateEvent($event_id, $data) {
        try {
            $stmt = $this->pdo->prepare("
                UPDATE events 
                SET event_name = ?, event_code = ?, event_date = ?, description = ?, updated_at = NOW()
                WHERE id = ?
            ");
            
            $stmt->execute([
                $data['event_name'],
                $data['event_code'],
                $data['event_date'] ?: null,
                $data['description'] ?? '',
                $event_id
            ]);
            
            return $stmt->rowCount() > 0;
            
        } catch (Exception $e) {
            error_log("Error updating event: " . $e->getMessage());
            return false;
        }
    }
    
    /**
     * Toggle event active status
     */
    public function toggleEventStatus($event_id) {
        try {
            $stmt = $this->pdo->prepare("
                UPDATE events 
                SET is_active = NOT is_active, updated_at = NOW()
                WHERE id = ?
            ");
            $stmt->execute([$event_id]);
            return $stmt->rowCount() > 0;
        } catch (Exception $e) {
            error_log("Error toggling event status: " . $e->getMessage());
            return false;
        }
    }
    
    /**
     * Get photo types with pricing for event
     */
    public function getEventPhotoTypesWithPricing($event_id) {
        try {
            $stmt = $this->pdo->prepare("
                SELECT pt.*, epp.price, epp.currency, epp.is_active as pricing_active
                FROM photo_types pt
                LEFT JOIN event_photo_pricing epp ON pt.id = epp.photo_type_id AND epp.event_id = ?
                WHERE pt.is_active = 1
                ORDER BY pt.type_name
            ");
            $stmt->execute([$event_id]);
            return $stmt->fetchAll(PDO::FETCH_ASSOC);
        } catch (Exception $e) {
            error_log("Error getting event photo types with pricing: " . $e->getMessage());
            return [];
        }
    }
    
    /**
     * Update event photo pricing
     */
    public function updateEventPhotoPricing($event_id, $pricing_data) {
        try {
            $this->pdo->beginTransaction();
            
            foreach ($pricing_data as $photo_type_id => $price) {
                $price = (float)$price;
                
                $stmt = $this->pdo->prepare("
                    INSERT INTO event_photo_pricing (event_id, photo_type_id, price, currency, updated_at)
                    VALUES (?, ?, ?, 'LKR', NOW())
                    ON DUPLICATE KEY UPDATE price = VALUES(price), updated_at = NOW()
                ");
                $stmt->execute([$event_id, $photo_type_id, $price]);
            }
            
            $this->pdo->commit();
            return true;
            
        } catch (Exception $e) {
            $this->pdo->rollBack();
            error_log("Error updating event photo pricing: " . $e->getMessage());
            return false;
        }
    }
    
    /**
     * Get event statistics
     */
    public function getEventStatistics($event_id) {
        try {
            $stats = [];
            
            // Total registrations
            $stmt = $this->pdo->prepare("SELECT COUNT(*) FROM students WHERE event_id = ?");
            $stmt->execute([$event_id]);
            $stats['total_registrations'] = $stmt->fetchColumn();
            
            // By status
            $stmt = $this->pdo->prepare("
                SELECT registration_status, COUNT(*) as count 
                FROM students 
                WHERE event_id = ?
                GROUP BY registration_status
            ");
            $stmt->execute([$event_id]);
            $status_counts = $stmt->fetchAll(PDO::FETCH_ASSOC);
            
            foreach ($status_counts as $status) {
                $stats['by_status'][$status['registration_status']] = $status['count'];
            }
            
            // Revenue
            $stmt = $this->pdo->prepare("
                SELECT SUM(total_amount) as total_revenue 
                FROM students 
                WHERE event_id = ? AND payment_status = 'paid'
            ");
            $stmt->execute([$event_id]);
            $stats['total_revenue'] = $stmt->fetchColumn() ?: 0;
            
            return $stats;
            
        } catch (Exception $e) {
            error_log("Error getting event statistics: " . $e->getMessage());
            return [];
        }
    }
}

class PhotoTypeService {
    private $pdo;
    
    public function __construct() {
        $this->pdo = get_pdo_connection();
    }
    
    /**
     * Get all active photo types
     */
    public function getActivePhotoTypes() {
        try {
            $stmt = $this->pdo->query("
                SELECT * FROM photo_types 
                WHERE is_active = 1 
                ORDER BY type_name
            ");
            return $stmt->fetchAll(PDO::FETCH_ASSOC);
        } catch (Exception $e) {
            error_log("Error getting active photo types: " . $e->getMessage());
            return [];
        }
    }
    
    /**
     * Get all photo types
     */
    public function getAllPhotoTypes() {
        try {
            $stmt = $this->pdo->query("
                SELECT * FROM photo_types 
                ORDER BY type_name
            ");
            return $stmt->fetchAll(PDO::FETCH_ASSOC);
        } catch (Exception $e) {
            error_log("Error getting all photo types: " . $e->getMessage());
            return [];
        }
    }
    
    /**
     * Create new photo type
     */
    public function createPhotoType($data) {
        try {
            $stmt = $this->pdo->prepare("
                INSERT INTO photo_types (type_name, type_code, description, is_active, created_at)
                VALUES (?, ?, ?, 1, NOW())
            ");
            
            $stmt->execute([
                $data['type_name'],
                $data['type_code'],
                $data['description'] ?? ''
            ]);
            
            return [
                'success' => true,
                'photo_type_id' => $this->pdo->lastInsertId()
            ];
            
        } catch (Exception $e) {
            error_log("Error creating photo type: " . $e->getMessage());
            return [
                'success' => false,
                'error' => $e->getMessage()
            ];
        }
    }
    
    /**
     * Update photo type
     */
    public function updatePhotoType($photo_type_id, $data) {
        try {
            $stmt = $this->pdo->prepare("
                UPDATE photo_types 
                SET type_name = ?, type_code = ?, description = ?
                WHERE id = ?
            ");
            
            $stmt->execute([
                $data['type_name'],
                $data['type_code'],
                $data['description'] ?? '',
                $photo_type_id
            ]);
            
            return $stmt->rowCount() > 0;
            
        } catch (Exception $e) {
            error_log("Error updating photo type: " . $e->getMessage());
            return false;
        }
    }
    
    /**
     * Toggle photo type active status
     */
    public function togglePhotoTypeStatus($photo_type_id) {
        try {
            $stmt = $this->pdo->prepare("
                UPDATE photo_types 
                SET is_active = NOT is_active
                WHERE id = ?
            ");
            $stmt->execute([$photo_type_id]);
            return $stmt->rowCount() > 0;
        } catch (Exception $e) {
            error_log("Error toggling photo type status: " . $e->getMessage());
            return false;
        }
    }
}

class DeliveryMethodService {
    private $pdo;
    
    public function __construct() {
        $this->pdo = get_pdo_connection();
    }
    
    /**
     * Get all active delivery methods
     */
    public function getActiveDeliveryMethods() {
        try {
            $stmt = $this->pdo->query("
                SELECT * FROM delivery_methods 
                WHERE is_active = 1 
                ORDER BY additional_cost, method_name
            ");
            return $stmt->fetchAll(PDO::FETCH_ASSOC);
        } catch (Exception $e) {
            error_log("Error getting active delivery methods: " . $e->getMessage());
            return [];
        }
    }
    
    /**
     * Get delivery method by ID
     */
    public function getDeliveryMethodById($method_id) {
        try {
            $stmt = $this->pdo->prepare("SELECT * FROM delivery_methods WHERE id = ?");
            $stmt->execute([$method_id]);
            return $stmt->fetch(PDO::FETCH_ASSOC);
        } catch (Exception $e) {
            error_log("Error getting delivery method by ID: " . $e->getMessage());
            return null;
        }
    }
}
