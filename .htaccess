# Photo Management System - URL Rewrite Rules

# Enable URL rewriting
RewriteEngine On

# Redirect old admin URLs to new app structure
# This handles requests like /admin/dashboard.php -> /app/dashboard.php
RewriteRule ^admin/(.*)$ app/$1 [R=301,L]

# Security: Prevent direct access to config files
<Files "config.php">
    Order Allow,Deny
    <PERSON> from all
</Files>

# Security: Prevent direct access to class files
<FilesMatch "\.(inc|class)$">
    Order Allow,Deny
    Deny from all
</FilesMatch>

# Security: Prevent access to sensitive directories
RedirectMatch 404 /\.git
RedirectMatch 404 /\.env
RedirectMatch 404 /composer\.json
RedirectMatch 404 /composer\.lock

# Enable compression for better performance
<IfModule mod_deflate.c>
    AddOutputFilterByType DEFLATE text/plain
    AddOutputFilterByType DEFLATE text/html
    AddOutputFilterByType DEFLATE text/xml
    AddOutputFilterByType DEFLATE text/css
    AddOutputFilterByType DEFLATE application/xml
    AddOutputFilterByType DEFLATE application/xhtml+xml
    AddOutputFilterByType DEFLATE application/rss+xml
    AddOutputFilterByType DEFLATE application/javascript
    AddOutputFilterByType DEFLATE application/x-javascript
</IfModule>

# Set cache headers for static assets
<IfModule mod_expires.c>
    ExpiresActive On
    ExpiresByType image/jpg "access plus 1 month"
    ExpiresByType image/jpeg "access plus 1 month"
    ExpiresByType image/gif "access plus 1 month"
    ExpiresByType image/png "access plus 1 month"
    ExpiresByType text/css "access plus 1 month"
    ExpiresByType application/pdf "access plus 1 month"
    ExpiresByType application/javascript "access plus 1 month"
    ExpiresByType application/x-javascript "access plus 1 month"
    ExpiresByType application/x-shockwave-flash "access plus 1 month"
    ExpiresByType image/x-icon "access plus 1 year"
    ExpiresDefault "access plus 2 days"
</IfModule>

# Custom error pages (optional)
# ErrorDocument 404 /error-pages/404.html
# ErrorDocument 500 /error-pages/500.html
