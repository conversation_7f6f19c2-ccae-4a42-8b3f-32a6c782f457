<?php
/**
 * Quick Sessions Fix
 * Manually add sessions functionality with proper error handling
 */

// Security check
$allowed_ips = ['127.0.0.1', '::1', 'localhost'];
if (!in_array($_SERVER['REMOTE_ADDR'], $allowed_ips) && !in_array($_SERVER['HTTP_HOST'], ['localhost', '127.0.0.1'])) {
    die('Access denied. Update can only be run from localhost.');
}

// Include configuration
require_once '../config/database.php';

$update_log = [];
$errors = [];

function log_message($message, $type = 'info') {
    global $update_log;
    $update_log[] = [
        'time' => date('Y-m-d H:i:s'),
        'type' => $type,
        'message' => $message
    ];
    
    if ($type === 'error') {
        global $errors;
        $errors[] = $message;
    }
}

// Start update process
if ($_SERVER['REQUEST_METHOD'] === 'POST' && isset($_POST['run_fix'])) {
    log_message("Starting quick sessions fix...", 'info');
    
    try {
        $pdo = DatabaseConfig::getUnifiedConnection();
        log_message("Database connection established", 'success');
        
        // Step 1: Create event_sessions table
        log_message("Step 1: Creating event_sessions table...", 'info');
        try {
            $pdo->exec("
                CREATE TABLE IF NOT EXISTS `event_sessions` (
                  `id` int(11) NOT NULL AUTO_INCREMENT,
                  `event_id` int(11) NOT NULL,
                  `session_name` varchar(100) NOT NULL,
                  `session_code` varchar(20) NOT NULL,
                  `session_date` date DEFAULT NULL,
                  `session_time` time DEFAULT NULL,
                  `venue` varchar(200) DEFAULT NULL,
                  `max_capacity` int(11) DEFAULT NULL,
                  `description` text,
                  `is_active` tinyint(1) NOT NULL DEFAULT 1,
                  `created_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP,
                  `updated_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
                  PRIMARY KEY (`id`),
                  UNIQUE KEY `unique_event_session_code` (`event_id`, `session_code`),
                  KEY `idx_event_id` (`event_id`),
                  KEY `idx_session_code` (`session_code`),
                  KEY `idx_is_active` (`is_active`),
                  CONSTRAINT `fk_event_sessions_event` FOREIGN KEY (`event_id`) REFERENCES `events` (`id`) ON DELETE CASCADE
                ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci
            ");
            log_message("✅ event_sessions table created successfully", 'success');
        } catch (PDOException $e) {
            if (strpos($e->getMessage(), 'already exists') !== false) {
                log_message("⚠️ event_sessions table already exists", 'warning');
            } else {
                throw $e;
            }
        }
        
        // Step 2: Check if session_id column exists
        log_message("Step 2: Checking session_id column...", 'info');
        $stmt = $pdo->prepare("
            SELECT COUNT(*) FROM INFORMATION_SCHEMA.COLUMNS 
            WHERE table_name = 'students' 
            AND table_schema = DATABASE() 
            AND column_name = 'session_id'
        ");
        $stmt->execute();
        $column_exists = $stmt->fetchColumn() > 0;
        
        if (!$column_exists) {
            log_message("Adding session_id column to students table...", 'info');
            $pdo->exec("ALTER TABLE `students` ADD COLUMN `session_id` int(11) DEFAULT NULL AFTER `event_id`");
            log_message("✅ session_id column added successfully", 'success');
        } else {
            log_message("⚠️ session_id column already exists", 'warning');
        }
        
        // Step 3: Add index if it doesn't exist
        log_message("Step 3: Adding index for session_id...", 'info');
        try {
            $pdo->exec("ALTER TABLE `students` ADD KEY `fk_student_session` (`session_id`)");
            log_message("✅ Index added successfully", 'success');
        } catch (PDOException $e) {
            if (strpos($e->getMessage(), 'Duplicate key name') !== false) {
                log_message("⚠️ Index already exists", 'warning');
            } else {
                throw $e;
            }
        }
        
        // Step 4: Add foreign key constraint if it doesn't exist
        log_message("Step 4: Adding foreign key constraint...", 'info');
        try {
            $pdo->exec("ALTER TABLE `students` ADD CONSTRAINT `fk_student_session` FOREIGN KEY (`session_id`) REFERENCES `event_sessions` (`id`) ON DELETE SET NULL");
            log_message("✅ Foreign key constraint added successfully", 'success');
        } catch (PDOException $e) {
            if (strpos($e->getMessage(), 'already exists') !== false || strpos($e->getMessage(), 'Duplicate') !== false) {
                log_message("⚠️ Foreign key constraint already exists", 'warning');
            } else {
                throw $e;
            }
        }
        
        // Step 5: Insert sample data
        log_message("Step 5: Inserting sample session data...", 'info');
        $sample_sessions = [
            [1, 'Morning Session - Faculty of Engineering', 'CONV2024_ENG_AM', '2024-12-15', '09:00:00', 'Main Auditorium', 200, 'Engineering faculty convocation - morning session'],
            [1, 'Afternoon Session - Faculty of Science', 'CONV2024_SCI_PM', '2024-12-15', '14:00:00', 'Main Auditorium', 200, 'Science faculty convocation - afternoon session'],
            [1, 'Evening Session - Faculty of Arts', 'CONV2024_ART_EV', '2024-12-15', '18:00:00', 'Main Auditorium', 200, 'Arts faculty convocation - evening session'],
            [2, 'Undergraduate Ceremony', 'GRAD2024_UG', '2024-11-30', '10:00:00', 'University Hall', 300, 'Undergraduate graduation ceremony'],
            [2, 'Postgraduate Ceremony', 'GRAD2024_PG', '2024-11-30', '15:00:00', 'University Hall', 150, 'Postgraduate graduation ceremony'],
            [3, 'New Students - Batch A', 'ORIENT2024_A', '2024-02-15', '09:00:00', 'Conference Hall', 100, 'Orientation for new students - Batch A'],
            [3, 'New Students - Batch B', 'ORIENT2024_B', '2024-02-15', '14:00:00', 'Conference Hall', 100, 'Orientation for new students - Batch B']
        ];
        
        $stmt = $pdo->prepare("
            INSERT IGNORE INTO `event_sessions` 
            (`event_id`, `session_name`, `session_code`, `session_date`, `session_time`, `venue`, `max_capacity`, `description`) 
            VALUES (?, ?, ?, ?, ?, ?, ?, ?)
        ");
        
        $inserted_count = 0;
        foreach ($sample_sessions as $session) {
            $stmt->execute($session);
            if ($stmt->rowCount() > 0) {
                $inserted_count++;
            }
        }
        
        log_message("✅ Inserted {$inserted_count} sample sessions", 'success');
        
        // Final verification
        log_message("Step 6: Verifying installation...", 'info');
        $stmt = $pdo->query("SELECT COUNT(*) FROM event_sessions");
        $session_count = $stmt->fetchColumn();
        
        $stmt = $pdo->query("SHOW COLUMNS FROM students LIKE 'session_id'");
        $column_exists = $stmt->rowCount() > 0;
        
        if ($session_count > 0 && $column_exists) {
            log_message("🎉 Sessions functionality successfully installed!", 'success');
            log_message("📊 Total sessions in database: {$session_count}", 'info');
        } else {
            log_message("❌ Installation verification failed", 'error');
        }
        
    } catch (Exception $e) {
        log_message("Update failed: " . $e->getMessage(), 'error');
    }
}
?>
<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Quick Sessions Fix - Photo Management System</title>
    <link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/bulma@0.9.4/css/bulma.min.css">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css">
    <style>
        .fix-header {
            background: linear-gradient(135deg, #ff6b6b 0%, #ee5a24 100%);
            color: white;
            padding: 2rem;
            border-radius: 8px;
            margin-bottom: 2rem;
        }
        .log-entry {
            padding: 0.5rem;
            margin-bottom: 0.25rem;
            border-radius: 4px;
            font-family: monospace;
            font-size: 0.875rem;
        }
        .log-info { background-color: #f0f8ff; }
        .log-success { background-color: #f0fff0; color: #006400; }
        .log-warning { background-color: #fff8dc; color: #b8860b; }
        .log-error { background-color: #ffe4e1; color: #dc143c; }
    </style>
</head>
<body>
    <div class="container mt-4">
        <!-- Header -->
        <div class="fix-header">
            <h1 class="title is-2 has-text-white">
                <i class="fas fa-tools mr-3"></i>
                Quick Sessions Fix
            </h1>
            <p class="subtitle is-4 has-text-white">
                Fix the sessions table installation with proper error handling
            </p>
        </div>

        <!-- Update Status -->
        <?php if (!empty($update_log)): ?>
            <div class="box">
                <h2 class="title is-5">
                    <i class="fas fa-list mr-2"></i>
                    Fix Log
                </h2>
                
                <?php if (empty($errors)): ?>
                    <div class="notification is-success">
                        <i class="fas fa-check-circle mr-2"></i>
                        Sessions fix completed successfully!
                    </div>
                <?php else: ?>
                    <div class="notification is-danger">
                        <i class="fas fa-exclamation-circle mr-2"></i>
                        Fix completed with <?php echo count($errors); ?> error(s).
                    </div>
                <?php endif; ?>
                
                <div style="max-height: 400px; overflow-y: auto; border: 1px solid #ddd; padding: 1rem;">
                    <?php foreach ($update_log as $entry): ?>
                        <div class="log-entry log-<?php echo $entry['type']; ?>">
                            <strong>[<?php echo $entry['time']; ?>]</strong> 
                            <?php echo htmlspecialchars($entry['message']); ?>
                        </div>
                    <?php endforeach; ?>
                </div>
            </div>
        <?php endif; ?>

        <!-- Fix Form -->
        <?php if (empty($update_log) || !empty($errors)): ?>
            <div class="box">
                <h2 class="title is-5">
                    <i class="fas fa-wrench mr-2"></i>
                    Run Quick Fix
                </h2>
                
                <div class="content">
                    <p>This fix will safely add the sessions functionality by:</p>
                    <ol>
                        <li>✅ Creating the <code>event_sessions</code> table</li>
                        <li>✅ Adding <code>session_id</code> column to students table (if not exists)</li>
                        <li>✅ Adding proper indexes and foreign keys</li>
                        <li>✅ Inserting sample session data</li>
                        <li>✅ Verifying the installation</li>
                    </ol>
                    
                    <div class="notification is-info">
                        <strong>Safe to run:</strong> This script checks for existing structures and won't break anything.
                    </div>
                </div>
                
                <form method="POST" action="">
                    <div class="field">
                        <div class="control">
                            <button class="button is-danger is-large" type="submit" name="run_fix">
                                <i class="fas fa-tools mr-2"></i>
                                Run Quick Fix
                            </button>
                        </div>
                    </div>
                </form>
            </div>
        <?php endif; ?>

        <!-- Success Actions -->
        <?php if (!empty($update_log) && empty($errors)): ?>
            <div class="box">
                <h2 class="title is-5">
                    <i class="fas fa-check-circle mr-2"></i>
                    Fix Complete!
                </h2>
                
                <div class="content">
                    <p>The sessions functionality is now working! You can:</p>
                    <ul>
                        <li><strong>Test Registration:</strong> Try the student registration with session selection</li>
                        <li><strong>Manage Sessions:</strong> Add/edit sessions through the admin panel</li>
                        <li><strong>View Data:</strong> Check the sessions in your database</li>
                    </ul>
                </div>
                
                <div class="buttons">
                    <a href="../student/registration.php" class="button is-primary">
                        <i class="fas fa-user-plus mr-2"></i>
                        Test Registration
                    </a>
                    <a href="../admin/dashboard.php" class="button is-info">
                        <i class="fas fa-tachometer-alt mr-2"></i>
                        Admin Dashboard
                    </a>
                </div>
            </div>
        <?php endif; ?>

        <!-- Footer -->
        <div class="has-text-centered mt-6 mb-4">
            <p class="has-text-grey">
                <i class="fas fa-shield-alt mr-2"></i>
                Photo Management System - Quick Sessions Fix Tool
            </p>
        </div>
    </div>
</body>
</html>
